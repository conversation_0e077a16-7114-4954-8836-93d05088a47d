import React, { useState, useEffect } from 'react';
import Button from '../../../../components/Field/Button';
import PopUp from '../../../../components/PopUp/PopUp';
import Toastify from '../../../../components/PopUp/Toastify';
import SearchableDropdown from '../../../../components/Field/SearchableDropdown';
import Input from '../../../../components/Field/Input';
import { useDispatch, useSelector } from 'react-redux';
import {
  setAllSubTopicsData,
  setTopicData,
  useCreateSubTopicServiceMutation,
  useLazyGetAllSubTopicsServiceQuery,
  useLazyGetTopicByIdsServiceQuery
} from './processSelector.slice';
import Table from '../../../../components/Layout/Table';
import { SubTopicMappingHeader } from './TableHeaderData';

const SubTopicMapping = () => {
  const [popUp, setPopUp] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState({ id: '', name: '' });
  const [selectedSubject, setSelectedSubject] = useState({ id: '', name: '' });
  const [selectedTopic, setSelectedTopic] = useState({ id: '', name: '' });
  const [subTopic, setSubTopic] = useState('');
  const [res, setRes] = useState(null);

  const [getTopicDataService] = useLazyGetTopicByIdsServiceQuery();
  const [createSubTopicService] = useCreateSubTopicServiceMutation();
  const [getAllSubTopicsService] = useLazyGetAllSubTopicsServiceQuery();
  const dispatch = useDispatch();

  const courseData = useSelector((state) => state.processSelector.courseData);
  const subjectData = useSelector((state) => state.processSelector.subjectData);
  const topicData = useSelector((state) => state.processSelector.topicsData);
  const allSubTopicsData = useSelector((state) => state.processSelector.allSubTopicsData);
  console.log(topicData);

  useEffect(() => {
    fetchAllSubTopicsService();
  }, []);

  const fetchAllSubTopicsService = async () => {
    try {
      const res = await getAllSubTopicsService().unwrap();
      dispatch(setAllSubTopicsData(res));
      console.log('All Sub Topics Data:', res);
    } catch (error) {
      setRes(error);
    }
  };

  // Auto-fetch topics when course and subject are selected
  useEffect(() => {
    const fetchTopicsByIdsService = async () => {
      try {
        const res = await getTopicDataService({
          course_id: selectedCourse.id,
          subject_id: selectedSubject.id
        }).unwrap();

        dispatch(setTopicData(res));
      } catch (error) {
        setRes(error);
      }
    };

    if (selectedCourse?.id && selectedSubject?.id) {
      fetchTopicsByIdsService();
    }
  }, [selectedCourse, selectedSubject, getTopicDataService, dispatch]);

  const handleChangeTopics = (e) => {
    setSelectedTopic(e);
  };

  const handleCreateSubTopic = async () => {
    try {
      const res = await createSubTopicService({
        course_id: selectedCourse.id,
        course_name: selectedCourse.name,
        subject_id: selectedSubject.id,
        subject_name: selectedSubject.name,
        topic_id: selectedTopic.id,
        topic_name: selectedTopic.name,
        sub_topic_name: subTopic
      }).unwrap();
      fetchAllSubTopicsService()
      setRes(res);
    } catch (error) {
      setRes(error);
    } finally {
      setPopUp(false);
      setSelectedCourse({ id: '', name: '' });
      setSelectedSubject({ id: '', name: '' });
      setSelectedTopic({ id: '', name: '' });
      setSubTopic('');
    }
  };

  return (
    <div>
      <Toastify res={res} resClear={() => setRes(null)} />
      {popUp && (
        <PopUp
          title={'Create Sub Topic Mapping'}
          onClose={() => {
            setPopUp(false);
            setSelectedCourse({ id: '', name: '' });
            setSelectedSubject({ id: '', name: '' });
            setSelectedTopic({ id: '', name: '' });
            setSubTopic('');
          }}
          post={handleCreateSubTopic}>
          <div className="grid grid-cols-2 gap-2 mb-4">
            <SearchableDropdown
              label="Course Name"
              value={selectedCourse?.id}
              placeholder="Select/Search the Course Name"
              options={courseData}
              onChange={(e) => {
                setSelectedCourse(e);
                setSelectedSubject({ id: '', name: '' });
                setSelectedTopic({ id: '', name: '' });
              }}
              required
            />

            <SearchableDropdown
              label="Subject Name"
              value={selectedSubject?.id}
              placeholder="Select/Search the Subject Name"
              options={subjectData}
              onChange={(e) => {
                setSelectedSubject(e);
                setSelectedTopic({ id: '', name: '' });
              }}
              disabled={!selectedCourse?.id}
              required
            />

            <SearchableDropdown
              label="Topic Name"
              value={selectedTopic?.id}
              placeholder="Select/Search the Topic Name"
              options={topicData}
              onChange={handleChangeTopics}
              disabled={!selectedCourse?.id || !selectedSubject?.id}
              required
            />
          </div>

          <Input
            label="Sub Topic Name"
            value={subTopic}
            onChange={(e) => setSubTopic(e.target.value)}
            className="border rounded"
            disabled={!selectedCourse?.id || !selectedSubject?.id}
            required
          />
        </PopUp>
      )}

      {/* <section className="flex justify-between items-center">
        <div></div>
        <div>
          <Button name="Sub-Topic Mapping" className="" onClick={() => setPopUp(true)} />
        </div>
      </section> */}
      <Table
        title="Sub Topic Mapping"
        onAddNew={() => setPopUp(true)}
        buttonName="Create Sub Topic Mapping"
        header={SubTopicMappingHeader}
        data={allSubTopicsData}
        searchBy={['course_name', 'subject_name', 'topic_name','sub_topic_name']}
        searchPlaceholder="Search by Course, Subject, Topic or Sub Topic Name"
      />
    </div>
  );
};

export default SubTopicMapping;
