// import { createSlice } from '@reduxjs/toolkit';
// import { CreatePhysicsTestApi } from '../../../../redux/api/api';

// const initialState = {
//   physicsTestData: null
// };

// export const createPhysicsTestApiSlice = CreatePhysicsTestApi.injectEndpoints({
//   endpoints: (builder) => ({
//     createPhysicsTestService: builder.mutation({
//       query: (body) => ({
//         url: '/generate-paper-physics',
//         method: 'POST',
//         body,
//         responseHandler: async (res) => res.json()
//       }),
//       transformResponse: (response) => {
//         console.log('Create Physics Test Response:', response);
//         return response;
//       },
//       transformErrorResponse: ({ originalStatus, status, data }) => ({
//         status: originalStatus ?? status,
//         data
//       }),
//       providesTags: ['CreatePhysicsTest']
//     })
//   })
// });

// const physicsTestSlice = createSlice({
//   name: 'physicsTest',
//   initialState,
//   reducers: {
//     setPhysicsTestData: (state, action) => {
//       state.physicsTestData = action.payload;
//     }
//   }
// });

// export const { setPhysicsTestData } = physicsTestSlice.actions;
// export default physicsTestSlice.reducer;

// export const { useCreatePhysicsTestServiceMutation } = createPhysicsTestApiSlice;