import { messaging, getToken, onMessage } from "./firebase";

export const requestNotificationPermission = async () => {
  try {
    const permission = await Notification.requestPermission();
    if (permission === "granted") {
      console.log("Notification permission granted.");
      const token = await getToken(messaging, {
        vapidKey: "BOO2JKu9FrvCcwWBskI8FZCY62ytH46Uq5cbk3n8PLzR9Ts5GZY6dB_ea03Ly5AFETsQlmbWMoi2vhvKbBvKqhU", // Your VAPID key from Firebase Console
      });
      console.log("FCM Token:", token);
      return token;
    } else {
      console.log("Notification permission denied.");
      return null;
    }
  } catch (error) {
    console.error("Error getting FCM token:", error);
    return null;
  }
};

export const onMessageListener = () =>
  new Promise((resolve) => {
    onMessage(messaging, (payload) => {
      console.log("Foreground Message:", payload);
      resolve(payload);
    });
  });