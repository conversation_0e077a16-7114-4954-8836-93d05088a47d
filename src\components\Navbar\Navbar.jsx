import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import SideBar from './SideBar';
import TopBar from './TopBar';
import ChatBotSystem from '../../pages/screens/studentPanel/chatSupport/ChatBotSystem';
import { motion } from 'framer-motion';
import { ArrowLeftSquare } from 'lucide-react';

const Navbar = ({ children }) => {
  const [isSidebarOpen, setSidebarOpen] = useState(false);
  const [showNav, setShowNav] = useState(true);
  const userRole = sessionStorage.getItem('role');
  const location = useLocation();

  const hiddenNavPaths = [
    '/sasthra/student/mock-test-simulation',
    '/sasthra/student/create-your-own-test',
    '/sasthra/student/ai-tutor',
    '/sasthra/teacher/live-streaming',
    '/sasthra/faculty/live-viewer',
    '/sasthra/student/problem-solver',
    '/sasthra/student/learn-practically',
    '/sasthra/student/student-community'
  ];

  const isHiddenNavPage = hiddenNavPaths.includes(location.pathname);

  // Reset the "Back" button visibility when navigating to these special pages
  useEffect(() => {
    if (isHiddenNavPage) {
      setShowNav(true);
    }
  }, [location.pathname, isHiddenNavPage]);

  const toggleSidebar = () => {
    setSidebarOpen((prev) => !prev);
  };

  // Close sidebar on link click only on mobile devices
  const handleLinkClick = () => {
    if (window.innerWidth < 768) {
      // md breakpoint in Tailwind is 768px
      setSidebarOpen(false);
    }
  };

  const isNavVisible = !isHiddenNavPage || !showNav;

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {isNavVisible && <TopBar onToggleSidebar={toggleSidebar} />}

      <div className="flex flex-grow overflow-hidden relative">
        {/* Backdrop for mobile, hidden on desktop */}
        {isSidebarOpen && (
          <div
            onClick={toggleSidebar}
            className="fixed inset-0 bg-black/60 z-30 md:hidden"
            aria-hidden="true"></div>
        )}

        {/* The Sidebar itself */}
        {isNavVisible && <SideBar isOpen={isSidebarOpen} onLinkClick={handleLinkClick} />}

        {/* Main Content Area */}
        <main className={`flex-grow overflow-y-auto p-4 transition-all duration-300 ease-in-out`}>
          {isHiddenNavPage && showNav && (
            <motion.button
              onClick={() => setShowNav(false)}
              className="p-2 group top-0 left-0 sticky flex items-center z-"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}>
              <ArrowLeftSquare size={24} className="text-blue-600" />
              <span className="ml-2 text-sm font-medium text-blue-600">Back</span>
            </motion.button>
          )}

          {children}
        </main>

        {userRole === 'student' && isNavVisible && (
          <div className="fixed bottom-4 right-4 z-20">
            <ChatBotSystem />
          </div>
        )}
      </div>
    </div>
  );
};

export default Navbar;
