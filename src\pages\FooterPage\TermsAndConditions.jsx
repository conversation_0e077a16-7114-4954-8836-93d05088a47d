import React, { lazy, Suspense, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import {
  FileText,
  Lock,
  Users,
  Shield,
  Mail,
  ChevronDown,
  ChevronUp,
  BookOpen,
  Award,
  Monitor,
  Smartphone,
  CreditCard,
  Phone
} from 'lucide-react';
import Logo from '../../assets/sasthra_logo.png';

// Lazy load components
const InteractiveParticles = lazy(() => import('./InteractiveParticles'));
const ScrollProgress = lazy(() => import('./ScrollProgress'));

const TermsAndConditions = () => {
  const [activeSection, setActiveSection] = useState(null);
  const [isNavOpen, setIsNavOpen] = useState(false);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: [0.22, 1, 0.36, 1]
      }
    }
  };

  const sections = [
    {
      id: 1,
      title: 'Introduction',
      icon: <FileText className="w-5 h-5" />,
      content: (
        <p className="text-gray-700 leading-relaxed">
          These Terms and Conditions ("Terms") govern your use of the Sasthra platform, including
          our website (
          <a
            href="https://sasthra.in"
            className="text-primary hover:text-primary-dark transition-colors duration-300 underline">
            sasthra.in
          </a>
          ), mobile applications, and any related services or products ("Services") provided by{' '}
          <span className="font-semibold text-accent">DataSpark AI Solutions</span> ("Sasthra",
          "We", "Us", or "Our"). By accessing or using our Services, you agree to be bound by these
          Terms, which constitute an electronic record under the Information Technology Act, 2000,
          and the Information Technology (Intermediaries Guidelines) Rules, 2011, as amended. If you
          do not agree with these Terms, please refrain from using our Services.
        </p>
      )
    },
    {
      id: 2,
      title: 'Eligibility',
      icon: <Users className="w-5 h-5" />,
      content: (
        <p className="text-gray-700 leading-relaxed">
          To use our Services, you must be at least 13 years of age and have the legal capacity to
          enter into a contract under applicable laws in India. If you are under 18, you must have
          parental or guardian consent to use the Services. By registering, you represent that all
          information provided is accurate and truthful. Sasthra reserves the right to suspend or
          terminate your account if you provide false or misleading information or engage in
          misconduct.
        </p>
      )
    },
    {
      id: 3,
      title: 'Services',
      icon: <Shield className="w-5 h-5" />,
      content: (
        <div className="space-y-4">
          <p className="text-gray-700 leading-relaxed">
            Sasthra provides AI-driven educational content, including live and recorded classes,
            study materials, mock tests, and personalized mentoring for academic and competitive
            exam preparation. Our Services are available through our website, mobile applications,
            or other authorized platforms. All content is provided on an "as-is" basis, and we do
            not guarantee the accuracy, completeness, or suitability of the information for your
            specific needs.
          </p>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
            <div className="bg-gradient-to-br from-primary/10 to-accent/10 p-4 rounded-xl flex flex-col items-center">
              <BookOpen className="w-6 h-6 text-accent mb-2" />
              <span className="text-sm font-medium text-center">Study Materials</span>
            </div>
            <div className="bg-gradient-to-br from-primary/10 to-accent/10 p-4 rounded-xl flex flex-col items-center">
              <Monitor className="w-6 h-6 text-accent mb-2" />
              <span className="text-sm font-medium text-center">Live Classes</span>
            </div>
            <div className="bg-gradient-to-br from-primary/10 to-accent/10 p-4 rounded-xl flex flex-col items-center">
              <Award className="w-6 h-6 text-accent mb-2" />
              <span className="text-sm font-medium text-center">Mock Tests</span>
            </div>
            <div className="bg-gradient-to-br from-primary/10 to-accent/10 p-4 rounded-xl flex flex-col items-center">
              <Smartphone className="w-6 h-6 text-accent mb-2" />
              <span className="text-sm font-medium text-center">Mobile App</span>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 4,
      title: 'User Obligations',
      icon: <Lock className="w-5 h-5" />,
      content: (
        <div className="space-y-4">
          <p className="text-gray-700 leading-relaxed">You agree to:</p>
          <ul className="space-y-3">
            {[
              'Use the Services in compliance with all applicable laws, including the Digital Personal Data Protection Act, 2023.',
              'Not share your login credentials or allow unauthorized access to your account.',
              'Not reproduce, distribute, modify, or create derivative works from our content without prior written consent.',
              'Not engage in any activity that disrupts or interferes with the functionality of our Services, including hacking, reverse-engineering, or unauthorized data access.'
            ].map((item, index) => (
              <motion.li
                key={index}
                className="flex items-start"
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}>
                <span className="inline-block w-2 h-2 bg-accent rounded-full mt-2 mr-2"></span>
                <span>{item}</span>
              </motion.li>
            ))}
          </ul>
          <p className="text-gray-700 leading-relaxed pt-2">
            Sasthra reserves the right to disqualify or suspend users who violate these obligations.
          </p>
        </div>
      )
    },
    {
      id: 5,
      title: 'Payment Terms',
      icon: <CreditCard className="w-5 h-5" />,
      content: (
        <div className="space-y-4">
          <p className="text-gray-700 leading-relaxed">
            Certain Services require payment of fees. All fees are non-refundable unless otherwise
            specified in our refund policy. You agree to provide accurate payment information and
            authorize Sasthra to charge the applicable fees.
          </p>
          <div className="bg-gradient-to-r from-primary/5 to-accent/5 p-4 rounded-lg border border-primary/10">
            <p className="text-gray-700 leading-relaxed">
              In case of payment disputes, contact us at{' '}
              <a
                href="mailto:<EMAIL>"
                className="text-primary hover:text-primary-dark transition-colors duration-300 underline">
                <EMAIL>
              </a>
              . Sasthra is not responsible for errors in payment processing by third-party gateways.
            </p>
          </div>
        </div>
      )
    },
    {
      id: 6,
      title: 'Intellectual Property',
      icon: <Lock className="w-5 h-5" />,
      content: (
        <p className="text-gray-700 leading-relaxed">
          All content on the Sasthra platform, including study materials, videos, tests, and
          software, is the exclusive property of DataSpark AI Solutions or its licensors, protected
          by copyright, trademark, and other intellectual property laws. You may not copy,
          distribute, or use our content for commercial purposes without express written permission.
          By submitting feedback or content, you grant Sasthra a non-exclusive, royalty-free,
          perpetual license to use, modify, and distribute it for improving our Services.
        </p>
      )
    },
    {
      id: 7,
      title: 'Termination of Account',
      icon: <Users className="w-5 h-5" />,
      content: (
        <p className="text-gray-700 leading-relaxed">
          You may terminate your account by contacting us at{' '}
          <a
            href="mailto:<EMAIL>"
            className="text-primary hover:text-primary-dark transition-colors duration-300 underline">
            <EMAIL>
          </a>
          . Upon termination, any active enrollments may be canceled, and refunds will be processed
          per our refund policy. Sasthra may terminate or suspend your account for violations of
          these Terms, fraudulent activities, or non-compliance with applicable laws, without prior
          notice.
        </p>
      )
    },
    {
      id: 8,
      title: 'Disclaimers and Limitation of Liability',
      icon: <Shield className="w-5 h-5" />,
      content: (
        <div className="space-y-4">
          <div className="bg-red-50/50 border-l-4 border-red-200 p-4">
            <p className="text-gray-700 leading-relaxed">
              Our Services are provided "as-is" without warranties of any kind, express or implied,
              including merchantability, fitness for a particular purpose, or non-infringement.
            </p>
          </div>
          <p className="text-gray-700 leading-relaxed">
            Sasthra is not liable for any direct, indirect, incidental, or consequential damages
            arising from your use of the Services, including data loss, technical issues, or failure
            to achieve academic results. You assume all risks associated with using our platform,
            including reliance on the accuracy or completeness of our content.
          </p>
        </div>
      )
    },
    {
      id: 9,
      title: 'Governing Law and Dispute Resolution',
      icon: <FileText className="w-5 h-5" />,
      content: (
        <p className="text-gray-700 leading-relaxed">
          These Terms are governed by the laws of India. Any disputes arising from your use of the
          Services shall be resolved through arbitration in Bangalore, India, in accordance with the
          Arbitration and Conciliation Act, 1996. If any provision of these Terms is found
          unenforceable, the remaining provisions will remain in full force. You may not assign your
          obligations under these Terms without our consent, but Sasthra may assign its rights to
          third parties.
        </p>
      )
    },
    {
      id: 10,
      title: 'Contact Us',
      icon: <Mail className="w-5 h-5" />,
      content: (
        <div className="space-y-4">
          <p className="text-gray-700 leading-relaxed">
            For questions or concerns about these Terms and Conditions, please contact:
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-gradient-to-br from-primary/5 to-accent/5 p-4 rounded-lg">
              <h3 className="font-medium text-gray-800 mb-2 flex items-center">
                <Mail className="w-4 h-4 mr-2" />
                General Support
              </h3>
              <a
                href="mailto:<EMAIL>"
                className="text-primary hover:text-primary-dark transition-colors duration-300 underline">
                <EMAIL>
              </a>
            </div>
            <div className="bg-gradient-to-br from-primary/5 to-accent/5 p-4 rounded-lg">
              <h3 className="font-medium text-gray-800 mb-2 flex items-center">
                <Phone className="w-4 h-4 mr-2" />
                Phone No
              </h3>

              <a
                href="tel:+9198333879"
                className="text-primary hover:text-primary-dark transition-colors duration-300 underline">
                +9198333879
              </a>
            </div>
          </div>
          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <p className="text-gray-700">
              <span className="font-medium">Registered Office:</span> DataSpark AI Solutions (Pvt
              Ltd) , Chennai,Tamilnadu, India
            </p>
          </div>
        </div>
      )
    }
  ];

  const toggleSection = (id) => {
    setActiveSection(activeSection === id ? null : id);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 font-sans relative overflow-hidden">
      <style jsx global>{`
        :root {
          --primary: #4f46e5;
          --primary-dark: #4338ca;
          --accent: var(--color-student);
          --accent-dark: #ea580c;
        }

        .font-devanagari {
          font-family: 'Noto Sans Devanagari', sans-serif;
        }

        .text-primary {
          color: var(--primary);
        }

        .text-primary-dark {
          color: var(--primary-dark);
        }

        .text-accent {
          color: var(--accent);
        }

        .text-accent-dark {
          color: var(--accent-dark);
        }

        .bg-primary {
          background-color: var(--primary);
        }

        .bg-accent {
          background-color: var(--accent);
        }

        .border-primary {
          border-color: var(--primary);
        }

        .border-accent {
          border-color: var(--accent);
        }

        .glass-card {
          background: rgba(255, 255, 255, 0.92);
          backdrop-filter: blur(16px);
          -webkit-backdrop-filter: blur(16px);
          box-shadow: 0 8px 32px rgba(31, 38, 135, 0.1);
        }

        .section-highlight {
          position: relative;
        }

        .section-highlight::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          height: 100%;
          width: 4px;
          background: linear-gradient(to bottom, var(--primary), var(--accent));
          border-radius: 4px;
        }
      `}</style>

      <Suspense fallback={null}>
        <InteractiveParticles />
        <ScrollProgress />
      </Suspense>

      <div className="container mx-auto px-4 py-12 relative z-10">
        {/* Mobile Navigation Toggle */}
        <button
          className="md:hidden fixed top-6 right-6 z-50 bg-white p-3 rounded-full shadow-lg"
          onClick={() => setIsNavOpen(!isNavOpen)}>
          {isNavOpen ? (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6 text-gray-800"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          ) : (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6 text-gray-800"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 6h16M4 12h16M4 18h16"
              />
            </svg>
          )}
        </button>

        <div className="flex flex-col md:flex-row gap-8">
          {/* Mobile Navigation - Overlay */}
          <AnimatePresence>
            {isNavOpen && (
              <motion.div
                className="fixed inset-0 bg-black/30 z-40 md:hidden"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                onClick={() => setIsNavOpen(false)}>
                <motion.div
                  className="absolute top-0 right-0 h-full w-4/5 bg-white shadow-xl"
                  initial={{ x: '100%' }}
                  animate={{ x: 0 }}
                  exit={{ x: '100%' }}
                  transition={{ type: 'spring', damping: 30 }}
                  onClick={(e) => e.stopPropagation()}>
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-8">
                      <div className="flex items-center">
                        <img src={Logo} alt="Sasthra Logo" className="h-12 w-12" />
                        <h1 className="text-xl font-bold text-gray-900 ml-3">Sasthra</h1>
                      </div>
                      <button onClick={() => setIsNavOpen(false)}>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-6 w-6 text-gray-500"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor">
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                      </button>
                    </div>

                    <h2 className="text-lg font-semibold text-gray-800 mb-4">Terms & Conditions</h2>

                    <nav className="space-y-2">
                      {sections.map((section) => (
                        <button
                          key={section.id}
                          onClick={() => {
                            document.getElementById(`section-${section.id}`)?.scrollIntoView({
                              behavior: 'smooth',
                              block: 'start'
                            });
                            setActiveSection(section.id);
                            setIsNavOpen(false);
                          }}
                          className={`w-full text-left px-3 py-2 rounded-lg transition-colors duration-200 flex items-center ${activeSection === section.id ? 'bg-primary/10 text-primary font-medium' : 'text-gray-700 hover:bg-gray-100'}`}>
                          <span className="mr-2">{section.icon}</span>
                          <span>{section.title}</span>
                        </button>
                      ))}
                    </nav>

                    <div className="mt-6 pt-4 border-t border-gray-200">
                      <a
                        href="/privacy-policy"
                        className="text-sm text-gray-600 hover:text-primary transition-colors duration-200 block mb-2"
                        onClick={() => setIsNavOpen(false)}>
                        Privacy Policy
                      </a>
                      <a
                        href="/terms"
                        className="text-sm text-gray-600 hover:text-primary transition-colors duration-200 block"
                        onClick={() => setIsNavOpen(false)}>
                        Terms of Service
                      </a>
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Main Content */}
          <motion.div
            className="flex-1"
            variants={containerVariants}
            initial="hidden"
            animate="visible">
            <motion.div
              className="glass-card  mt-10 rounded-2xl overflow-hidden"
              variants={itemVariants}>
              <div className="p-8 md:p-10">
                <motion.div
                  className="relative flex flex-col items-center mb-12 py-8 px-6 rounded-2xl bg-[var(--color-teacher)] shadow-[0_10px_30px_-10px_rgba(79,70,229,0.1)]"
                  variants={itemVariants}
                  initial={{ y: -20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.6, ease: 'easeOut' }}>
                  {/* Back button with creative animation */}
                  <motion.div
                    whileHover={{ x: -4 }}
                    whileTap={{ scale: 0.95 }}
                    className="absolute left-6 top-6">
                    <Link to="/" className="flex items-center text-white group">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-6 w-6 mr-1 transition-transform duration-300 group-hover:-translate-x-1"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M10 19l-7-7m0 0l7-7m-7 7h18"
                        />
                      </svg>
                      <span className="font-medium text-white">Back</span>
                    </Link>
                  </motion.div>

                  {/* Logo with subtle float animation */}
                  <motion.div
                    animate={{
                      y: [0, -5, 0]
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                      ease: 'easeInOut'
                    }}
                    >
                    <img src={Logo} alt="Sasthra Logo" className="h-20 w-auto drop-shadow-lg" />
                  </motion.div>

                  {/* Title with gradient text */}
                  <motion.h1
                    className="text-4xl md:text-5xl text-white font-bold text-center mb-3 bg-clip-text "
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.2, duration: 0.6 }}>
                    Terms and Conditions
                  </motion.h1>

                 

                  {/* Animated decorative elements */}
                  <div className="absolute -bottom-7 left-1/2 transform -translate-x-1/2 flex space-x-2">
                    {[...Array(5)].map((_, i) => (
                      <motion.div
                        key={i}
                        className="h-2 w-2 rounded-full bg-primary"
                        initial={{ y: 0, opacity: 0 }}
                        animate={{
                          y: [0, -8, 0],
                          opacity: [0, 1, 0.5]
                        }}
                        transition={{
                          duration: 2,
                          delay: i * 0.2,
                          repeat: Infinity,
                          repeatType: 'reverse'
                        }}
                      />
                    ))}
                  </div>
                </motion.div>

                <div className="space-y-8">
                  {sections.map((section) => (
                    <motion.section
                      key={section.id}
                      id={`section-${section.id}`}
                      className={`p-6 rounded-xl transition-all duration-200 ${activeSection === section.id ? 'bg-gray-50/50 section-highlight' : 'hover:bg-gray-50/30'}`}
                      variants={itemVariants}
                      onViewportEnter={() => setActiveSection(section.id)}
                      viewport={{ margin: '-30% 0px -30% 0px', once: false }}>
                      <button
                        className="w-full flex justify-between items-center text-left"
                        onClick={() => toggleSection(section.id)}>
                        <div className="flex items-center">
                          <div
                            className={`w-10 h-10 rounded-lg flex items-center justify-center mr-4 ${activeSection === section.id ? 'bg-primary/10 text-primary' : 'bg-gray-100 text-gray-600'}`}>
                            {section.icon}
                          </div>
                          <h2 className="text-xl font-semibold text-[var(--color-student)]">
                            {section.id}. {section.title}
                          </h2>
                        </div>
                        {activeSection === section.id ? (
                          <ChevronUp className="w-5 h-5 text-gray-500" />
                        ) : (
                          <ChevronDown className="w-5 h-5 text-gray-500" />
                        )}
                      </button>

                      <AnimatePresence>
                        {activeSection === section.id && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.3, ease: 'easeInOut' }}
                            className="overflow-hidden">
                            <div className="pl-14 pt-4">{section.content}</div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </motion.section>
                  ))}
                </div>

                <motion.div
                  className="mt-12 pt-8 border-t border-gray-200 text-center"
                  variants={itemVariants}>
                  <p className="text-gray-600">
                    2025 Sasthra, a DataSpark AI Solutions (Pvt Ltd) . All rights reserved.
                  </p>
                </motion.div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default TermsAndConditions;
