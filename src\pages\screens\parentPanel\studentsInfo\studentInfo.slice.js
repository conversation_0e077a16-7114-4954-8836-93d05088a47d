

import {parentApi} from '../../../../redux/api/api';
import { createSlice } from '@reduxjs/toolkit';



 const initialState ={
  studentInfoData:null,
 }


 export const studentDetailsSlice = parentApi.injectEndpoints({
  endpoints:(builder)=>({
    studentDetailsService : builder.query({
      query:(body)=>({
        url:'/parent-dashboard',
        method:'GET',
        body,
        responseHandler:async(res)=> res.json()
      }),
      transformResponse:(responce)=>{
        console.log('Student Info:',responce);
        return responce;
      },
      transformErrorResponse:({ originalStatus,status,data})=>({
        status:originalStatus?? status,
        data
      }),
      providesTags:['studentInfo']
    }),
  })
 });

 const StudentInfoSlice =createSlice({
  name:'studentDetails',
  initialState,
  reducers:{
    setstudentDetailsData:(state,action)=>{
      state.studentInfoData = action.payload
    }
  }
 });

 export const { setstudentDetailsData}= StudentInfoSlice.actions

 export default  StudentInfoSlice.reducer;
  

 export const { useLazyStudentDetailsServiceQuery} = studentDetailsSlice;