import React, { useState, useEffect, useRef } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faSpinner,
  faCamera,
  faCheckCircle,
  faTimesCircle,
  faArrowLeft,
  faPowerOff,
  faCheck,
  faUserCircle,
  faAngleLeft,
  faAngleRight,
  faTimes
} from '@fortawesome/free-solid-svg-icons';
import { useCaptureFaceImagesMutation } from './addStudents.slice';
import Toastify from '../../../../components/PopUp/Toastify';
import { motion, AnimatePresence } from 'framer-motion';

const COUNSELOR_COLOR = '#f4c430';

const FaceCaptureModal = ({
  showFaceCapture,
  setShowFaceCapture,

  loading,
  setLoading,

  onFaceCaptureSuccess
}) => {
  const [cameraStream, setCameraStream] = useState(null);
  const [capturedImages, setCapturedImages] = useState({ front: null, left: null, right: null });
  const [faceCaptureResponse, setFaceCaptureResponse] = useState(null);
  const [captureFaceImagesService] = useCaptureFaceImagesMutation();
  const videoRef = useRef(null);
  const [res, setRes] = useState(null);

  // Camera control functions
  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { width: 640, height: 480, facingMode: 'user' }
      });
      setCameraStream(stream);
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
    } catch (err) {
      setRes('❌ Error accessing camera: ' + err.message);
      console.error('Camera error:', err);
    }
  };

  const stopCamera = () => {
    if (cameraStream) {
      cameraStream.getTracks().forEach((track) => track.stop());
      setCameraStream(null);
      if (videoRef.current) {
        videoRef.current.srcObject = null;
      }
    }
  };

  const captureImage = (type) => {
    if (!videoRef.current || !cameraStream) return;

    const canvas = document.createElement('canvas');
    canvas.width = videoRef.current.videoWidth;
    canvas.height = videoRef.current.videoHeight;
    const ctx = canvas.getContext('2d');
    ctx.drawImage(videoRef.current, 0, 0);

    canvas.toBlob(
      (blob) => {
        const file = new File([blob], `${type}_face.jpg`, { type: 'image/jpeg' });
        setCapturedImages((prev) => ({ ...prev, [type]: file }));
      },
      'image/jpeg',
      0.8
    );
  };

  const submitFaceImages = async () => {
    if (!capturedImages.front || !capturedImages.left || !capturedImages.right) {
      setRes('❌ Please capture all three face images (front, left, right)');
      return;
    }

    setLoading(true);
    try {
      const formData = new FormData();
      formData.append('front_face', capturedImages.front);
      formData.append('left_face', capturedImages.left);
      formData.append('right_face', capturedImages.right);

      const result = await captureFaceImagesService(formData).unwrap();
      setRes(result);
      stopCamera();
      setShowFaceCapture(false);

      if (result.session_id && result.face_embeddings) {
        const embedding = Array.isArray(result.face_embeddings)
          ? result.face_embeddings[0]
          : result.face_embeddings.embedding || result.face_embeddings;

        if (onFaceCaptureSuccess) {
          onFaceCaptureSuccess({
            face_embedding: embedding,
            face_session_id: result.session_id
          });
        }
      }
    } catch (error) {
      setFaceCaptureResponse({
        success: false,
        message: error.message || 'Error submitting face images'
      });
      console.error('Submission error:', error);
    } finally {
      setLoading(false);
    }
  };

  // Initialize camera when modal opens
  useEffect(() => {
    if (showFaceCapture) {
      startCamera();
    }
    return () => {
      stopCamera();
    };
  }, [showFaceCapture]);

  // Auto-close on success
  useEffect(() => {
    if (faceCaptureResponse?.success) {
      const timer = setTimeout(() => {
        setShowFaceCapture(false);
        stopCamera();
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [faceCaptureResponse, setShowFaceCapture]);

  const renderFaceCaptureResponse = () => {
    if (!faceCaptureResponse) return null;
    return (
      <AnimatePresence>
        <motion.div
          key="face-capture-response"
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.7, opacity: 0 }}
          transition={{ type: 'spring', stiffness: 300, damping: 25 }}
          className="mt-4 p-4 rounded-xl shadow-xl border-2"
          style={{
            background: faceCaptureResponse.success
              ? `linear-gradient(135deg, #fff 0%, ${COUNSELOR_COLOR} 100%)`
              : 'linear-gradient(135deg, #fff 0%, #f87171 100%)',
            borderColor: faceCaptureResponse.success ? COUNSELOR_COLOR : '#f87171'
          }}>
          <div className="flex items-center mb-2">
            <FontAwesomeIcon
              icon={faceCaptureResponse.success ? faCheckCircle : faTimesCircle}
              className={
                faceCaptureResponse.success
                  ? 'text-yellow-500 text-2xl mr-2'
                  : 'text-red-500 text-2xl mr-2'
              }
            />
            <h4
              className="text-lg font-bold"
              style={{ color: faceCaptureResponse.success ? COUNSELOR_COLOR : '#b91c1c' }}>
              {faceCaptureResponse.success ? 'Success!' : 'Failed'}
            </h4>
          </div>
          <div className="text-black text-base">
            {faceCaptureResponse.sessionId && (
              <p>
                <strong>Session ID:</strong> {faceCaptureResponse.sessionId.substring(0, 8)}...
              </p>
            )}
            <p>
              <strong>Message:</strong> {faceCaptureResponse.message}
            </p>
            {faceCaptureResponse.timestamp && (
              <p>
                <strong>Timestamp:</strong> {faceCaptureResponse.timestamp}
              </p>
            )}
          </div>
          {faceCaptureResponse.success && (
            <motion.div
              className="mt-2 text-yellow-700 text-sm font-semibold"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ delay: 0.2 }}>
              This popup will close automatically.
            </motion.div>
          )}
          {!faceCaptureResponse.success && (
            <motion.button
              type="button"
              onClick={() => setFaceCaptureResponse(null)}
              className="mt-4 text-xs bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600"
              whileHover={{ scale: 1.08, boxShadow: '0 2px 8px #888' }}
              whileTap={{ scale: 0.95 }}>
              <FontAwesomeIcon icon={faTimes} className="mr-1" /> Clear
            </motion.button>
          )}
        </motion.div>
      </AnimatePresence>
    );
  };

  if (!showFaceCapture) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center"
      style={{ background: 'rgba(0,0,0,0.35)', backdropFilter: 'blur(8px)' }}>
      <Toastify res={res} resClear={() => setRes(null)} />
      <div
        className="relative max-w-md w-full mx-2 p-0 rounded-2xl shadow-2xl border-0"
        style={{ background: 'rgba(255,255,255,0.7)', backdropFilter: 'blur(16px)' }}>
        <button
          type="button"
          onClick={() => {
            setShowFaceCapture(false);
            stopCamera();
          }}
          className="absolute top-3 right-4 hover:cursor-pointer text-black hover:text-yellow-500 text-2xl font-extrabold z-10"
          style={{ background: 'none', border: 'none', boxShadow: 'none' }}>
          <FontAwesomeIcon icon={faTimes} />
        </button>
        <div className="flex flex-col items-center py-6 px-3">
          <div className="mb-4 flex flex-col items-center">
            <div className="relative">
              <div className="absolute -inset-2 bg-yellow-400 rounded-full blur opacity-30"></div>
              <div className="relative bg-white/60 rounded-full p-3 shadow-lg mb-1 border-2 border-yellow-400">
                <FontAwesomeIcon icon={faCamera} className="text-2xl text-black" />
              </div>
            </div>
            <h3
              className="text-xl font-extrabold text-black mb-1 tracking-tight"
              style={{ letterSpacing: '0.01em' }}>
              Face Capture
            </h3>
            <p className="text-sm text-yellow-700 font-medium">Biometric Registration</p>
          </div>

          {/* Progress indicator */}
          <div className="flex items-center justify-center gap-4 mb-6">
            {['front', 'left', 'right'].map((type, idx) => (
              <div key={type} className="flex flex-col items-center">
                <div className="relative">
                  <div
                    className={`absolute -inset-0.5 rounded-full blur ${capturedImages[type] ? 'bg-yellow-400' : 'bg-gray-300'} opacity-30`}></div>
                  <div
                    className={`relative w-8 h-8 flex items-center justify-center rounded-full border-2 ${capturedImages[type] ? 'bg-yellow-400 border-yellow-500 shadow-lg' : 'bg-white/70 border-gray-300'} transition-all duration-300`}
                    style={{
                      boxShadow: capturedImages[type] ? '0 2px 12px #f4c43055' : undefined
                    }}>
                    {capturedImages[type] ? (
                      <FontAwesomeIcon icon={faCheck} className="text-white text-sm" />
                    ) : (
                      <span className="text-black text-xs font-bold">{idx + 1}</span>
                    )}
                  </div>
                </div>
                <span className="text-[10px] mt-1.5 text-black font-semibold capitalize">
                  {type}
                </span>
              </div>
            ))}
          </div>

          {/* Circular video preview */}
          <div className="relative mb-6">
            <div className="absolute -inset-1 bg-yellow-400 rounded-full blur opacity-30"></div>
            <div className="relative w-40 h-40 rounded-full overflow-hidden border-4 border-yellow-400 shadow-xl flex items-center justify-center bg-white/40">
              <video
                ref={videoRef}
                autoPlay
                playsInline
                className="object-cover w-full h-full"
                style={{ borderRadius: '50%' }}
              />
            </div>
          </div>

          {/* Capture buttons */}
          <div className="grid grid-cols-3 gap-3 mb-6 w-full px-4">
            <motion.button
              type="button"
              onClick={() => captureImage('front')}
              className={`relative overflow-hidden w-full py-2.5 rounded-lg font-bold text-sm shadow-md border hover:cursor-pointer transition-all duration-200 ${capturedImages.front ? 'bg-yellow-400 border-yellow-500 text-black' : 'bg-white/80 border-yellow-300 text-yellow-700 hover:bg-yellow-100'}`}
              style={{ borderColor: COUNSELOR_COLOR }}
              disabled={!cameraStream}
              whileHover={{ scale: 1.07, boxShadow: '0 2px 8px #f4c430' }}
              whileTap={{ scale: 0.95 }}>
              {capturedImages.front ? (
                <>
                  <div className="absolute inset-0 bg-white/20"></div>
                  <FontAwesomeIcon icon={faCheckCircle} className="mr-1.5" />
                  Front
                </>
              ) : (
                <>
                  <FontAwesomeIcon icon={faUserCircle} className="mr-1.5" />
                  Front
                </>
              )}
            </motion.button>
            <motion.button
              type="button"
              onClick={() => captureImage('left')}
              className={`relative overflow-hidden w-full py-2.5 rounded-lg font-bold text-sm shadow-md border transition-all hover:cursor-pointer duration-200 ${capturedImages.left ? 'bg-yellow-400 border-yellow-500 text-black' : 'bg-white/80 border-yellow-300 text-yellow-700 hover:bg-yellow-100'}`}
              style={{ borderColor: COUNSELOR_COLOR }}
              disabled={!cameraStream}
              whileHover={{ scale: 1.07, boxShadow: '0 2px 8px #f4c430' }}
              whileTap={{ scale: 0.95 }}>
              {capturedImages.left ? (
                <>
                  <div className="absolute inset-0 bg-white/20"></div>
                  <FontAwesomeIcon icon={faCheckCircle} className="mr-1.5" />
                  Left
                </>
              ) : (
                <>
                  <FontAwesomeIcon icon={faAngleLeft} className="mr-1.5" />
                  Left
                </>
              )}
            </motion.button>
            <motion.button
              type="button"
              onClick={() => captureImage('right')}
              className={`relative overflow-hidden w-full py-2.5 rounded-lg font-bold text-sm shadow-md border transition-all hover:cursor-pointer duration-200 ${capturedImages.right ? 'bg-yellow-400 border-yellow-500 text-black' : 'bg-white/80 border-yellow-300 text-yellow-700 hover:bg-yellow-100'}`}
              style={{ borderColor: COUNSELOR_COLOR }}
              disabled={!cameraStream}
              whileHover={{ scale: 1.07, boxShadow: '0 2px 8px #f4c430' }}
              whileTap={{ scale: 0.95 }}>
              {capturedImages.right ? (
                <>
                  <div className="absolute inset-0 bg-white/20"></div>
                  <FontAwesomeIcon icon={faCheckCircle} className="mr-1.5" />
                  Right
                </>
              ) : (
                <>
                  <FontAwesomeIcon icon={faAngleRight} className="mr-1.5" />
                  Right
                </>
              )}
            </motion.button>
          </div>

          {/* Camera controls and submit */}
          <div className="flex flex-col md:flex-row justify-between items-center w-full gap-3 mb-2 px-4">
            <div className="flex gap-2 w-full md:w-auto">
              <motion.button
                type="button"
                onClick={startCamera}
                className="relative overflow-hidden flex-1 bg-white/80 border hover:cursor-pointer border-green-400 text-green-700 font-bold px-3 py-2 rounded-lg hover:bg-green-50 shadow-md transition-all text-sm"
                disabled={cameraStream || loading}
                whileHover={{ scale: 1.07, boxShadow: '0 2px 8px #22c55e' }}
                whileTap={{ scale: 0.95 }}>
                <div className="absolute inset-0 bg-green-400 opacity-0 hover:opacity-10 transition-opacity"></div>
                <FontAwesomeIcon icon={faPowerOff} className="mr-2 text-green-600" />
                {cameraStream ? 'Camera Active' : 'Start Camera'}
              </motion.button>
              <motion.button
                type="button"
                onClick={stopCamera}
                className="relative overflow-hidden flex-1 bg-white/80 border hover:cursor-pointer border-red-400 text-red-700 font-bold px-3 py-2 rounded-lg hover:bg-red-50 shadow-md transition-all text-sm"
                disabled={!cameraStream}
                whileHover={{ scale: 1.07, boxShadow: '0 2px 8px #ef4444' }}
                whileTap={{ scale: 0.95 }}>
                <div className="absolute inset-0 bg-red-400 opacity-0 hover:opacity-10 transition-opacity"></div>
                <FontAwesomeIcon icon={faPowerOff} className="mr-2 text-red-600" />
                Stop
              </motion.button>
            </div>
            <div className="flex gap-2 w-full md:w-auto">
              <motion.button
                type="button"
                onClick={() => {
                  setShowFaceCapture(false);
                  stopCamera();
                }}
                className="relative overflow-hidden flex-1 bg-gray-200 hover:cursor-pointer border border-gray-400 text-gray-700 font-bold px-3 py-2 rounded-lg hover:bg-gray-300 shadow-md transition-all text-sm"
                whileHover={{ scale: 1.07, boxShadow: '0 2px 8px #888' }}
                whileTap={{ scale: 0.95 }}>
                <div className="absolute inset-0 bg-gray-400 opacity-0 hover:opacity-10 transition-opacity"></div>
                <FontAwesomeIcon icon={faArrowLeft} className="mr-2" />
                Cancel
              </motion.button>
              <motion.button
                type="button"
                onClick={submitFaceImages}
                className="relative overflow-hidden flex-1 bg-yellow-400 hover:cursor-pointer border border-yellow-500 text-black font-bold px-3 py-2 rounded-lg shadow-md hover:bg-yellow-500 transition-all flex items-center justify-center gap-2 text-sm"
                style={{ background: COUNSELOR_COLOR, borderColor: COUNSELOR_COLOR }}
                disabled={
                  loading || !capturedImages.front || !capturedImages.left || !capturedImages.right
                }
                whileHover={{ scale: 1.07, boxShadow: '0 2px 8px #f4c430' }}
                whileTap={{ scale: 0.95 }}>
                <div className="absolute inset-0 bg-white opacity-0 hover:opacity-20 transition-opacity"></div>
                {loading ? (
                  <>
                    <FontAwesomeIcon icon={faSpinner} spin className="text-yellow-700" />
                    Processing...
                  </>
                ) : (
                  <>
                    <FontAwesomeIcon icon={faCheckCircle} />
                    Submit
                  </>
                )}
              </motion.button>
            </div>
          </div>

          {/* Response card */}
          {renderFaceCaptureResponse()}
        </div>
      </div>
    </div>
  );
};

export default FaceCaptureModal;
