import React from 'react';
import { Link } from 'react-router-dom';
import { Bell } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

const NotificationDropdown = ({
  notifications,
  showNotifications,
  setShowNotifications,
  setIsProfileOpen,
  handleNotificationClick,
  unreadNotifications,
  setUnreadNotifications
}) => {
  return (
    <div className="relative">
      <motion.button
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        onClick={() => {
          setShowNotifications(!showNotifications);
          setIsProfileOpen(false);
          setUnreadNotifications(0);
        }}
        className="text-white p-2 hover:cursor-pointer relative">
        <Bell size={24} />
        {unreadNotifications > 0 && (
          <motion.span
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
            {unreadNotifications}
          </motion.span>
        )}
      </motion.button>

      <AnimatePresence>
        {showNotifications && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ type: 'spring', damping: 20 }}
            className="absolute right-0 mt-2 w-72 bg-white rounded-xl shadow-xl py-2 z-40">
            <div className="px-4 py-2 border-b border-gray-100">
              <h3 className="font-semibold text-gray-800">Notifications</h3>
            </div>
            <div className="max-h-60 overflow-y-auto">
              {notifications.length > 0 ? (
                notifications.map((notification) => (
                  <motion.div
                    key={notification.id}
                    whileHover={{ backgroundColor: '#f8fafc' }}
                    onClick={() => handleNotificationClick(notification.id)}
                    className={`px-4 py-3 cursor-pointer ${!notification.read ? 'bg-blue-50' : ''}`}>
                    <p className="text-sm text-gray-800">{notification.text}</p>
                    <p className="text-xs text-gray-500 mt-1">{notification.time}</p>
                  </motion.div>
                ))
              ) : (
                <div className="px-4 py-3 text-center text-gray-500 text-sm">No notifications</div>
              )}
            </div>
            <div className="px-4 py-2 border-t border-gray-100 text-center">
              <Link to="/notifications" className="text-sm text-blue-500 hover:underline">
                View all
              </Link>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default NotificationDropdown;
