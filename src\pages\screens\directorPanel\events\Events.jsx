"use client";

import { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useDispatch, useSelector } from "react-redux";
import {
  useCreateEventMutation,
  useListEventsQuery,
  useGetEventQuery,
  useUpdateEventMutation,
  useDeleteEventMutation,
  useGetKotaTeachersQuery,
  useGetUpcomingEventsQuery,
  useDownloadEventDocumentQuery,
  useDeleteEventDocumentsMutation,
} from "./events.Slice";
import { useListCoursesServiceQuery } from "../courses/addCourses.Slice";
import { useListBatchServiceQuery } from "../batches/createBatches.Slice";
import {
  setEvents,
  setSelectedEvent,
  setKotaTeachers,
  setUpcomingEvents,
} from "./events.Slice";
import Toastify from "../../../../components/PopUp/Toastify";

const CreateEvents = () => {
  const dispatch = useDispatch();
  const { events, selectedEvent, kotaTeachers, upcomingEvents } = useSelector(
    (state) => state.events || {
      events: [],
      selectedEvent: null,
      kotaTeachers: [],
      upcomingEvents: [],
    }
  );

  const [activeTab, setActiveTab] = useState("create");
  const [isEditing, setIsEditing] = useState(false);
  const [selectedEventId, setSelectedEventId] = useState(null);
  const [showEventDetails, setShowEventDetails] = useState(false);
  const [res, setRes] = useState(null);
  const [pdfPreview, setPdfPreview] = useState(null); // State for PDF preview

  const [eventForm, setEventForm] = useState({
    course_id: "",
    course_name: "",
    batch_id: "",
    batch_name: "",
    subject: "",
    chapter_name: "",
    topic: "",
    lecture_number: "",
    event_date: "",
    event_time: "",
    kota_teacher_uuid: "",
    language: "",
    event_content_document: null,
    notes_document: null,
  });

  const [eventContentFile, setEventContentFile] = useState(null);
  const [notesFile, setNotesFile] = useState(null);
  const eventContentFileRef = useRef(null);
  const notesFileRef = useRef(null);

  const [filters, setFilters] = useState({
    course_name: "",
    course_id: "",
    batch_name: "",
    batch_id: "",
    subject: "",
    teacher_id: "",
    date_from: "",
    date_to: "",
  });

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1,
        ease: "easeOut",
      },
    },
    exit: {
      opacity: 0,
      y: -30,
      transition: { duration: 0.3 },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4, ease: "easeOut" },
    },
  };

  const cardVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.4,
        ease: "easeOut",
      },
    },
    hover: {
      scale: 1.02,
      boxShadow:
        "0 20px 25px -5px rgba(125, 30, 28, 0.1), 0 10px 10px -5px rgba(125, 30, 28, 0.04)",
      transition: { duration: 0.2 },
    },
  };

  const tabVariants = {
    inactive: {
      scale: 0.95,
      opacity: 0.7,
      transition: { duration: 0.2 },
    },
    active: {
      scale: 1,
      opacity: 1,
      transition: { duration: 0.2 },
    },
  };

  const buttonVariants = {
    hover: {
      scale: 1.05,
      transition: { duration: 0.2 },
    },
    tap: {
      scale: 0.95,
      transition: { duration: 0.1 },
    },
  };

  // RTK Query hooks
  const { data: courses, isLoading: isCoursesLoading, error: coursesError } =
    useListCoursesServiceQuery();
  const { data: batches, isLoading: isBatchesLoading, error: batchesError } =
    useListBatchServiceQuery();
  const {
    data: kotaTeachersData,
    isLoading: isKotaTeachersLoading,
    error: kotaTeachersError,
  } = useGetKotaTeachersQuery();
  const {
    data: listedEvents,
    isLoading: isEventsLoading,
    error: eventsError,
    refetch: refetchEvents,
  } = useListEventsQuery(filters, { skip: activeTab !== "list" });
  const {
    data: upcomingEventsData,
    isLoading: isUpcomingEventsLoading,
    error: upcomingEventsError,
  } = useGetUpcomingEventsQuery({}, { skip: activeTab !== "upcoming" });
  const [createEvent, { isLoading: isCreatingEvent }] = useCreateEventMutation();
  const [updateEvent, { isLoading: isUpdatingEvent }] = useUpdateEventMutation();
  const [deleteEvent, { isLoading: isDeletingEvent }] = useDeleteEventMutation();
  const [deleteEventDocuments] = useDeleteEventDocumentsMutation();
  const {
    data: eventDetails,
    isLoading: isEventDetailsLoading,
    error: eventDetailsError,
    refetch: refetchEvent,
  } = useGetEventQuery(selectedEventId, { skip: !selectedEventId });
  const { refetch: refetchEventContentDownload } = useDownloadEventDocumentQuery(
    { eventId: 0, documentType: "event_content" },
    { skip: true }
  );
  const { refetch: refetchNotesDownload } = useDownloadEventDocumentQuery(
    { eventId: 0, documentType: "notes" },
    { skip: true }
  );

  // Effects
  useEffect(() => {
    if (listedEvents) dispatch(setEvents(listedEvents));
    if (kotaTeachersData) {
      const teachersWithFullName = (kotaTeachersData || []).map((teacher) => ({
        ...teacher,
        full_name: `${teacher.first_name} ${teacher.last_name}`,
      }));
      dispatch(setKotaTeachers(teachersWithFullName));
    }
    if (upcomingEventsData) dispatch(setUpcomingEvents(upcomingEventsData));
  }, [listedEvents, kotaTeachersData, upcomingEventsData, dispatch]);

  useEffect(() => {
    if (coursesError) {
      setRes({
        status: coursesError.status || 500,
        data: { message: coursesError.data?.message || coursesError.message },
      });
    }
    if (batchesError) {
      setRes({
        status: batchesError.status || 500,
        data: { message: batchesError.data?.message || batchesError.message },
      });
    }
    if (kotaTeachersError) {
      setRes({
        status: kotaTeachersError.status || 500,
        data: { message: kotaTeachersError.data?.message || kotaTeachersError.message },
      });
    }
    if (eventsError) {
      setRes({
        status: eventsError.status || 500,
        data: { message: eventsError.data?.message || eventsError.message },
      });
    }
    if (upcomingEventsError) {
      setRes({
        status: upcomingEventsError.status || 500,
        data: { message: upcomingEventsError.data?.message || upcomingEventsError.message },
      });
    }
    if (eventDetailsError) {
      console.log("eventDetailsError:", eventDetailsError);
      setRes({
        status: eventDetailsError.status || 500,
        data: { message: eventDetailsError.data?.message || eventDetailsError.message },
      });
    }
  }, [
    coursesError,
    batchesError,
    kotaTeachersError,
    eventsError,
    upcomingEventsError,
    eventDetailsError,
  ]);

  useEffect(() => {
    console.log("useGetEventQuery:", {
      selectedEventId,
      isEventDetailsLoading,
      eventDetails,
      eventDetailsError,
    });
    if (isEditing && eventDetails && selectedEventId) {
      const eventData = eventDetails.event || eventDetails;
      const formattedDate = eventData.event_date
        ? new Date(eventData.event_date).toISOString().split("T")[0]
        : "";

      const newFormState = {
        course_id: eventData.course_id || "",
        course_name: eventData.course_name || "",
        batch_id: eventData.batch_id || "",
        batch_name: eventData.batch_name || "",
        subject: eventData.subject || "",
        chapter_name: eventData.chapter_name || "",
        topic: eventData.topic || "",
        lecture_number: eventData.lecture_number || "",
        event_date: formattedDate,
        event_time: eventData.event_time || "",
        kota_teacher_uuid: eventData.kota_teacher_uuid || "",
        language: eventData.language || "",
        event_content_document: null,
        notes_document: null,
      };

      console.log("Prefilling form with:", newFormState);
      setEventForm(newFormState);
      dispatch(setSelectedEvent(eventData));
    }
  }, [eventDetails, isEditing, selectedEventId, dispatch]);

  useEffect(() => {
    if (eventForm.course_id && courses) {
      const selectedCourse = courses.find(
        (course) => course.course_id === eventForm.course_id
      );
      if (selectedCourse) {
        setEventForm((prev) => ({
          ...prev,
          course_name: selectedCourse.course_name,
        }));
      }
    } else {
      setEventForm((prev) => ({ ...prev, course_name: "" }));
    }
  }, [eventForm.course_id, courses]);

  useEffect(() => {
    if (eventForm.batch_id && batches) {
      const selectedBatch = batches.find(
        (batch) => batch.batch_id === eventForm.batch_id
      );
      if (selectedBatch) {
        setEventForm((prev) => ({
          ...prev,
          batch_name: selectedBatch.batch_name,
        }));
      }
    } else {
      setEventForm((prev) => ({ ...prev, batch_name: "" }));
    }
  }, [eventForm.batch_id, batches]);

  // Event handlers
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setEventForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleFileChange = (e, setFile, fieldName) => {
    const file = e.target.files[0];
    if (file) {
      const allowedTypes = [
        "application/pdf",
        "application/vnd.ms-powerpoint",
        "application/vnd.openxmlformats-officedocument.presentationml.presentation",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "text/plain",
        "application/vnd.ms-excel",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      ];
      if (!allowedTypes.includes(file.type)) {
        setRes({
          status: 400,
          data: {
            message:
              "File type not supported. Allowed: PDF, PPT, PPTX, DOC, DOCX, TXT, XLS, XLSX",
          },
        });
        return;
      }
      setFile(file);
      setEventForm((prev) => ({ ...prev, [fieldName]: file }));
    }
  };

  const resetForm = () => {
    console.log(
      "resetForm called at:",
      new Date().toLocaleString("en-IN", { timeZone: "Asia/Kolkata" })
    );
    setEventForm({
      course_id: "",
      course_name: "",
      batch_id: "",
      batch_name: "",
      subject: "",
      chapter_name: "",
      topic: "",
      lecture_number: "",
      event_date: "",
      event_time: "",
      kota_teacher_uuid: "",
      language: "",
      event_content_document: null,
      notes_document: null,
    });
    setEventContentFile(null);
    setNotesFile(null);
    if (eventContentFileRef.current) eventContentFileRef.current.value = "";
    if (notesFileRef.current) notesFileRef.current.value = "";
    setIsEditing(false);
    setSelectedEventId(null);
    dispatch(setSelectedEvent(null));
    setPdfPreview(null); // Clear PDF preview
  };

  const resClear = () => {
    setRes(null);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const formData = new FormData();
      Object.keys(eventForm).forEach((key) => {
        if (key !== "event_content_document" && key !== "notes_document") {
          formData.append(key, eventForm[key]);
        }
      });

      if (eventContentFile)
        formData.append("event_content_document", eventContentFile);
      if (notesFile) formData.append("notes_document", notesFile);

      if (isEditing) {
        await updateEvent({ eventId: selectedEventId, body: formData }).unwrap();
        setRes({ status: 200, data: { message: "Event updated successfully!" } });
      } else {
        await createEvent(formData).unwrap();
        setRes({ status: 200, data: { message: "Event created successfully!" } });
      }

      resetForm();
    } catch (error) {
      setRes({
        status: error.status || 500,
        data: {
          message:
            error.data?.message ||
            `Failed to ${isEditing ? "update" : "create"} event`,
        },
      });
      console.error(`${isEditing ? "Update" : "Create"} event error:`, error);
    }
  };

  const handleDeleteEvent = async (eventId) => {
    if (
      !window.confirm(
        "Are you sure you want to delete this event? This will also delete associated documents."
      )
    ) {
      return;
    }
    try {
      await deleteEvent(eventId).unwrap();
      setRes({ status: 200, data: { message: "Event deleted successfully!" } });
      refetchEvents();
      if (selectedEventId === eventId) {
        setShowEventDetails(false);
      }
    } catch (error) {
      setRes({
        status: error.status || 500,
        data: { message: error.data?.message || "Failed to delete event" },
      });
      console.error("Delete event error:", error);
    }
  };

  const handleEditEvent = (eventId) => {
    console.log("Editing event with ID:", eventId);
    setSelectedEventId(eventId);
    setIsEditing(true);
    setActiveTab("create");
    refetchEvent();
  };

  const viewEventDetails = (event) => {
    dispatch(setSelectedEvent(event));
    setShowEventDetails(true);
  };

  const handleDownloadDocument = async (eventId, documentType) => {
    console.log("handleDownloadDocument called:", {
      eventId,
      documentType,
      time: new Date().toLocaleString("en-IN", { timeZone: "Asia/Kolkata" }),
    });
    try {
      let downloadData;
      if (documentType === "event_content") {
        downloadData = await refetchEventContentDownload({ eventId, documentType });
      } else if (documentType === "notes") {
        downloadData = await refetchNotesDownload({ eventId, documentType });
      }

      if (downloadData?.data?.blob) {
        const url = URL.createObjectURL(downloadData.data.blob);
        setPdfPreview({
          url,
          filename: downloadData.data.filename || `${documentType}_document.pdf`,
        });
        setShowEventDetails(true); // Show modal with PDF preview
      } else if (downloadData?.data?.download_url) {
        // Fallback if backend returns a URL
        const response = await fetch(downloadData.data.download_url);
        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        setPdfPreview({ url, filename: `${documentType}_document.pdf` });
        setShowEventDetails(true);
      } else {
        setRes({ status: 400, data: { message: "No file data provided" } });
      }
    } catch (error) {
      console.error("Download document error:", error);
      setRes({
        status: error.status || 500,
        data: {
          message: error.data?.message || `Error downloading ${documentType} document`,
        },
      });
    }
  };

  const handleDeleteDocuments = async (eventId, documentType) => {
    if (
      !window.confirm(`Are you sure you want to delete the ${documentType} document?`)
    ) {
      return;
    }
    try {
      await deleteEventDocuments({
        eventId,
        body: { document_types: [documentType] },
      }).unwrap();
      setRes({ status: 200, data: { message: "Document deleted successfully!" } });
      refetchEvents();
    } catch (error) {
      setRes({
        status: error.status || 500,
        data: {
          message: error.data?.message || `Failed to delete ${documentType} document`,
        },
      });
      console.error("Delete document error:", error);
    }
  };

  const applyFilters = () => {
    refetchEvents();
  };

  const clearFilters = () => {
    setFilters({
      course_name: "",
      course_id: "",
      batch_name: "",
      batch_id: "",
      subject: "",
      teacher_id: "",
      date_from: "",
      date_to: "",
    });
    refetchEvents();
  };

  const renderCreateEventForm = () => (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      whileHover="hover"
      className="bg-gradient-to-br from-white via-red-50 to-orange-50 p-8 rounded-3xl shadow-2xl border border-red-100"
      style={{ "--color-director": "#7d1e1c" }}
    >
      <motion.div
        initial={{ opacity: 0, x: -30 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.6 }}
        className="flex items-center mb-8"
      >
        <motion.div
          className="w-16 h-16 rounded-2xl flex items-center justify-center mr-6 shadow-lg"
          style={{ background: "linear-gradient(135deg, #7d1e1c, #a52a2a)" }}
          whileHover={{ rotate: 5, scale: 1.1 }}
          transition={{ duration: 0.3 }}
        >
          <span className="text-white text-2xl">📅</span>
        </motion.div>
        <div>
          <h2 className="text-3xl font-bold text-gray-800 mb-2">
            {isEditing ? "Edit Event" : "Create New Event"}
          </h2>
          <p className="text-gray-600">
            Fill in the details to {isEditing ? "update" : "create"} your event
          </p>
        </div>
      </motion.div>

      <form onSubmit={handleSubmit} className="space-y-8">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 md:grid-cols-2 gap-6"
        >
          {/* Course Selection */}
          <motion.div variants={itemVariants} className="space-y-3">
            <label className="block text-sm font-bold text-gray-700 mb-2">📚 Course</label>
            <motion.select
              name="course_id"
              value={eventForm.course_id}
              onChange={handleInputChange}
              className="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:border-red-500 focus:ring-4 focus:ring-red-100 transition-all duration-300 bg-white shadow-sm hover:shadow-md"
              style={{ "--tw-ring-color": "rgba(125, 30, 28, 0.1)" }}
              required
              disabled={isCoursesLoading}
              whileFocus={{ scale: 1.02 }}
            >
              <option value="">Select Course</option>
              {isCoursesLoading ? (
                <option>Loading...</option>
              ) : (
                courses?.map((course) => (
                  <option key={course.course_id} value={course.course_id}>
                    {course.course_name}
                  </option>
                ))
              )}
            </motion.select>
          </motion.div>

          {/* Batch Selection */}
          <motion.div variants={itemVariants} className="space-y-3">
            <label className="block text-sm font-bold text-gray-700 mb-2">👥 Batch</label>
            <motion.select
              name="batch_id"
              value={eventForm.batch_id}
              onChange={handleInputChange}
              className="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:border-red-500 focus:ring-4 focus:ring-red-100 transition-all duration-300 bg-white shadow-sm hover:shadow-md"
              required
              disabled={isBatchesLoading}
              whileFocus={{ scale: 1.02 }}
            >
              <option value="">Select Batch</option>
              {isBatchesLoading ? (
                <option>Loading...</option>
              ) : (
                batches?.map((batch) => (
                  <option key={batch.batch_id} value={batch.batch_id}>
                    {batch.batch_name}
                  </option>
                ))
              )}
            </motion.select>
          </motion.div>

          {/* Subject */}
          <motion.div variants={itemVariants} className="space-y-3">
            <label className="block text-sm font-bold text-gray-700 mb-2">📖 Subject</label>
            <motion.input
              type="text"
              name="subject"
              placeholder="Enter subject"
              value={eventForm.subject}
              onChange={handleInputChange}
              className="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:border-red-500 focus:ring-4 focus:ring-red-100 transition-all duration-300 shadow-sm hover:shadow-md"
              required
              whileFocus={{ scale: 1.02 }}
            />
          </motion.div>

          {/* Chapter Name */}
          <motion.div variants={itemVariants} className="space-y-3">
            <label className="block text-sm font-bold text-gray-700 mb-2">📑 Chapter Name</label>
            <motion.input
              type="text"
              name="chapter_name"
              placeholder="Enter chapter name"
              value={eventForm.chapter_name}
              onChange={handleInputChange}
              className="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:border-red-500 focus:ring-4 focus:ring-red-100 transition-all duration-300 shadow-sm hover:shadow-md"
              required
              whileFocus={{ scale: 1.02 }}
            />
          </motion.div>

          {/* Topic */}
          <motion.div variants={itemVariants} className="space-y-3">
            <label className="block text-sm font-bold text-gray-700 mb-2">💡 Topic</label>
            <motion.input
              type="text"
              name="topic"
              placeholder="Enter topic"
              value={eventForm.topic}
              onChange={handleInputChange}
              className="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:border-red-500 focus:ring-4 focus:ring-red-100 transition-all duration-300 shadow-sm hover:shadow-md"
              required
              whileFocus={{ scale: 1.02 }}
            />
          </motion.div>

          {/* Lecture Number */}
          <motion.div variants={itemVariants} className="space-y-3">
            <label className="block text-sm font-bold text-gray-700 mb-2">🔢 Lecture Number</label>
            <motion.input
              type="number"
              name="lecture_number"
              placeholder="Enter lecture number"
              value={eventForm.lecture_number}
              onChange={handleInputChange}
              className="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:border-red-500 focus:ring-4 focus:ring-red-100 transition-all duration-300 shadow-sm hover:shadow-md"
              required
              whileFocus={{ scale: 1.02 }}
            />
          </motion.div>

          {/* Teacher Selection */}
          <motion.div variants={itemVariants} className="space-y-3">
            <label className="block text-sm font-bold text-gray-700 mb-2">👨‍🏫 Kota Teacher</label>
            <motion.select
              name="kota_teacher_uuid"
              value={eventForm.kota_teacher_uuid}
              onChange={handleInputChange}
              className="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:border-red-500 focus:ring-4 focus:ring-red-100 transition-all duration-300 bg-white shadow-sm hover:shadow-md"
              required
              disabled={isKotaTeachersLoading}
              whileFocus={{ scale: 1.02 }}
            >
              <option value="">Select Kota Teacher</option>
              {isKotaTeachersLoading ? (
                <option>Loading...</option>
              ) : (
                kotaTeachers?.map((teacher) => (
                  <option key={teacher.id} value={teacher.id}>
                    {teacher.full_name} ({teacher.course})
                  </option>
                ))
              )}
            </motion.select>
          </motion.div>

          {/* Event Date */}
          <motion.div variants={itemVariants} className="space-y-3">
            <label className="block text-sm font-bold text-gray-700 mb-2">📅 Event Date</label>
            <motion.input
              type="date"
              name="event_date"
              value={eventForm.event_date}
              onChange={handleInputChange}
              className="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:border-red-500 focus:ring-4 focus:ring-red-100 transition-all duration-300 shadow-sm hover:shadow-md"
              required
              whileFocus={{ scale: 1.02 }}
            />
          </motion.div>

          {/* Event Time */}
          <motion.div variants={itemVariants} className="space-y-3">
            <label className="block text-sm font-bold text-gray-700 mb-2">⏰ Event Time</label>
            <motion.input
              type="time"
              name="event_time"
              value={eventForm.event_time}
              onChange={handleInputChange}
              className="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:border-red-500 focus:ring-4 focus:ring-red-100 transition-all duration-300 shadow-sm hover:shadow-md"
              required
              whileFocus={{ scale: 1.02 }}
            />
          </motion.div>

          {/* Language */}
          <motion.div variants={itemVariants} className="space-y-3">
            <label className="block text-sm font-bold text-gray-700 mb-2">🌐 Language</label>
            <motion.select
              name="language"
              value={eventForm.language}
              onChange={handleInputChange}
              className="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:border-red-500 focus:ring-4 focus:ring-red-100 transition-all duration-300 bg-white shadow-sm hover:shadow-md"
              required
              whileFocus={{ scale: 1.02 }}
            >
              <option value="">Select Language</option>
              <option value="english">English</option>
              <option value="hindi">Hindi</option>
              <option value="other">Other</option>
            </motion.select>
          </motion.div>
        </motion.div>

        {/* File Upload Section */}
        <motion.div variants={itemVariants} className="border-t-2 border-red-100 pt-8">
          <motion.h3
            className="text-xl font-bold mb-6 text-gray-800 flex items-center"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
          >
            <span className="mr-3">📎</span>
            Upload Documents (Optional)
          </motion.h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <motion.div variants={itemVariants} whileHover={{ scale: 1.02 }} className="space-y-4">
              <label className="block text-sm font-bold text-gray-700">📄 Event Content Document</label>
              <div className="relative">
                <input
                  id="eventContentFile"
                  type="file"
                  accept=".pdf,.ppt,.pptx,.doc,.docx,.txt,.xls,.xlsx"
                  onChange={(e) => handleFileChange(e, setEventContentFile, "event_content_document")}
                  className="w-full px-4 py-4 border-2 border-dashed border-red-200 rounded-xl focus:border-red-500 transition-all duration-300 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:text-white hover:file:bg-red-700 cursor-pointer hover:bg-red-50"
                  style={{
                    "--file-bg": "#7d1e1c",
                    "file:background-color": "var(--file-bg)",
                  }}
                  ref={eventContentFileRef}
                />
              </div>
              <p className="text-xs text-gray-500">
                Supported: PDF, PPT, PPTX, DOC, DOCX, TXT, XLS, XLSX
              </p>
              {eventContentFile && (
                <motion.p
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-sm text-green-600 bg-green-50 p-3 rounded-lg border border-green-200"
                >
                  ✓ Selected: {eventContentFile.name}
                </motion.p>
              )}
            </motion.div>

            <motion.div variants={itemVariants} whileHover={{ scale: 1.02 }} className="space-y-4">
              <label className="block text-sm font-bold text-gray-700">📝 Notes Document</label>
              <div className="relative">
                <input
                  id="notesFile"
                  type="file"
                  accept=".pdf,.ppt,.pptx,.doc,.docx,.txt,.xls,.xlsx"
                  onChange={(e) => handleFileChange(e, setNotesFile, "notes_document")}
                  className="w-full px-4 py-4 border-2 border-dashed border-red-200 rounded-xl focus:border-red-500 transition-all duration-300 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:text-white hover:file:bg-red-700 cursor-pointer hover:bg-red-50"
                  style={{
                    "--file-bg": "#7d1e1c",
                    "file:background-color": "var(--file-bg)",
                  }}
                  ref={notesFileRef}
                />
              </div>
              <p className="text-xs text-gray-500">
                Supported: PDF, PPT, PPTX, DOC, DOCX, TXT, XLS, XLSX
              </p>
              {notesFile && (
                <motion.p
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-sm text-green-600 bg-green-50 p-3 rounded-lg border border-green-200"
                >
                  ✓ Selected: {notesFile.name}
                </motion.p>
              )}
            </motion.div>
          </div>
        </motion.div>

        <motion.div variants={itemVariants} className="flex space-x-4 pt-6">
          <motion.button
            type="submit"
            disabled={
              isCreatingEvent ||
              isUpdatingEvent ||
              isCoursesLoading ||
              isBatchesLoading ||
              isKotaTeachersLoading ||
              isEventDetailsLoading
            }
            variants={buttonVariants}
            whileHover="hover"
            whileTap="tap"
            className="flex-1 text-white px-8 py-4 rounded-xl font-bold shadow-xl hover:shadow-2xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed text-lg"
            style={{
              background: "linear-gradient(135deg, #7d1e1c, #a52a2a)",
              boxShadow: "0 10px 25px rgba(125, 30, 28, 0.3)",
            }}
          >
            {isCreatingEvent || isUpdatingEvent ? (
              <span className="flex items-center justify-center">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="w-6 h-6 border-2 border-white border-t-transparent rounded-full mr-3"
                />
                Processing...
              </span>
            ) : isEditing ? (
              "✏️ Update Event"
            ) : (
              "➕ Create Event"
            )}
          </motion.button>
          {isEditing && (
            <motion.button
              type="button"
              onClick={resetForm}
              variants={buttonVariants}
              whileHover="hover"
              whileTap="tap"
              className="px-8 py-4 bg-gray-600 text-white rounded-xl font-bold shadow-xl hover:shadow-2xl hover:bg-gray-700 transition-all duration-300 text-lg"
            >
              ❌ Cancel Edit
            </motion.button>
          )}
        </motion.div>
      </form>
    </motion.div>
  );

  const renderFilters = () => (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      className="bg-white p-6 rounded-2xl shadow-xl border border-red-100 mb-6"
    >
      <motion.h3
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        className="text-xl font-bold mb-6 text-gray-800 flex items-center"
      >
        <span className="mr-3">🔍</span>
        Filter Events
      </motion.h3>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="grid grid-cols-1 md:grid-cols-6 gap-4"
      >
        <motion.select
          variants={itemVariants}
          name="course_id"
          value={filters.course_id}
          onChange={(e) => setFilters({ ...filters, course_id: e.target.value })}
          className="px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-red-500 focus:ring-4 focus:ring-red-100 transition-all duration-300 shadow-sm hover:shadow-md"
          disabled={isCoursesLoading}
        >
          <option value="">All Courses</option>
          {isCoursesLoading ? (
            <option>Loading...</option>
          ) : (
            courses?.map((course) => (
              <option key={course.course_id} value={course.course_id}>
                {course.course_name}
              </option>
            ))
          )}
        </motion.select>

        <motion.select
          variants={itemVariants}
          name="batch_id"
          value={filters.batch_id}
          onChange={(e) => setFilters({ ...filters, batch_id: e.target.value })}
          className="px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-red-500 focus:ring-4 focus:ring-red-100 transition-all duration-300 shadow-sm hover:shadow-md"
          disabled={isBatchesLoading}
        >
          <option value="">All Batches</option>
          {isBatchesLoading ? (
            <option>Loading...</option>
          ) : (
            batches?.map((batch) => (
              <option key={batch.batch_id} value={batch.batch_id}>
                {batch.batch_name}
              </option>
            ))
          )}
        </motion.select>

        <motion.input
          variants={itemVariants}
          type="text"
          name="subject"
          placeholder="Subject"
          value={filters.subject}
          onChange={(e) => setFilters({ ...filters, subject: e.target.value })}
          className="px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-red-500 focus:ring-4 focus:ring-red-100 transition-all duration-300 shadow-sm hover:shadow-md"
        />

        <motion.select
          variants={itemVariants}
          name="teacher_id"
          value={filters.teacher_id}
          onChange={(e) => setFilters({ ...filters, teacher_id: e.target.value })}
          className="px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-red-500 focus:ring-4 focus:ring-red-100 transition-all duration-300 shadow-sm hover:shadow-md"
          disabled={isKotaTeachersLoading}
        >
          <option value="">All Teachers</option>
          {isKotaTeachersLoading ? (
            <option>Loading...</option>
          ) : (
            kotaTeachers?.map((teacher) => (
              <option key={teacher.id} value={teacher.id}>
                {teacher.full_name}
              </option>
            ))
          )}
        </motion.select>

        <motion.input
          variants={itemVariants}
          type="date"
          name="date_from"
          placeholder="From Date"
          value={filters.date_from}
          onChange={(e) => setFilters({ ...filters, date_from: e.target.value })}
          className="px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-red-500 focus:ring-4 focus:ring-red-100 transition-all duration-300 shadow-sm hover:shadow-md"
        />

        <motion.input
          variants={itemVariants}
          type="date"
          name="date_to"
          placeholder="To Date"
          value={filters.date_to}
          onChange={(e) => setFilters({ ...filters, date_to: e.target.value })}
          className="px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-red-500 focus:ring-4 focus:ring-red-100 transition-all duration-300 shadow-sm hover:shadow-md"
        />
      </motion.div>
      <motion.div variants={itemVariants} className="mt-6 flex space-x-4">
        <motion.button
          onClick={applyFilters}
          variants={buttonVariants}
          whileHover="hover"
          whileTap="tap"
          className="text-white px-6 py-3 rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300"
          style={{
            background: "linear-gradient(135deg, #7d1e1c, #a52a2a)",
            boxShadow: "0 4px 15px rgba(125, 30, 28, 0.3)",
          }}
          disabled={isEventsLoading || isUpcomingEventsLoading}
        >
          🔍 Apply Filters
        </motion.button>
        <motion.button
          onClick={clearFilters}
          variants={buttonVariants}
          whileHover="hover"
          whileTap="tap"
          className="bg-gray-600 text-white px-6 py-3 rounded-xl font-bold shadow-lg hover:shadow-xl hover:bg-gray-700 transition-all duration-300"
        >
          🗑️ Clear Filters
        </motion.button>
      </motion.div>
    </motion.div>
  );

  const renderEventsList = () => (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      className="bg-white rounded-2xl shadow-xl border border-red-100 overflow-hidden"
    >
      <div
        className="p-6 border-b border-red-100"
        style={{ background: "linear-gradient(135deg, #7d1e1c, #a52a2a)" }}
      >
        <h2 className="text-2xl font-bold text-white flex items-center">
          <span className="mr-3">
            {activeTab === "upcoming" ? "⏰" : "📋"}
          </span>
          {activeTab === "upcoming" ? "Upcoming Events" : "All Events"}
        </h2>
      </div>
      <div className="overflow-x-auto">
        {(isEventsLoading || isUpcomingEventsLoading) && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-16"
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              className="w-12 h-12 border-4 border-red-500 border-t-transparent rounded-full mx-auto mb-4"
            />
            <p className="text-gray-500 text-lg">Loading events...</p>
          </motion.div>
        )}
        <table className="w-full">
          <thead className="bg-red-50">
            <tr>
              <th className="px-6 py-4 text-left text-sm font-bold text-gray-700 uppercase tracking-wider">
                Event Details
              </th>
              <th className="px-6 py-4 text-left text-sm font-bold text-gray-700 uppercase tracking-wider">
                Teacher
              </th>
              <th className="px-6 py-4 text-left text-sm font-bold text-gray-700 uppercase tracking-wider">
                Date & Time
              </th>
              <th className="px-6 py-4 text-left text-sm font-bold text-gray-700 uppercase tracking-wider">
                Documents
              </th>
              <th className="px-6 py-4 text-left text-sm font-bold text-gray-700 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            <AnimatePresence>
              {(activeTab === "upcoming" ? upcomingEventsData : listedEvents)?.map(
                (event, index) => {
                  console.log("Event ID in list:", event.id);
                  return (
                    <motion.tr
                      key={event.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                      whileHover={{ backgroundColor: "#fef2f2", scale: 1.01 }}
                      className="hover:bg-red-50 transition-all duration-200"
                    >
                      <td className="px-6 py-6">
                        <div className="space-y-2">
                          <div className="font-bold text-gray-900 text-base">
                            {event.course_name} - {event.subject}
                          </div>
                          <div className="text-gray-600 text-sm">
                            📚 Batch: {event.batch_name}
                          </div>
                          <div className="text-gray-600 text-sm">
                            📖 {event.chapter_name} - {event.topic}
                          </div>
                          <div className="text-red-600 text-sm font-semibold">
                            🎯 Lecture #{event.lecture_number}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-6 text-sm text-gray-900 font-semibold">
                        👨‍🏫 {event.kota_teacher_name}
                      </td>
                      <td className="px-6 py-6 text-sm text-gray-900">
                        <div className="space-y-1">
                          <div className="font-semibold">
                            📅 {new Date(event.event_date).toLocaleDateString()}
                          </div>
                          <div className="text-gray-600">⏰ {event.event_time}</div>
                        </div>
                      </td>
                      <td className="px-6 py-6 text-sm">
                        <div className="space-y-3">
                          {event.event_content_document_url && (
                            <div className="flex items-center space-x-2">
                              <motion.button
                                onClick={() =>
                                  handleDownloadDocument(event.id, "event_content")
                                }
                                variants={buttonVariants}
                                whileHover="hover"
                                whileTap="tap"
                                className="text-white text-xs px-3 py-2 rounded-lg font-semibold transition-all duration-200 shadow-md hover:shadow-lg"
                                style={{
                                  background:
                                    "linear-gradient(135deg, #7d1e1c, #a52a2a)",
                                }}
                              >
                                📄 Content
                              </motion.button>
                              <motion.button
                                onClick={() =>
                                  handleDeleteDocuments(event.id, "event_content")
                                }
                                variants={buttonVariants}
                                whileHover="hover"
                                whileTap="tap"
                                className="text-red-600 hover:text-red-800 text-xs bg-red-50 hover:bg-red-100 px-2 py-1 rounded-md transition-colors duration-200"
                              >
                                ✕
                              </motion.button>
                            </div>
                          )}
                          {event.notes_document_url && (
                            <div className="flex items-center space-x-2">
                              <motion.button
                                onClick={() =>
                                  handleDownloadDocument(event.id, "notes")
                                }
                                variants={buttonVariants}
                                whileHover="hover"
                                whileTap="tap"
                                className="text-white text-xs px-3 py-2 rounded-lg font-semibold transition-all duration-200 shadow-md hover:shadow-lg"
                                style={{
                                  background:
                                    "linear-gradient(135deg, #7d1e1c, #a52a2a)",
                                }}
                              >
                                📝 Notes
                              </motion.button>
                              <motion.button
                                onClick={() =>
                                  handleDeleteDocuments(event.id, "notes")
                                }
                                variants={buttonVariants}
                                whileHover="hover"
                                whileTap="tap"
                                className="text-red-600 hover:text-red-800 text-xs bg-red-50 hover:bg-red-100 px-2 py-1 rounded-md transition-colors duration-200"
                              >
                                ✕
                              </motion.button>
                            </div>
                          )}
                          {!event.event_content_document_url &&
                            !event.notes_document_url && (
                              <span className="text-gray-400 text-xs italic">
                                📭 No documents
                              </span>
                            )}
                        </div>
                      </td>
                      <td className="px-6 py-6 text-sm">
                        <div className="flex space-x-2">
                          <motion.button
                            onClick={() => viewEventDetails(event)}
                            variants={buttonVariants}
                            whileHover="hover"
                            whileTap="tap"
                            className="text-white px-4 py-2 rounded-lg text-xs font-semibold transition-all duration-200 shadow-md hover:shadow-lg"
                            style={{
                              background:
                                "linear-gradient(135deg, #7d1e1c, #a52a2a)",
                            }}
                          >
                            👁️ View
                          </motion.button>
                          <motion.button
                            onClick={() => handleEditEvent(event.id)}
                            variants={buttonVariants}
                            whileHover="hover"
                            whileTap="tap"
                            className="text-yellow-700 bg-yellow-100 hover:bg-yellow-200 px-4 py-2 rounded-lg text-xs font-semibold transition-all duration-200 shadow-md hover:shadow-lg"
                          >
                            ✏️ Edit
                          </motion.button>
                          <motion.button
                            onClick={() => handleDeleteEvent(event.id)}
                            variants={buttonVariants}
                            whileHover="hover"
                            whileTap="tap"
                            className="text-red-700 bg-red-100 hover:bg-red-200 px-4 py-2 rounded-lg text-xs font-semibold transition-all duration-200 shadow-md hover:shadow-lg"
                            disabled={isDeletingEvent}
                          >
                            🗑️ Delete
                          </motion.button>
                        </div>
                      </td>
                    </motion.tr>
                  );
                }
              )}
            </AnimatePresence>
          </tbody>
        </table>
        {(activeTab === "upcoming" ? upcomingEventsData?.length === 0 : listedEvents?.length === 0) &&
          !isEventsLoading &&
          !isUpcomingEventsLoading && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-16"
            >
              <div className="text-8xl mb-6">📅</div>
              <p className="text-gray-500 text-xl font-semibold">No events found</p>
              <p className="text-gray-400 text-sm mt-2">
                Try adjusting your filters or create a new event
              </p>
            </motion.div>
          )}
      </div>
    </motion.div>
  );

  const renderEventDetails = () => (
    <AnimatePresence>
      {showEventDetails && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 50 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 50 }}
            transition={{ duration: 0.3 }}
            className="bg-white rounded-3xl shadow-2xl w-full max-w-5xl max-h-[90vh] overflow-y-auto"
          >
            <div
              className="sticky top-0 p-8 border-b border-red-100 rounded-t-3xl"
              style={{ background: "linear-gradient(135deg, #7d1e1c, #a52a2a)" }}
            >
              <div className="flex justify-between items-center">
                <h3 className="text-3xl font-bold text-white flex items-center">
                  <span className="mr-4">📋</span>
                  Event Details
                </h3>
                <motion.button
                  onClick={() => {
                    setShowEventDetails(false);
                    setPdfPreview(null); // Clear preview on close
                    if (pdfPreview?.url) URL.revokeObjectURL(pdfPreview.url); // Clean up memory
                  }}
                  whileHover={{ scale: 1.1, rotate: 90 }}
                  whileTap={{ scale: 0.9 }}
                  className="text-white hover:text-red-200 bg-white bg-opacity-20 rounded-full p-3 shadow-lg transition-all duration-200"
                >
                  <span className="text-2xl">✕</span>
                </motion.button>
              </div>
            </div>

            {selectedEvent && (
              <div className="p-8">
                <motion.div
                  variants={containerVariants}
                  initial="hidden"
                  animate="visible"
                  className="grid grid-cols-1 md:grid-cols-2 gap-8"
                >
                  <motion.div variants={itemVariants} className="space-y-3">
                    <label className="block text-sm font-bold text-gray-700">📚 Course</label>
                    <p className="text-gray-900 bg-red-50 p-4 rounded-xl border border-red-100 font-semibold">
                      {selectedEvent.course_name}
                    </p>
                  </motion.div>
                  <motion.div variants={itemVariants} className="space-y-3">
                    <label className="block text-sm font-bold text-gray-700">👥 Batch</label>
                    <p className="text-gray-900 bg-blue-50 p-4 rounded-xl border border-blue-100 font-semibold">
                      {selectedEvent.batch_name}
                    </p>
                  </motion.div>
                  <motion.div variants={itemVariants} className="space-y-3">
                    <label className="block text-sm font-bold text-gray-700">📖 Subject</label>
                    <p className="text-gray-900 bg-green-50 p-4 rounded-xl border border-green-100 font-semibold">
                      {selectedEvent.subject}
                    </p>
                  </motion.div>
                  <motion.div variants={itemVariants} className="space-y-3">
                    <label className="block text-sm font-bold text-gray-700">📑 Chapter</label>
                    <p className="text-gray-900 bg-yellow-50 p-4 rounded-xl border border-yellow-100 font-semibold">
                      {selectedEvent.chapter_name}
                    </p>
                  </motion.div>
                  <motion.div variants={itemVariants} className="space-y-3">
                    <label className="block text-sm font-bold text-gray-700">💡 Topic</label>
                    <p className="text-gray-900 bg-purple-50 p-4 rounded-xl border border-purple-100 font-semibold">
                      {selectedEvent.topic}
                    </p>
                  </motion.div>
                  <motion.div variants={itemVariants} className="space-y-3">
                    <label className="block text-sm font-bold text-gray-700">🔢 Lecture Number</label>
                    <p className="text-gray-900 bg-indigo-50 p-4 rounded-xl border border-indigo-100 font-semibold">
                      #{selectedEvent.lecture_number}
                    </p>
                  </motion.div>
                  <motion.div variants={itemVariants} className="space-y-3">
                    <label className="block text-sm font-bold text-gray-700">👨‍🏫 Teacher</label>
                    <p className="text-gray-900 bg-orange-50 p-4 rounded-xl border border-orange-100 font-semibold">
                      {selectedEvent.kota_teacher_name}
                    </p>
                  </motion.div>
                  <motion.div variants={itemVariants} className="space-y-3">
                    <label className="block text-sm font-bold text-gray-700">📅 Date</label>
                    <p className="text-gray-900 bg-pink-50 p-4 rounded-xl border border-pink-100 font-semibold">
                      {new Date(selectedEvent.event_date).toLocaleDateString()}
                    </p>
                  </motion.div>
                  <motion.div variants={itemVariants} className="space-y-3">
                    <label className="block text-sm font-bold text-gray-700">⏰ Time</label>
                    <p className="text-gray-900 bg-teal-50 p-4 rounded-xl border border-teal-100 font-semibold">
                      {selectedEvent.event_time}
                    </p>
                  </motion.div>
                  <motion.div variants={itemVariants} className="space-y-3">
                    <label className="block text-sm font-bold text-gray-700">🌐 Language</label>
                    <p className="text-gray-900 bg-cyan-50 p-4 rounded-xl border border-cyan-100 font-semibold capitalize">
                      {selectedEvent.language}
                    </p>
                  </motion.div>
                </motion.div>

                {(selectedEvent.event_content_document_url ||
                  selectedEvent.notes_document_url ||
                  pdfPreview) && (
                  <motion.div variants={itemVariants} className="border-t border-gray-200 pt-8 mt-8">
                    <label className="block text-xl font-bold text-gray-700 mb-6 flex items-center">
                      <span className="mr-3">📎</span>
                      Documents
                    </label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {pdfPreview && (
                        <motion.div
                          variants={itemVariants}
                          className="w-full h-[500px] border-2 border-red-200 rounded-2xl overflow-hidden"
                        >
                          <iframe
                            src={pdfPreview.url}
                            title="PDF Preview"
                            className="w-full h-full"
                            style={{ border: "none" }}
                          />
                          <p className="text-center text-gray-600 mt-2">
                            {pdfPreview.filename}
                          </p>
                        </motion.div>
                      )}
                      {selectedEvent.event_content_document_url &&
                        !pdfPreview && (
                          <motion.button
                            onClick={() =>
                              handleDownloadDocument(selectedEvent.id, "event_content")
                            }
                            variants={buttonVariants}
                            whileHover="hover"
                            whileTap="tap"
                            className="flex items-center justify-between w-full p-6 border-2 border-red-200 rounded-2xl hover:border-red-400 hover:bg-red-50 transition-all duration-300 shadow-lg hover:shadow-xl"
                          >
                            <div className="flex items-center">
                              <span className="text-4xl mr-4">📄</span>
                              <div className="text-left">
                                <div className="font-bold text-gray-900 text-lg">
                                  Event Content
                                </div>
                                <div className="text-sm text-gray-600">
                                  {selectedEvent.event_content_document_name ||
                                    "Document"}
                                </div>
                              </div>
                            </div>
                            <span className="text-red-600 text-2xl">↗</span>
                          </motion.button>
                        )}
                      {selectedEvent.notes_document_url && !pdfPreview && (
                        <motion.button
                          onClick={() =>
                            handleDownloadDocument(selectedEvent.id, "notes")
                          }
                          variants={buttonVariants}
                          whileHover="hover"
                          whileTap="tap"
                          className="flex items-center justify-between w-full p-6 border-2 border-red-200 rounded-2xl hover:border-red-400 hover:bg-red-50 transition-all duration-300 shadow-lg hover:shadow-xl"
                        >
                          <div className="flex items-center">
                            <span className="text-4xl mr-4">📝</span>
                            <div className="text-left">
                              <div className="font-bold text-gray-900 text-lg">
                                Notes
                              </div>
                              <div className="text-sm text-gray-600">
                                {selectedEvent.notes_document_name || "Document"}
                              </div>
                            </div>
                          </div>
                          <span className="text-red-600 text-2xl">↗</span>
                        </motion.button>
                      )}
                    </div>
                  </motion.div>
                )}
              </div>
            )}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50 p-6">
      <Toastify res={res} resClear={resClear} />

      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="max-w-7xl mx-auto space-y-8"
      >
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-12"
        >
          <motion.h1
            className="text-5xl font-bold mb-4"
            style={{
              background:
                "linear-gradient(135deg, #7d1e1c, #a52a2a, #d2691e)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              backgroundClip: "text",
            }}
          >
            🎓 Event Management System
          </motion.h1>
          <p className="text-gray-600 text-xl">
            Create, manage, and track educational events with ease
          </p>
        </motion.div>

        {/* Tab Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="bg-white rounded-3xl shadow-2xl p-3 border border-red-100"
        >
          <nav className="flex space-x-3">
            {[
              { key: "create", label: "Create Event", icon: "➕" },
              { key: "list", label: "All Events", icon: "📋" },
              { key: "upcoming", label: "Upcoming Events", icon: "⏰" },
            ].map((tab) => (
              <motion.button
                key={tab.key}
                onClick={() => setActiveTab(tab.key)}
                variants={tabVariants}
                animate={activeTab === tab.key ? "active" : "inactive"}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className={`flex-1 py-4 px-6 rounded-2xl font-bold text-sm transition-all duration-300 flex items-center justify-center space-x-3 ${
                  activeTab === tab.key
                    ? "text-white shadow-xl"
                    : "text-gray-600 hover:text-gray-800 hover:bg-red-50"
                }`}
                style={
                  activeTab === tab.key
                    ? {
                        background:
                          "linear-gradient(135deg, #7d1e1c, #a52a2a)",
                        boxShadow: "0 10px 25px rgba(125, 30, 28, 0.3)",
                      }
                    : {}
                }
              >
                <span className="text-lg">{tab.icon}</span>
                <span>{tab.label}</span>
              </motion.button>
            ))}
          </nav>
        </motion.div>

        {/* Tab Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
          >
            {activeTab === "create" && renderCreateEventForm()}
            {(activeTab === "list" || activeTab === "upcoming") && (
              <>
                {renderFilters()}
                {renderEventsList()}
              </>
            )}
          </motion.div>
        </AnimatePresence>

        {/* Event Details Modal */}
        {renderEventDetails()}
      </motion.div>
    </div>
  );
};

export default CreateEvents;