import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  ChevronDown,
  ChevronUp,
  Filter,
  Search,
  BarChart2,
  User,
  Users,
  BookOpen,
  TrendingUp,
  Frown,
  Smile
} from 'lucide-react';

const StudentAttendance = () => {
  const [activeTab, setActiveTab] = useState('daily');
  const [expandedStudent, setExpandedStudent] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    status: 'all',
    month: new Date().getMonth()
  });

  // Sample data
  const attendanceData = {
    daily: [
      { id: 1, name: '<PERSON>', status: 'present', time: '08:15 AM', course: 'Neet' },
      
    ],
    monthly: {
      present: 18,
      absent: 2,
      late: 3,
      excused: 2,
      trend: 'up'
    }
  };

  const statusColors = {
    present: 'bg-emerald-100 text-emerald-600',
    absent: 'bg-rose-100 text-rose-600',
    late: 'bg-amber-100 text-amber-600',
    excused: 'bg-blue-100 text-blue-600'
  };

  const statusIcons = {
    present: <CheckCircle size={16} />,
    absent: <XCircle size={16} />,
    late: <AlertCircle size={16} />,
    excused: <BookOpen size={16} />
  };

  const filteredStudents = attendanceData.daily.filter((student) => {
    const matchesSearch = student.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filters.status === 'all' || student.status === filters.status;
    return matchesSearch && matchesFilter;
  });

  return (
    <div className="p-6 max-w-6xl mx-auto">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 flex items-center gap-3">
          <Users className="text-indigo-600" size={28} />
          Student Attendance
        </h1>
        <p className="text-gray-500 mt-2">Track and manage student attendance records</p>
      </motion.div>

      {/* Stats Cards */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ staggerChildren: 0.1 }}>
        {[
          {
            title: 'Total Present',
            value: attendanceData.monthly.present,
            icon: <Smile className="text-emerald-600" size={20} />,
            bg: 'bg-emerald-50'
          },
          {
            title: 'Total Absent',
            value: attendanceData.monthly.absent,
            icon: <Frown className="text-rose-600" size={20} />,
            bg: 'bg-rose-50'
          },
          {
            title: 'Late Arrivals',
            value: attendanceData.monthly.late,
            icon: <Clock className="text-amber-600" size={20} />,
            bg: 'bg-amber-50'
          },
          {
            title: 'Attendance Trend',
            value: attendanceData.monthly.trend === 'up' ? 'Improving' : 'Declining',
            icon: (
              <TrendingUp
                className={
                  attendanceData.monthly.trend === 'up' ? 'text-emerald-600' : 'text-rose-600'
                }
                size={20}
              />
            ),
            bg: attendanceData.monthly.trend === 'up' ? 'bg-emerald-50' : 'bg-rose-50'
          }
        ].map((stat, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            whileHover={{ y: -5 }}
            className={`${stat.bg} p-5 rounded-xl shadow-sm border border-gray-100`}>
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm text-gray-500">{stat.title}</p>
                <p className="text-2xl font-semibold mt-1">{stat.value}</p>
              </div>
              <div className="p-2 rounded-lg bg-white shadow-xs">{stat.icon}</div>
            </div>
          </motion.div>
        ))}
      </motion.div>

      {/* Tabs and Filters */}
      <motion.div
        className="bg-white rounded-xl p-1 shadow-sm border border-gray-200 inline-flex mb-6"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2 }}>
        {['daily', 'monthly'].map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${activeTab === tab ? 'bg-indigo-100 text-indigo-700' : 'text-gray-500 hover:bg-gray-100'}`}>
            {tab.charAt(0).toUpperCase() + tab.slice(1)} View
          </button>
        ))}
      </motion.div>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
        className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search
            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
            size={18}
          />
          <input
            type="text"
            placeholder="Search students..."
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex gap-2">
          <select
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
            value={filters.status}
            onChange={(e) => setFilters({ ...filters, status: e.target.value })}>
            <option value="all">All Statuses</option>
            <option value="present">Present</option>
            <option value="absent">Absent</option>
            <option value="late">Late</option>
            <option value="excused">Excused</option>
          </select>
          <button className="px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg flex items-center gap-2 text-sm">
            <Filter size={16} />
            More Filters
          </button>
        </div>
      </motion.div>

      {/* Daily View */}
      {activeTab === 'daily' && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
          className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          {/* Table Header */}
          <div className="grid grid-cols-12 gap-4 p-4 border-b border-gray-200 bg-gray-50 text-sm font-medium text-gray-500">
            <div className="col-span-5">Student</div>
            <div className="col-span-3">Status</div>
            <div className="col-span-2">Time</div>
            <div className="col-span-2">Course</div>
          </div>

          {/* Student Rows */}
          {filteredStudents.length > 0 ? (
            filteredStudents.map((student) => (
              <motion.div
                key={student.id}
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                transition={{ duration: 0.3 }}
                className={`border-b border-gray-200 ${expandedStudent === student.id ? 'bg-gray-50' : ''}`}>
                <div
                  className="grid grid-cols-12 gap-4 p-4 items-center cursor-pointer"
                  onClick={() =>
                    setExpandedStudent(expandedStudent === student.id ? null : student.id)
                  }>
                  <div className="col-span-5 flex items-center gap-3">
                    <div className="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center">
                      <User className="text-indigo-600" size={16} />
                    </div>
                    <span className="font-medium">{student.name}</span>
                  </div>
                  <div className="col-span-3">
                    <span
                      className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium ${statusColors[student.status]}`}>
                      {statusIcons[student.status]}
                      {student.status.charAt(0).toUpperCase() + student.status.slice(1)}
                    </span>
                  </div>
                  <div className="col-span-2 text-sm text-gray-500">{student.time || '-'}</div>
                  <div className="col-span-2 text-sm text-gray-500">{student.course}</div>
                  <div className="flex justify-end">
                    {expandedStudent === student.id ? (
                      <ChevronUp size={18} />
                    ) : (
                      <ChevronDown size={18} />
                    )}
                  </div>
                </div>

                <AnimatePresence>
                  {expandedStudent === student.id && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.2 }}
                      className="px-4 pb-4">
                      <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-xs">
                        <h4 className="font-medium mb-2">Attendance Details</h4>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <p className="text-gray-500">Date</p>
                            <p>{new Date().toLocaleDateString()}</p>
                          </div>
                          <div>
                            <p className="text-gray-500">Status</p>
                            <p className="capitalize">{student.status}</p>
                          </div>
                          <div>
                            <p className="text-gray-500">Course</p>
                            <p>{student.course}</p>
                          </div>
                          <div>
                            <p className="text-gray-500">Teacher</p>
                            <p>Mr. Johnson</p>
                          </div>
                        </div>
                        {student.status === 'absent' && (
                          <button className="mt-3 text-sm text-indigo-600 hover:text-indigo-800">
                            + Add absence note
                          </button>
                        )}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            ))
          ) : (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="p-8 text-center">
              <p className="text-gray-500">No students found matching your criteria</p>
            </motion.div>
          )}
        </motion.div>
      )}

      {/* Monthly View */}
      {activeTab === 'monthly' && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
          className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex justify-between items-center mb-6">
            <h3 className="font-medium text-lg">Monthly Attendance Overview</h3>
            <select
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
              value={filters.month}
              onChange={(e) => setFilters({ ...filters, month: e.target.value })}>
              {Array.from({ length: 12 }, (_, i) => (
                <option key={i} value={i}>
                  {new Date(0, i).toLocaleString('default', { month: 'long' })}
                </option>
              ))}
            </select>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h4 className="font-medium mb-4 flex items-center gap-2">
                <BarChart2 size={18} />
                Attendance Chart
              </h4>
              <div className="h-64 bg-gray-50 rounded-lg border border-gray-200 flex items-center justify-center">
                <p className="text-gray-400">Chart visualization would appear here</p>
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-4 flex items-center gap-2">
                <Calendar size={18} />
                Attendance Summary
              </h4>
              <div className="space-y-4">
                {[
                  {
                    label: 'Present Days',
                    value: attendanceData.monthly.present,
                    color: 'bg-emerald-500'
                  },
                  {
                    label: 'Absent Days',
                    value: attendanceData.monthly.absent,
                    color: 'bg-rose-500'
                  },
                  {
                    label: 'Late Arrivals',
                    value: attendanceData.monthly.late,
                    color: 'bg-amber-500'
                  },
                  {
                    label: 'Excused Absences',
                    value: attendanceData.monthly.excused,
                    color: 'bg-blue-500'
                  }
                ].map((item, index) => (
                  <div key={index} className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span>{item.label}</span>
                      <span>{item.value} days</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`${item.color} h-2 rounded-full`}
                        style={{ width: `${(item.value / 30) * 100}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default StudentAttendance;
