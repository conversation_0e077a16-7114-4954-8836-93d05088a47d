import { createSlice } from '@reduxjs/toolkit';
import { directorApi } from '../../../../redux/api/api';

const initialState = {
  EvaluationData: null
};

export const createEvaluationApiSlice = directorApi.injectEndpoints({
  endpoints: (builder) => ({
    createEvaluationService: builder.mutation({
      query: (body) => ({
        url: '/analyze_performance',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Create Evaluation Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['CreateEvaluation']
    })
  })
});

const evaluationSlice = createSlice({
  name: 'evaluation',
  initialState,
  reducers: {
    setEvaluationData: (state, action) => {
      state.EvaluationData = action.payload;
    }
  }
});

export default evaluationSlice.reducer;
export const { setEvaluationData } = evaluationSlice.actions;
export const { useCreateEvaluationServiceMutation } = createEvaluationApiSlice;
