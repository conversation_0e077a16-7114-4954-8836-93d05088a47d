import { createSlice } from '@reduxjs/toolkit';
import { parentStudentScoresApi } from '../../../../redux/api/api';

const initialState = {
  parentStudentScores: null,
};

export const parentApiSlice = parentStudentScoresApi.injectEndpoints({
  endpoints: (builder) => ({
    getParentStudentScores: builder.query({
      query: () => ({
        url: '/parent-student-scores',
        method: 'GET',
        
       

      }),
      transformResponse: (response) => {
        console.log('Parent Student Scores Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['ParentStudentScores']
    })
  })
});

const overallPerformanceSlice = createSlice({
  name: 'overallPerformance',
  initialState,
  reducers: {
    setParentStudentScores: (state, action) => {
      state.parentStudentScores = action.payload;
    }
  }
});

export default overallPerformanceSlice.reducer;
export const { setParentStudentScores } = overallPerformanceSlice.actions;
export const { useGetParentStudentScoresQuery } = parentApiSlice;