'use client';

import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Building2, Mail, Phone, AlertCircle, Loader2, RefreshCw } from 'lucide-react';
import {
  useLazyStudentDetailsServiceQuery,
  setstudentDetailsData
} from '../studentsInfo/studentInfo.slice';

const CenterInfo = () => {
  const dispatch = useDispatch();

  const [triggerCenterDetails, { data, isLoading, isError, error }] =
    useLazyStudentDetailsServiceQuery();
  const center = data?.student;
  useEffect(() => {
    const fetchCenterData = async () => {
      try {
        const response = await triggerCenterDetails().unwrap();
        console.log('API Response:', response); // Debug: Log API response
        dispatch(setstudentDetailsData(response));
        console.log('Dispatched to Redux:', response.center); // Debug: Log dispatched center data
      } catch (err) {
        console.error('Failed to fetch center details:', err);
      }
    };

    fetchCenterData();
  }, [triggerCenterDetails, dispatch]);

  // Debug: Log center data when component renders
  console.log('Center Data in Component:', center);

  // Handle loading state
  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-[var(--color-parents)] rounded-2xl p-8 shadow-xl">
          <div className="bg-white/90 backdrop-blur-sm rounded-xl p-8">
            <div className="flex items-center justify-center space-x-4">
              <div className="relative">
                <Loader2 className="w-8 h-8 text-[var(--color-parents)] animate-spin" />
              </div>
              <div className="text-center">
                <p className="text-gray-700 font-semibold text-lg">Loading Center Information</p>
                <p className="text-gray-500 text-sm mt-1">
                  Please wait while we fetch the details...
                </p>
                <button
                  onClick={() => triggerCenterDetails()}
                  className="mt-4 inline-flex items-center space-x-2 bg-[var(--color-parents)] hover:bg-[var(--color-parents)] text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                  <RefreshCw className="w-4 h-4" />
                  <span>Retry</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Handle error state
  if (isError) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-gradient-to-br from-red-500 to-red-600 rounded-2xl p-8 shadow-xl">
          <div className="bg-white/95 backdrop-blur-sm rounded-xl p-8 text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <AlertCircle className="w-8 h-8 text-red-500" />
            </div>
            <h3 className="text-xl font-bold text-red-600 mb-2">
              Unable to Load Center Information
            </h3>
            <p className="text-red-500 mb-4">
              {error?.data?.message ||
                error?.message ||
                'An unexpected error occurred while fetching center details.'}
            </p>
            <button
              onClick={() => triggerCenterDetails()}
              className="inline-flex items-center space-x-2 bg-red-500 hover:bg-red-600 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200">
              <RefreshCw className="w-4 h-4" />
              <span>Retry</span>
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Handle no center data
  if (!center) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-gradient-to-br from-gray-400 to-gray-500 rounded-2xl p-8 shadow-xl">
          <div className="bg-white/95 backdrop-blur-sm rounded-xl p-8 text-center">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Building2 className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-600 mb-2">
              No Center Information Available
            </h3>
            <p className="text-gray-500">
              Center details are not currently available. Please try again later.
            </p>
            <button
              onClick={() => triggerCenterDetails()}
              className="mt-4 inline-flex items-center space-x-2 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
              <RefreshCw className="w-4 h-4" />
              <span>Retry</span>
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="relative overflow-hidden">
        {/* Gradient Background */}
        <div className="relative rounded-3xl shadow-2xl overflow-hidden bg-[var(--color-parents)]">
          {/* Animated Floating Circles */}
          <div className="absolute inset-0">
            <div className="absolute top-0 left-0 w-64 h-64 bg-white/10 rounded-full -translate-x-32 -translate-y-32 animate-pulse"></div>
            <div
              className="absolute top-1/2 right-0 w-40 h-40 bg-white/10 rounded-full translate-x-20 animate-bounce"
              style={{ animationDuration: '3s' }}></div>
            <div
              className="absolute bottom-0 left-1/3 w-24 h-24 bg-white/10 rounded-full translate-y-12 animate-pulse"
              style={{ animationDelay: '1s' }}></div>
          </div>

          <div className="relative z-10 p-6 sm:p-10">
            {/* Header */}
            <div className="text-center mb-5">
              <div className="inline-flex items-center justify-center w-16 sm:w-20 h-16 sm:h-20 bg-white/20 backdrop-blur-sm rounded-full mb-6">
                <Building2 className="w-8 sm:w-10 h-8 sm:h-10 text-white" />
              </div>
              <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-white mb-3">
                Training Center
              </h1>
              <div className="w-20 sm:w-32 h-1 bg-white/60 mx-auto rounded-full"></div>
              <p className="text-white/90 mt-3 sm:mt-4 text-base sm:text-lg">
                Comprehensive Center Information
              </p>
            </div>

            {/* Info Cards Section */}
            <div className="bg-white/95 backdrop-blur-lg rounded-2xl p-6 sm:p-8 shadow-xl">
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 sm:gap-8">
                {/* Card 1: Center Name */}
                <div className="group hover:scale-[1.03] transition-all duration-300">
                  <div className="bg-[var(--color-parents)] rounded-xl p-6 text-white shadow-lg">
                    <div className="flex items-center justify-between mb-4">
                      <div className="w-10 h-10 sm:w-12 sm:h-12 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center">
                        <Building2 className="w-5 sm:w-6 h-5 sm:h-6" />
                      </div>
                      <div className="w-6 sm:w-8 h-6 sm:h-8 bg-white/20 rounded-full"></div>
                    </div>
                    <h3 className="text-sm font-medium text-white/80 mb-2">Center Name</h3>
                    <p
                      className="text-lg font-bold break-words truncate sm:whitespace-normal"
                      title={center?.center_name || 'N/A'}>
                      {center?.center_name || 'Not Available'}
                    </p>
                  </div>
                </div>

                {/* Card 2: Email */}
                <div className="group hover:scale-[1.03] transition-all duration-300">
                  <div className="bg-[var(--color-parents)] rounded-xl p-6 text-white shadow-lg">
                    <div className="flex items-center justify-between mb-4">
                      <div className="w-10 h-10 sm:w-12 sm:h-12 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center">
                        <Mail className="w-5 sm:w-6 h-5 sm:h-6" />
                      </div>
                      <div className="w-6 sm:w-8 h-6 sm:h-8 bg-white/20 rounded-full"></div>
                    </div>
                    <h3 className="text-sm font-medium text-white/80 mb-2">Email Address</h3>
                    <a href={`mailto:${center?.center_email}`}>
                      <p
                        className="text-base sm:text-lg font-bold break-words truncate sm:whitespace-normal"
                        title={center?.center_email || 'N/A'}>
                        {center?.center_email || 'Not Available'}
                      </p>
                    </a>
                  </div>
                </div>

                {/* Card 3: Phone */}
                <div className="group hover:scale-[1.03] transition-all duration-300">
                  <div className="bg-[var(--color-parents)] rounded-xl p-6 text-white shadow-lg">
                    <div className="flex items-center justify-between mb-4">
                      <div className="w-10 h-10 sm:w-12 sm:h-12 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center">
                        <Phone className="w-5 sm:w-6 h-5 sm:h-6" />
                      </div>
                      <div className="w-6 sm:w-8 h-6 sm:h-8 bg-white/20 rounded-full"></div>
                    </div>
                    <h3 className="text-sm font-medium text-white/80 mb-2">Phone Number</h3>
                    <p
                      className="text-base sm:text-lg font-bold break-words truncate sm:whitespace-normal"
                      title={center?.center_phone || 'N/A'}>
                      {center?.center_phone || 'Not Available'}
                    </p>
                  </div>
                </div>
              </div>

              {/* Additional Info */}
              <div className="mt-8 p-5 sm:p-6 bg-[var(--color-parents)]/10 rounded-xl border-l-4 border-[var(--color-parents)]">
                <div className="flex flex-col sm:flex-row items-start sm:space-x-4 space-y-4 sm:space-y-0">
                  <div className="w-10 h-10 bg-[var(--color-parents)]/20 rounded-full flex items-center justify-center flex-shrink-0">
                    <AlertCircle className="w-5 h-5 text-[var(--color-parents)]" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800 text-base mb-1">
                      Center Information
                    </h4>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      This training center provides comprehensive educational services and support.
                      For any inquiries or additional information, please use the contact details
                      provided above.
                    </p>
                    <div className="mt-3">
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-[#10e7dc]/20 text-[var(--color-parents)]">
                        Active Center
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CenterInfo;
