import React, { useEffect } from 'react';
import { useLazyGetKotaTeachersQuery } from './mappedCenter.slice';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUniversity,
  faEnvelope,
  faPhone,
  faIdBadge,
  faUsers,
  faChalkboardTeacher,
  faSyncAlt,
  faCog,
  faInfoCircle,
  faMapMarkerAlt,
  faUserTie,
  faCalendarCheck
} from '@fortawesome/free-solid-svg-icons';

const MappedCenters = () => {
  const [trigger, { data, error, isLoading }] = useLazyGetKotaTeachersQuery();

  useEffect(() => {
    trigger();
  }, [trigger]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        when: 'beforeChildren'
      }
    }
  };

  const cardVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 10
      }
    },
    hover: {
      y: -5,
      boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)'
    }
  };

  const teacherVariants = {
    hidden: { x: -10, opacity: 0 },
    visible: (i) => ({
      x: 0,
      opacity: 1,
      transition: {
        delay: i * 0.05,
        type: 'spring',
        stiffness: 150
      }
    })
  };

  const shimmerVariants = {
    initial: { backgroundPosition: '-200% 0' },
    animate: {
      backgroundPosition: '200% 0',
      transition: {
        duration: 1.5,
        repeat: Infinity,
        repeatType: 'loop',
        ease: 'linear'
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header Section */}
        <motion.div
          className="mb-12 flex flex-col md:flex-row items-start md:items-center justify-between gap-4"
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            duration: 0.6,
            ease: [0.6, -0.05, 0.01, 0.99]
          }}>
          <div>
            <motion.h1
              className="text-4xl font-bold  text-[var(--color-teacher)] mb-2"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}>
              Center-Teacher Assignment
            </motion.h1>
            <motion.p
              className="text-lg text-gray-600"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}>
              Interactive overview of teachers assigned to training centers
            </motion.p>
          </div>
       
        </motion.div>

        {/* Loading State */}
        <AnimatePresence>
          {isLoading && (
            <motion.div
              className="text-center py-20"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}>
              <motion.div
                animate={{
                  rotate: 360,
                  scale: [1, 1.1, 1]
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  ease: 'easeInOut'
                }}>
                <FontAwesomeIcon
                  icon={faCog}
                  className="text-[var(--color-teacher)] text-5xl mb-6"
                />
              </motion.div>
              <motion.p
                className="text-[var(--color-teacher)] font-medium text-xl"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}>
                Loading assignment data...
              </motion.p>
              <motion.div
                className="mt-6 mx-auto w-64 h-2 bg-gray-200 rounded-full overflow-hidden"
                variants={shimmerVariants}
                initial="initial"
                animate="animate"
                style={{
                  background: 'linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)',
                  backgroundSize: '400% 100%'
                }}
              />
            </motion.div>
          )}
        </AnimatePresence>

        {/* Error State */}
        <AnimatePresence>
          {error && (
            <motion.div
              className="bg-red-50 border-l-4 border-red-500 p-4 rounded-lg mb-6"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{
                opacity: 1,
                scale: 1,
                transition: { type: 'spring', stiffness: 300 }
              }}
              exit={{ opacity: 0 }}>
              <div className="flex items-center">
                <motion.div
                  animate={{
                    rotate: [0, 10, -10, 0],
                    transition: { duration: 0.5 }
                  }}>
                  <FontAwesomeIcon icon={faInfoCircle} className="text-red-500 mr-3 text-xl" />
                </motion.div>
                <div>
                  <h3 className="font-bold text-red-700 text-lg">Error Loading Data</h3>
                  <p className="text-red-600">
                    {error.data?.message || error.message || 'Failed to fetch assignment data'}
                  </p>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Empty State */}
        <AnimatePresence>
          {!isLoading && !error && !data?.mapped_centers?.length && (
            <motion.div
              className="text-center py-20 bg-white rounded-xl shadow-sm"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{
                opacity: 1,
                scale: 1,
                transition: { type: 'spring', stiffness: 200 }
              }}>
              <motion.div
                animate={{
                  y: [0, -10, 0],
                  transition: {
                    duration: 3,
                    repeat: Infinity,
                    repeatType: 'reverse'
                  }
                }}>
                <FontAwesomeIcon
                  icon={faUniversity}
                  className="text-[var(--color-teacher)] text-5xl mb-6 opacity-70"
                />
              </motion.div>
              <h3 className="text-2xl font-medium text-gray-700 mb-2">No Centers Found</h3>
              <p className="text-gray-500 text-lg">
                No training centers with teacher assignments available
              </p>
              <motion.button
                onClick={() => trigger()}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="mt-6 px-6 py-2 bg-[var(--color-teacher)] text-white rounded-lg shadow">
                Try Again
              </motion.button>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Data Display */}
        {!isLoading && !error && data?.mapped_centers?.length > 0 && (
          <motion.div
            className="space-y-8"
            variants={containerVariants}
            initial="hidden"
            animate="visible">
            {data.mapped_centers.map((center, index) => (
              <motion.div
                key={center.center_code || `center-${index}`}
                variants={cardVariants}
                whileHover="hover"
                className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200 transition-all">
                {/* Center Header */}
                <motion.div
                  className="bg-gradient-to-r from-[var(--color-teacher-light)] to-white p-6 border-b"
                  initial={{ backgroundPosition: '100% 50%' }}
                  animate={{ backgroundPosition: '0% 50%' }}
                  transition={{ duration: 1.5 }}>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center">
                      <motion.div
                        className="p-3 bg-[var(--color-teacher)] text-white rounded-lg mr-4"
                        whileHover={{ rotate: 15, scale: 1.1 }}
                        transition={{ type: 'spring' }}>
                        <FontAwesomeIcon icon={faUniversity} className="text-2xl" />
                      </motion.div>
                      <div>
                        <h3 className="text-2xl font-bold text-gray-800">{center.name}</h3>
                        <motion.div
                          className="flex items-center text-sm text-gray-600 mt-2"
                          whileHover={{ x: 5 }}>
                          <FontAwesomeIcon
                            icon={faMapMarkerAlt}
                            className="mr-2 text-[var(--color-teacher)]"
                          />
                          <span>{center.location || 'Location not specified'}</span>
                        </motion.div>
                      </div>
                    </div>
                  
                  </div>
                </motion.div>

                {/* Center Details */}
                <div className="p-6 grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2 }}>
                    <h4 className="font-medium text-gray-700 mb-4 flex items-center text-lg">
                      <motion.span
                        animate={{ rotate: [0, 360] }}
                        transition={{ duration: 1, delay: 1 }}>
                        <FontAwesomeIcon
                          icon={faIdBadge}
                          className="text-[var(--color-teacher)] mr-3"
                        />
                      </motion.span>
                      Center Information
                    </h4>
                    <ul className="space-y-3 text-gray-600">
                      <motion.li
                        className="flex items-center py-2 border-b border-gray-100"
                        whileHover={{ x: 5 }}>
                        <span className="w-28 font-medium">Code:</span>
                        <span className="bg-gray-100 px-3 py-1 rounded-md font-mono">
                          {center.center_code || 'N/A'}
                        </span>
                      </motion.li>
                      <motion.li
                        className="flex items-center py-2 border-b border-gray-100"
                        whileHover={{ x: 5 }}>
                        <span className="w-28 font-medium">Email:</span>
                        <a
                          href={`mailto:${center.email}`}
                          className="text-[var(--color-teacher)] hover:underline">
                          {center.email || 'N/A'}
                        </a>
                      </motion.li>
                      <motion.li className="flex items-center py-2" whileHover={{ x: 5 }}>
                        <span className="w-28 font-medium">Phone:</span>
                        <a
                          href={`tel:${center.phone}`}
                          className="text-[var(--color-teacher)] hover:underline">
                          {center.phone || 'N/A'}
                        </a>
                      </motion.li>
                    </ul>
                  </motion.div>

              
                </div>
              </motion.div>
            ))}
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default MappedCenters;
