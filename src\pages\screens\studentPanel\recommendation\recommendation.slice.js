import { createSlice } from '@reduxjs/toolkit';
import { recommendationApi } from '../../../../redux/api/api';

const initialState = {
  recommendationData: null,
  recommendationPendingData: null
};

export const recommendationApiSlice = recommendationApi.injectEndpoints({
  endpoints: (builder) => ({
    getRecommendations: builder.query({
      query: (query) => {
        return `/missions/playlist/${query.user_id}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      })
    }),
    getRecommendationsById: builder.query({
      query: (query) => {
        return `/missions/content/${query.mission_id}?user_id=${query.user_id}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      })
    })
  })
});

const recommendationSlice = createSlice({
  name: 'recommendation',
  initialState,
  reducers: {
    setRecommendationData: (state, action) => {
      state.recommendationData = action.payload;
    },
    clearRecommendationData: (state) => {
      state.recommendationData = null;
    },
    setRecommendationPendingData: (state, action) => {
      state.recommendationPendingData = action.payload;
    },
    clearRecommendationPendingData: (state) => {
      state.recommendationPendingData = null;
    }
  }
});

export const { useLazyGetRecommendationsQuery, useLazyGetRecommendationsByIdQuery } = recommendationApiSlice;
export const {
  setRecommendationData,
  clearRecommendationData,
  setRecommendationPendingData,
  clearRecommendationPendingData
} = recommendationSlice.actions;
export const selectRecommendationData = (state) => state.recommendation.recommendationData;
export default recommendationSlice.reducer;
