import { createSlice } from '@reduxjs/toolkit';
import { directorApi } from '../../../../redux/api/api';

const initialState = {
  batchStudents: null,
  studentEvaluations: null
};

export const studentApiSlice = directorApi.injectEndpoints({
  endpoints: (builder) => ({
    listBatchStudents: builder.query({
      query: () => ({
        url: '/list-batch-students',
        method: 'GET',
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('List Batch Students Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['ListBatchStudents']
    }),
    getStudentEvaluations: builder.query({
      query: (studentId) => ({
        url: `/get_student_evaluations?student_id=${studentId}`,
        method: 'GET',
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Get Student Evaluations Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['GetStudentEvaluations']
    })
  })
});

const studentSlice = createSlice({
  name: 'student',
  initialState,
  reducers: {
    setBatchStudents: (state, action) => {
      state.batchStudents = action.payload;
    },
    setStudentEvaluations: (state, action) => {
      state.studentEvaluations = action.payload;
    }
  }
});

export default studentSlice.reducer;
export const { setBatchStudents, setStudentEvaluations } = studentSlice.actions;
export const { 
  useListBatchStudentsQuery,
  useGetStudentEvaluationsQuery
} = studentApiSlice;