'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence, useScroll, useTransform } from 'framer-motion';
import {
  FaStar,
  FaChevronLeft,
  FaChevronRight,
  FaQuoteLeft,
  FaUsers,
  FaTrophy,
  FaBookOpen,
  FaBullseye,
  FaVideo,
  FaRobot,
  FaUserTie,
  FaClipboardList,
  FaChartLine,
  FaGamepad,
  FaAtom,
  FaDna,
  FaMicroscope,
  FaStethoscope,
  FaGraduationCap,
  FaMedal,
  FaLightbulb,
  FaHeart
} from 'react-icons/fa';
import { GiDna2, GiMolecule, GiChemicalDrop, GiMedicalPack, GiBookshelf } from 'react-icons/gi';
import { MdLocalHospital } from 'react-icons/md';
import BlurText from '../../components/AceternityUi/Demo/BlurText';
import RotatingText from '../../components/AceternityUi/Demo/RotatingText';

const NeetPrepHome = () => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const { scrollY } = useScroll();
  const y1 = useTransform(scrollY, [0, 300], [0, -50]);
  const y2 = useTransform(scrollY, [0, 300], [0, 50]);
  const y3 = useTransform(scrollY, [300, 600], [0, -30]);

  const features = [
    {
      title: 'Streaming Classes',
      description: 'Live, interactive sessions with top NEET faculty to master tough topics.',
      icon: <FaVideo className="w-8 h-8" />
    },
    {
      title: 'AI Study Partner',
      description: 'Adaptive assistant with personalized tips, concept guides, and smart practice.',
      icon: <FaRobot className="w-8 h-8" />
    },
    {
      title: 'Mentor Connect',
      description: '1:1 guidance from NEET toppers and experts throughout your prep.',
      icon: <FaUserTie className="w-8 h-8" />
    },
    {
      title: 'Mock Exams',
      description: 'Real-time tests to compete, climb leaderboards, and build exam confidence.',
      icon: <FaClipboardList className="w-8 h-8" />
    },
    {
      title: 'Dynamic Progress Chart',
      description: 'AI analytics to track weak areas, monitor growth, and customize plans.',
      icon: <FaChartLine className="w-8 h-8" />
    },
    {
      title: 'Gamified Learning',
      description: 'Earn badges, unlock rewards, and stay motivated on your learning journey.',
      icon: <FaGamepad className="w-8 h-8" />
    }
  ];

  const testimonials = [
    {
      id: 1,
      name: 'Priya Sharma',
      score: '685/720',
      college: 'AIIMS Delhi',
      image:
        'https://thumbs.dreamstime.com/b/young-indian-medical-student-clinic-practice-female-doctor-white-coat-stethoscope-indian-woman-doctor-211589948.jpg',
      quote:
        'The AI-powered study sessions completely transformed my preparation. I went from struggling with Biology to scoring 95% in my final attempt!',
      subject: 'Biology Expert'
    },
    {
      id: 2,
      name: 'Arjun Mehra',
      score: '672/720',
      college: 'JIPMER Puducherry',
      image: 'https://i.pinimg.com/736x/26/1a/54/261a547bce7c810ebaa5a2291edb1571.jpg',
      quote:
        'The personalized learning approach helped me identify my weak areas in Chemistry. The mock exams were exactly like the real NEET!',
      subject: 'Chemistry Topper'
    },
    {
      id: 3,
      name: 'Sneha Kapoor',
      score: '658/720',
      college: 'MAMC Delhi',
      image:
        'https://t3.ftcdn.net/jpg/08/30/71/92/360_F_830719276_zH1N4mVRUOOna9DBdm2DLMbigaspyvyl.jpg',
      quote:
        'Interactive live classes and instant doubt clearing made Physics so much easier. I never thought I could score this high!',
      subject: 'Physics Achiever'
    },
    {
      id: 4,
      name: 'Rohit Kumar',
      score: '645/720',
      college: 'KGMU Lucknow',
      image:
        'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8N3x8ZG9jdG9yfGVufDB8fDB8fHww',
      quote:
        'The gamified learning system kept me motivated throughout my preparation. Earning badges and competing with peers was amazing!',
      subject: 'All-Rounder'
    }
  ];

  const stats = [
    { number: '75,000+', label: 'Students Enrolled', icon: FaUsers },
    { number: '95%', label: 'Success Rate', icon: FaTrophy },
    { number: '500+', label: 'AIIMS Selections', icon: FaBullseye },
    { number: '24/7', label: 'AI Support', icon: FaBookOpen }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 5000);
    return () => clearInterval(timer);
  }, []);

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  // Back button handler
  const goBack = () => {
    window.history.back();
  };

  return (
    <div className="min-h-screen bg-white text-gray-800 overflow-hidden relative">
      {/* Back Button */}
      <motion.button
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5 }}
        onClick={goBack}
        className="absolute top-6 left-6 z-50 bg-white hover:bg-[#FFC107]/20 text-gray-800 hover:text-[#FFC107] p-3 rounded-full shadow-lg transition-all duration-300 border border-gray-200 hover:border-[#FFC107]">
        <FaChevronLeft className="w-6 h-6" />
      </motion.button>

      {/* Indian Pattern Background */}
      <div className="fixed inset-0 pointer-events-none z-0">
        {/* Mandala Pattern */}
        <motion.div className="absolute top-20 right-20 opacity-5" style={{ y: y1 }}>
          <svg width="200" height="200" viewBox="0 0 200 200" className="text-[#FFC107]">
            <circle cx="100" cy="100" r="80" fill="none" stroke="currentColor" strokeWidth="2" />
            <circle cx="100" cy="100" r="60" fill="none" stroke="currentColor" strokeWidth="1" />
            <circle cx="100" cy="100" r="40" fill="none" stroke="currentColor" strokeWidth="1" />
            <circle cx="100" cy="100" r="20" fill="none" stroke="currentColor" strokeWidth="1" />
            {[...Array(8)].map((_, i) => (
              <line
                key={i}
                x1="100"
                y1="20"
                x2="100"
                y2="180"
                stroke="currentColor"
                strokeWidth="1"
                transform={`rotate(${i * 45} 100 100)`}
              />
            ))}
          </svg>
        </motion.div>

        {/* Paisley Pattern */}
        <motion.div className="absolute bottom-40 left-20 opacity-5" style={{ y: y2 }}>
          <svg width="150" height="150" viewBox="0 0 150 150" className="text-[#0D2A4B]">
            <path
              d="M75 25 Q125 25 125 75 Q125 125 75 125 Q50 125 50 100 Q50 75 75 75 Q100 75 100 50 Q100 25 75 25 Z"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
            />
            <path
              d="M75 40 Q110 40 110 75 Q110 110 75 110 Q60 110 60 95 Q60 80 75 80 Q90 80 90 65 Q90 50 75 50 Z"
              fill="none"
              stroke="currentColor"
              strokeWidth="1"
            />
          </svg>
        </motion.div>

        {/* Lotus Pattern */}
        <motion.div className="absolute top-1/2 left-10 opacity-5" style={{ y: y3 }}>
          <svg width="120" height="120" viewBox="0 0 120 120" className="text-[#FFC107]">
            {[...Array(8)].map((_, i) => (
              <ellipse
                key={i}
                cx="60"
                cy="60"
                rx="40"
                ry="15"
                fill="none"
                stroke="currentColor"
                strokeWidth="1"
                transform={`rotate(${i * 45} 60 60)`}
              />
            ))}
            <circle cx="60" cy="60" r="10" fill="none" stroke="currentColor" strokeWidth="2" />
          </svg>
        </motion.div>

        {/* Floating Medical Icons */}
        <motion.div
          className="absolute top-32 left-1/4 text-4xl opacity-10 text-[#FFC107]"
          style={{ y: y1 }}>
          <GiDna2 />
        </motion.div>

        <motion.div
          className="absolute bottom-32 right-1/4 text-3xl opacity-10 text-[#0D2A4B]"
          style={{ y: y2 }}>
          <FaAtom />
        </motion.div>

        <motion.div
          className="absolute top-1/2 right-20 text-3xl opacity-10 text-[#FFC107]"
          style={{ y: y3 }}>
          <GiMolecule />
        </motion.div>

        <motion.div
          className="absolute bottom-1/4 left-1/3 text-2xl opacity-10 text-[#0D2A4B]"
          style={{ y: y1 }}>
          <FaMicroscope />
        </motion.div>

        {/* Geometric Doodles */}
        <motion.div className="absolute top-60 right-1/3 opacity-5" style={{ y: y2 }}>
          <svg width="100" height="100" viewBox="0 0 100 100" className="text-[#FFC107]">
            <polygon points="50,10 90,90 10,90" fill="none" stroke="currentColor" strokeWidth="2" />
            <polygon points="50,25 75,75 25,75" fill="none" stroke="currentColor" strokeWidth="1" />
          </svg>
        </motion.div>
      </div>

      {/* Hero Section */}
      <div className="relative z-10">
        <div className="absolute inset-0">
          <img
            src="https://frontend-sasthra-demo-images-dsais.s3.eu-north-1.amazonaws.com/s3_imgs_sasthra/3.jpg"
            className="w-full h-full object-cover"
            alt="Students studying for NEET"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/50 to-black/60"></div>

          {/* Indian Border Pattern */}
          <div className="absolute inset-0">
            <div className="absolute top-0 left-0 right-0 h-2 bg-gradient-to-r from-[#FF9933] via-white to-[#138808]"></div>
            <div className="absolute bottom-0 left-0 right-0 h-2 bg-gradient-to-r from-[#FF9933] via-white to-[#138808]"></div>
          </div>
        </div>

        <div className="relative bg-black/20">
          {/* Decorative Wave */}
          <div className="absolute inset-x-0 bottom-0">
            <svg
              viewBox="0 0 224 12"
              fill="currentColor"
              className="w-full -mb-1 text-white"
              preserveAspectRatio="none">
              <path d="M0,0 C48.8902582,6.27314026 86.2235915,9.40971039 112,9.40971039 C137.776408,9.40971039 175.109742,6.27314026 224,0 L224,12.0441132 L0,12.0441132 L0,0 Z" />
            </svg>
          </div>

          <div className="px-4 py-20 mx-auto sm:max-w-xl md:max-w-full lg:max-w-screen-xl md:px-24 lg:px-8 lg:py-28">
            <div className="relative max-w-4xl mx-auto text-center">
              {/* Indian Decorative Element */}
              <div className="flex justify-center mb-6">
                <div className="flex items-center space-x-2">
                  <GiMedicalPack className="text-[#FFC107] text-2xl" />
                  <div className="w-16 h-0.5 bg-gradient-to-r from-[#FF9933] to-[#FFC107]"></div>
                  <FaStethoscope className="text-[#FFC107] text-2xl" />
                  <div className="w-16 h-0.5 bg-gradient-to-r from-[#FFC107] to-[#138808]"></div>
                  <FaGraduationCap className="text-[#FFC107] text-2xl" />
                </div>
              </div>

              <h1 className="mb-8 font-extrabold text-white text-4xl md:text-6xl leading-tight flex justify-center items-center flex-wrap">
                <BlurText
                  text="Transform Your NEET Preparation —"
                  delay={150}
                  animateBy="words"
                  direction="top"
                  className="text-3xl md:text-4xl mb-4 md:mb-0"
                />

                <div className="relative inline-block mx-2 my-2">
                  <div className="absolute inset-0 transform -skew-x-12 bg-gradient-to-r from-[#FFC107] to-[#FF9933] shadow-lg"></div>
                  <RotatingText
                    texts={['Smarter', 'Faster', 'Personalized']}
                    staggerFrom="last"
                    initial={{ y: '100%' }}
                    animate={{ y: 0 }}
                    exit={{ y: '-120%' }}
                    staggerDuration={0.025}
                    splitLevelClassName="overflow-hidden pb-0.5 sm:pb-1 md:pb-1 px-4 text-[#0D2A4B] font-bold"
                    transition={{ type: 'spring', damping: 30, stiffness: 400 }}
                    rotationInterval={2000}
                  />
                </div>
              </h1>

              <p className="mb-8 text-xl md:text-2xl text-gray-200 leading-relaxed max-w-4xl mx-auto">
                Join India's top AI-powered NEET platform with Live Streaming Classes, Mentorship,
                AI Study Partner, Mock Battles, & Dynamic Progress Charts — designed for your NEET
                success!
              </p>

              {/* Enhanced Stats with Indian Touch */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
                {stats.map((stat, index) => (
                  <div
                    key={stat.label}
                    className="bg-white/10 backdrop-blur-md rounded-xl p-6 text-center border border-white/20 shadow-lg">
                    <div className="flex justify-center mb-3">
                      <div className="bg-gradient-to-r from-[#FFC107] to-[#FF9933] p-3 rounded-full">
                        <stat.icon className="w-6 h-6 text-white" />
                      </div>
                    </div>
                    <div className="text-2xl md:text-3xl font-bold text-[#FFC107] mb-1">
                      {stat.number}
                    </div>
                    <div className="text-sm text-gray-300 font-medium">{stat.label}</div>
                  </div>
                ))}
              </div>

              {/* Scroll Indicator */}
              <a
                href="#features"
                aria-label="Scroll down"
                className="inline-flex items-center justify-center w-12 h-12 mx-auto text-white transition duration-300 transform border-2 border-white rounded-full hover:border-[#FFC107] hover:text-[#FFC107] hover:scale-110 group">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 12 12"
                  fill="currentColor">
                  <path d="M10.293,3.293,6,7.586,1.707,3.293A1,1,0,0,0,.293,4.707l5,5a1,1,0,0,0,1.414,0l5-5a1,1,0,1,0-1.414-1.414Z" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <section id="features" className="py-20 bg-gradient-to-b from-white to-gray-50 relative">
        {/* Indian Pattern Overlay */}
        <div className="absolute inset-0 opacity-5">
          <div
            className="w-full h-full"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fillRule='evenodd'%3E%3Cg fill='%23FFC107' fillOpacity='0.1'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm10 0c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
            }}></div>
        </div>

        <div className="max-w-6xl mx-auto px-4 relative z-10">
          {/* Section Header with Indian Design */}
          <div className="text-center mb-16">
            <div className="flex justify-center items-center mb-6">
              <div className="w-20 h-0.5 bg-gradient-to-r from-transparent to-[#FFC107]"></div>
              <FaLightbulb className="mx-4 text-3xl text-[#FFC107]" />
              <div className="w-20 h-0.5 bg-gradient-to-r from-[#FFC107] to-transparent"></div>
            </div>

            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
              Why Choose Our Platform?
            </h2>

            <div className="flex justify-center items-center space-x-2 mb-4">
              <div className="w-8 h-1 bg-[#FF9933] rounded-full"></div>
              <div className="w-12 h-1 bg-[#FFC107] rounded-full"></div>
              <div className="w-8 h-1 bg-[#138808] rounded-full"></div>
            </div>

            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Experience the future of NEET preparation with cutting-edge features designed for your
              success
            </p>
          </div>

          {/* Enhanced Features Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div
                key={feature.title}
                className="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-[#FFC107]/30 relative overflow-hidden">
                {/* Indian Corner Design */}
                <div className="absolute top-0 right-0 w-16 h-16 opacity-10">
                  <svg viewBox="0 0 64 64" className="w-full h-full text-[#FFC107]">
                    <path d="M0 0 L64 0 L64 64 Z" fill="currentColor" />
                  </svg>
                </div>

                <div className="relative z-10">
                  <div className="flex justify-center mb-6">
                    <div className="bg-gradient-to-br from-[#FFC107] to-[#FF9933] p-4 rounded-2xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                      <div className="text-white">{feature.icon}</div>
                    </div>
                  </div>

                  <h3 className="text-xl font-bold text-gray-800 mb-4 text-center group-hover:text-[#0D2A4B] transition-colors">
                    {feature.title}
                  </h3>

                  <p className="text-gray-600 leading-relaxed text-center">{feature.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Subject Progress Section */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-gray-100 relative">
        {/* Parallax Medical Icons */}
        <motion.div
          className="absolute top-20 left-20 text-6xl opacity-5 text-[#FFC107]"
          style={{ y: y1 }}>
          <FaDna />
        </motion.div>
        <motion.div
          className="absolute bottom-20 right-20 text-5xl opacity-5 text-[#0D2A4B]"
          style={{ y: y2 }}>
          <GiChemicalDrop />
        </motion.div>

        <div className="max-w-6xl mx-auto px-4 reślative z-10">
          <div className="text-center mb-16">
            <div className="flex justify-center items-center mb-6">
              <FaAtom className="text-3xl text-[#FFC107] mr-2" />
              <GiChemicalDrop className="text-3xl text-[#FF9933] mx-2" />
              <FaDna className="text-3xl text-[#FFC107] ml-2" />
            </div>

            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
              Master All NEET Subjects
            </h2>

            <div className="flex justify-center items-center space-x-2 mb-4">
              <div className="w-8 h-1 bg-[#FFC107] rounded-full"></div>
              <div className="w-12 h-1 bg-[#FF9933] rounded-full"></div>
              <div className="w-8 h-1 bg-[#FFC107] rounded-full"></div>
            </div>

            <p className="text-xl text-gray-600">
              Track your progress across Physics, Chemistry, and Biology with AI-powered analytics
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                name: 'Physics',
                progress: 85,
                color: '#FFC107',
                icon: <FaAtom className="w-8 h-8" />
              },
              {
                name: 'Chemistry',
                progress: 92,
                color: '#FF9933',
                icon: <GiChemicalDrop className="w-8 h-8" />
              },
              {
                name: 'Biology',
                progress: 88,
                color: '#FFC107',
                icon: <FaDna className="w-8 h-8" />
              }
            ].map((subject, index) => (
              <div
                key={subject.name}
                className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <div
                      className="p-3 rounded-xl text-white"
                      style={{ backgroundColor: subject.color }}>
                      {subject.icon}
                    </div>
                    <h4 className="font-bold text-xl text-gray-800">{subject.name}</h4>
                  </div>
                  <span className="text-2xl font-bold" style={{ color: subject.color }}>
                    {subject.progress}%
                  </span>
                </div>

                <div className="w-full bg-gray-200 rounded-full h-4 mb-4">
                  <div
                    className="h-4 rounded-full transition-all duration-1000 ease-out"
                    style={{
                      backgroundColor: subject.color,
                      width: `${subject.progress}%`
                    }}
                  />
                </div>

                <div className="flex justify-between text-sm text-gray-600">
                  <span>Beginner</span>
                  <span>Expert</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-white relative overflow-hidden">
        {/* Indian Pattern Background */}
        <div className="absolute inset-0 opacity-5">
          <div
            className="w-full h-full"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fillRule='evenodd'%3E%3Cg fill='%23FFC107' fillOpacity='0.1'%3E%3Cpath d='M40 40c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm20 0c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
            }}></div>
        </div>

        <div className="max-w-6xl mx-auto px-4 relative z-10">
          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="flex justify-center items-center mb-6">
              <FaHeart className="text-3xl text-[#FFC107] mr-2" />
              <FaMedal className="text-3xl text-[#FFC107] mx-2" />
              <FaTrophy className="text-3xl text-[#FFC107] ml-2" />
            </div>

            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
              Success Stories That Inspire
            </h2>

            <div className="flex justify-center items-center space-x-2 mb-4">
              <div className="w-8 h-1 bg-[#FF9933] rounded-full"></div>
              <div className="w-12 h-1 bg-[#FFC107] rounded-full"></div>
              <div className="w-8 h-1 bg-[#138808] rounded-full"></div>
            </div>

            <p className="text-xl text-gray-600">
              Real students, real results, real achievements from across India
            </p>
          </div>

          {/* Main Testimonial */}
          <div className="relative mb-16">
            <AnimatePresence mode="wait">
              <div
                key={currentTestimonial}
                className="bg-gradient-to-br from-white to-gray-50 rounded-3xl shadow-2xl p-8 md:p-12 max-w-4xl mx-auto relative border border-gray-100">
                {/* Indian Corner Decorations */}
                <div className="absolute top-0 left-0 w-20 h-20 opacity-10">
                  <svg viewBox="0 0 80 80" className="w-full h-full text-[#FFC107]">
                    <path d="M0 0 Q40 0 40 40 Q0 40 0 0" fill="currentColor" />
                  </svg>
                </div>

                <div className="absolute bottom-0 right-0 w-20 h-20 opacity-10">
                  <svg viewBox="0 0 80 80" className="w-full h-full text-[#FFC107]">
                    <path d="M80 80 Q40 80 40 40 Q80 40 80 80" fill="currentColor" />
                  </svg>
                </div>

                {/* Quote Icon */}
                <div className="absolute -top-6 left-8">
                  <div className="bg-gradient-to-r from-[#FFC107] to-[#FF9933] p-4 rounded-full shadow-lg">
                    <FaQuoteLeft className="w-6 h-6 text-white" />
                  </div>
                </div>

                <div className="flex flex-col md:flex-row items-center gap-8 pt-4">
                  <div className="flex-shrink-0">
                    <div className="relative">
                      <img
                        src={testimonials[currentTestimonial].image || '/placeholder.svg'}
                        alt={testimonials[currentTestimonial].name}
                        className="w-28 h-28 rounded-full border-4 border-[#FFC107] object-cover shadow-lg"
                      />
                      {/* Achievement Badge */}
                      <div className="absolute -bottom-2 -right-2 bg-gradient-to-r from-[#FFC107] to-[#FF9933] p-2 rounded-full shadow-lg">
                        <FaMedal className="w-4 h-4 text-white" />
                      </div>
                    </div>
                  </div>

                  <div className="flex-1 text-center md:text-left">
                    <blockquote className="text-lg md:text-xl text-gray-700 italic mb-6 leading-relaxed font-medium">
                      "{testimonials[currentTestimonial].quote}"
                    </blockquote>

                    <div className="space-y-3">
                      <h4 className="text-2xl font-bold text-gray-800">
                        {testimonials[currentTestimonial].name}
                      </h4>

                      <div className="flex justify-center md:justify-start items-center space-x-4">
                        <div className="bg-gradient-to-r from-[#FFC107] to-[#FF9933] text-white px-4 py-2 rounded-full font-bold">
                          {testimonials[currentTestimonial].score}
                        </div>
                        <span className="text-gray-600 font-medium">
                          {testimonials[currentTestimonial].college}
                        </span>
                      </div>

                      <p className="text-[#0D2A4B] font-semibold">
                        {testimonials[currentTestimonial].subject}
                      </p>

                      <div className="flex justify-center md:justify-start gap-1">
                        {[...Array(5)].map((_, i) => (
                          <FaStar key={i} className="w-5 h-5 text-[#FFC107]" />
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </AnimatePresence>

            {/* Navigation */}
            <div className="flex justify-center gap-4 mt-8">
              <button
                onClick={prevTestimonial}
                className="bg-white hover:bg-[#FFC107] text-gray-800 hover:text-white p-4 rounded-full shadow-lg transition-all duration-300 border border-gray-200 hover:border-[#FFC107] group">
                <FaChevronLeft className="w-5 h-5" />
              </button>

              <button
                onClick={nextTestimonial}
                className="bg-white hover:bg-[#FFC107] text-gray-800 hover:text-white p-4 rounded-full shadow-lg transition-all duration-300 border border-gray-200 hover:border-[#FFC107] group">
                <FaChevronRight className="w-5 h-5" />
              </button>
            </div>

            {/* Dots Indicator */}
            <div className="flex justify-center gap-3 mt-6">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentTestimonial(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentTestimonial
                      ? 'bg-[#FFC107] scale-125 shadow-lg'
                      : 'bg-gray-300 hover:bg-gray-400'
                  }`}
                />
              ))}
            </div>
          </div>

          {/* All Testimonials Preview */}
          <div className="grid md:grid-cols-4 gap-6">
            {testimonials.map((testimonial, index) => (
              <div
                key={testimonial.id}
                className={`bg-white rounded-xl p-6 shadow-lg cursor-pointer transition-all duration-300 border-2 hover:shadow-xl ${
                  index === currentTestimonial
                    ? 'border-[#FFC107] scale-105 shadow-xl'
                    : 'border-gray-100 hover:border-[#FFC107]/50'
                }`}
                onClick={() => setCurrentTestimonial(index)}>
                <div className="text-center">
                  <img
                    src={testimonial.image || '/placeholder.svg'}
                    alt={testimonial.name}
                    className="w-16 h-16 rounded-full mx-auto mb-4 border-2 border-[#FFC107] object-cover"
                  />
                  <h4 className="font-bold text-gray-800 mb-2">{testimonial.name}</h4>
                  <div className="bg-gradient-to-r from-[#FFC107] to-[#FF9933] text-white px-3 py-1 rounded-full text-sm font-bold mb-2">
                    {testimonial.score}
                  </div>
                  <p className="text-gray-600 text-xs">{testimonial.college}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Enhanced CTA Section */}
      <div className="bg-gradient-to-br from-[#0D2A4B] to-[#1a365d] relative overflow-hidden">
        {/* Indian Pattern Overlay */}
        <div className="absolute inset-0 opacity-10">
          <div
            className="w-full h-full"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fillRule='evenodd'%3E%3Cg fill='%23FFC107' fillOpacity='0.2'%3E%3Cpath d='M50 50c0-16.569-13.431-30-30-30s-30 13.431-30 30 13.431 30 30 30 30-13.431 30-30zm30 0c0-16.569-13.431-30-30-30s-30 13.431-30 30 13.431 30 30 30 30-13.431 30-30z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
            }}></div>
        </div>

        {/* Parallax Elements */}
        <motion.div
          className="absolute top-20 left-20 text-6xl opacity-10 text-[#FFC107]"
          style={{ y: y1 }}>
          <GiBookshelf />
        </motion.div>

        <motion.div
          className="absolute bottom-20 right-20 text-5xl opacity-10 text-[#FFC107]"
          style={{ y: y2 }}>
          <MdLocalHospital />
        </motion.div>

        <div className="px-4 py-20 mx-auto sm:max-w-xl md:max-w-full lg:max-w-screen-xl md:px-24 lg:px-8 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            {/* Indian Decorative Header */}
            <div className="flex justify-center items-center mb-8">
              <div className="w-20 h-0.5 bg-gradient-to-r from-transparent to-[#FFC107]"></div>
              <div className="mx-6 bg-gradient-to-r from-[#FFC107] to-[#FF9933] p-4 rounded-full shadow-lg">
                <FaStar className="text-white text-2xl" />
              </div>
              <div className="w-20 h-0.5 bg-gradient-to-r from-[#FFC107] to-transparent"></div>
            </div>

            <h2 className="text-4xl md:text-5xl font-bold leading-tight tracking-tight text-white mb-6">
              <span className="relative inline-block">
                {/* Decorative Pattern */}
                <svg
                  viewBox="0 0 52 24"
                  fill="currentColor"
                  className="absolute top-0 left-0 z-0 hidden w-32 -mt-8 -ml-20 text-white/20 lg:w-32 lg:-ml-28 lg:-mt-10 sm:block">
                  <defs>
                    <pattern id="pattern" x="0" y="0" width=".135" height=".30">
                      <circle cx="1" cy="1" r=".7" />
                    </pattern>
                  </defs>
                  <rect fill="url(#pattern)" width="52" height="24" />
                </svg>
                <span className="relative">Ready to Transform Your</span>
              </span>{' '}
              <span className="text-[#FFC107]">NEET Journey?</span>
            </h2>

            <p className="text-xl md:text-2xl text-white/90 mb-10 leading-relaxed max-w-3xl mx-auto">
              Ideal for students, teachers, parents, or trainees — our AI-powered NEET prep delivers
              tailored tools, live classes, personalized roadmaps, deep analytics, & gamified
              learning for your success!
            </p>

            {/* Enhanced CTA Button */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-12">
              <a
                href="/"
                className="group relative inline-flex items-center justify-center px-10 py-4 font-bold tracking-wide text-[#0D2A4B] transition duration-300 rounded-xl shadow-2xl bg-gradient-to-r from-[#FFC107] to-[#FF9933] hover:from-[#FF9933] hover:to-[#FFC107] hover:scale-105 hover:shadow-3xl">
                <span className="relative z-10 flex items-center">
                  Get Started Today
                  <FaGraduationCap className="ml-2 w-5 h-5" />
                </span>
              </a>

              <div className="flex items-center space-x-6 text-white/80">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span className="text-sm">No Hidden Fees</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span className="text-sm">Expert Support</span>
                </div>
              </div>
            </div>

            {/* Indian Flag Colors Footer */}
            <div className="flex justify-center items-center space-x-4 mb-8">
              <div className="w-16 h-1 bg-[#FF9933] rounded-full"></div>
              <div className="w-16 h-1 bg-white rounded-full"></div>
              <div className="w-16 h-1 bg-[#FF9933] rounded-full"></div>
            </div>

            <footer className="text-center text-sm text-white/70 font-medium">
              © {new Date().getFullYear()} All rights reserved. | Empowering Future Doctors of
              India
            </footer>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NeetPrepHome;
