"use client"
import { useState, useEffect } from "react"
import {
  FaPlay,
  FaChevronLeft,
  FaChevronRight,
  FaPause,
  FaExpand,
  FaYoutube,
  FaClock,
  FaEye,
  FaHeart,
  FaBookmark,
  FaShare,
} from "react-icons/fa"
import { motion, AnimatePresence } from "framer-motion"

const YouTubeVideos = ({ youtubeResults, isYoutubeLoading, getYouTubeVideoId }) => {
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0)
  const [isPlaying, setIsPlaying] = useState(false)
  const [showControls, setShowControls] = useState(true)
  const [autoPlay, setAutoPlay] = useState(false)
  const [hoveredThumbnail, setHoveredThumbnail] = useState(null)

  // 🎨 NEW: Additional state for customization
  const [viewMode, setViewMode] = useState("grid") // 'grid' or 'list'
  const [likedVideos, setLikedVideos] = useState(new Set())
  const [bookmarkedVideos, setBookmarkedVideos] = useState(new Set())

  // Auto-hide controls after 3 seconds
  useEffect(() => {
    if (showControls) {
      const timer = setTimeout(() => {
        setShowControls(false)
      }, 3000)
      return () => clearTimeout(timer)
    }
  }, [showControls])

  // Auto-advance carousel (optional)
  useEffect(() => {
    if (autoPlay && youtubeResults.length > 1) {
      const interval = setInterval(() => {
        setCurrentVideoIndex((prev) => (prev + 1) % youtubeResults.length)
      }, 10000) // Change video every 10 seconds
      return () => clearInterval(interval)
    }
  }, [autoPlay, youtubeResults.length])

  const nextVideo = () => {
    if (youtubeResults.length > 0) {
      setCurrentVideoIndex((prev) => (prev + 1) % youtubeResults.length)
      setShowControls(true)
    }
  }

  const prevVideo = () => {
    if (youtubeResults.length > 0) {
      setCurrentVideoIndex((prev) => (prev - 1 + youtubeResults.length) % youtubeResults.length)
      setShowControls(true)
    }
  }

  const goToVideo = (index) => {
    setCurrentVideoIndex(index)
    setShowControls(true)
  }

  const toggleAutoPlay = () => {
    setAutoPlay(!autoPlay)
    setShowControls(true)
  }

  // 🎨 NEW: Toggle functions for interactions
  const toggleLike = (index) => {
    const newLiked = new Set(likedVideos)
    if (newLiked.has(index)) {
      newLiked.delete(index)
    } else {
      newLiked.add(index)
    }
    setLikedVideos(newLiked)
  }

  const toggleBookmark = (index) => {
    const newBookmarked = new Set(bookmarkedVideos)
    if (newBookmarked.has(index)) {
      newBookmarked.delete(index)
    } else {
      newBookmarked.add(index)
    }
    setBookmarkedVideos(newBookmarked)
  }

  return (
    <div className="h-full bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/50 rounded-2xl shadow-2xl border border-blue-100/50 backdrop-blur-sm overflow-scroll">
      {/* 🎨 CUSTOMIZABLE: Header Section */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="relative bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-600 p-6 text-white overflow-hidden"
      >
        <div className="relative z-10 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <motion.div
              animate={{ rotate: [0, 5, -5, 0] }}
              transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
              className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm"
            >
              <FaYoutube className="text-white text-xl" />
            </motion.div>
            <div>
              {/* 🎨 CHANGE: Customize header text */}
              <h3 className="text-xl font-bold">Educational Videos</h3>
              <p className="text-white/80 text-sm">Curated learning content</p>
            </div>
            {youtubeResults.length > 0 && (
              <motion.span
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                className="bg-white/20 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm font-medium border border-white/30"
              >
                {youtubeResults.length} videos
              </motion.span>
            )}
          </div>

          {/* 🎨 NEW: View Mode Toggle */}
          <div className="flex items-center space-x-3">
            <div className="flex bg-white/10 rounded-full p-1">
              <button
                onClick={() => setViewMode("grid")}
                className={`px-3 py-1 rounded-full text-xs transition-all ${
                  viewMode === "grid" ? "bg-white/20 text-white" : "text-white/70"
                }`}
              >
                Grid
              </button>
              <button
                onClick={() => setViewMode("list")}
                className={`px-3 py-1 rounded-full text-xs transition-all ${
                  viewMode === "list" ? "bg-white/20 text-white" : "text-white/70"
                }`}
              >
                List
              </button>
            </div>

            {youtubeResults.length > 1 && (
              <>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={toggleAutoPlay}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 backdrop-blur-sm border ${
                    autoPlay
                      ? "bg-green-500/80 text-white border-green-400/50 shadow-lg shadow-green-500/25"
                      : "bg-white/20 text-white border-white/30 hover:bg-white/30"
                  }`}
                  title={autoPlay ? "Disable auto-advance" : "Enable auto-advance"}
                >
                  <div className="flex items-center space-x-2">
                    <motion.div
                      animate={autoPlay ? { rotate: 360 } : {}}
                      transition={{ duration: 2, repeat: autoPlay ? Number.POSITIVE_INFINITY : 0, ease: "linear" }}
                    >
                      <FaClock className="text-sm" />
                    </motion.div>
                    <span>{autoPlay ? "Auto ON" : "Auto OFF"}</span>
                  </div>
                </motion.button>
                <div className="bg-white/20 backdrop-blur-sm px-3 py-2 rounded-full border border-white/30">
                  <span className="text-sm font-medium">
                    {currentVideoIndex + 1} / {youtubeResults.length}
                  </span>
                </div>
              </>
            )}
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="p-6 h-full overflow-y-auto scrollbar-thin scrollbar-thumb-blue-300 scrollbar-track-transparent">
        {isYoutubeLoading ? (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="flex flex-col items-center justify-center h-64 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl border border-blue-100"
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: "linear" }}
              className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full mb-4"
            />
            <motion.div
              animate={{ scale: [1, 1.05, 1] }}
              transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
            >
              <h4 className="text-blue-600 font-bold text-lg mb-2">Finding Perfect Videos</h4>
            </motion.div>
            <p className="text-blue-500 text-sm text-center max-w-xs">
              Searching through thousands of educational videos to find the best matches for your query...
            </p>
            <div className="flex space-x-1 mt-4">
              {[0, 1, 2].map((i) => (
                <motion.div
                  key={i}
                  animate={{
                    scale: [1, 1.3, 1],
                    opacity: [0.5, 1, 0.5],
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Number.POSITIVE_INFINITY,
                    delay: i * 0.2,
                  }}
                  className="w-2 h-2 bg-blue-500 rounded-full"
                />
              ))}
            </div>
          </motion.div>
        ) : youtubeResults.length === 0 ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex flex-col items-center justify-center h-full text-center bg-gradient-to-br from-gray-50 to-blue-50 rounded-2xl border-2 border-dashed border-gray-200 p-8"
          >
            <motion.div
              animate={{
                scale: [1, 1.1, 1],
                rotate: [0, 5, -5, 0],
              }}
              transition={{ duration: 3, repeat: Number.POSITIVE_INFINITY }}
              className="mb-6 relative"
            >
              <div className="w-20 h-20 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-full flex items-center justify-center shadow-2xl">
                <FaYoutube className="text-white text-3xl" />
              </div>
              <motion.div
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY, delay: 0.5 }}
                className="absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center"
              >
                <span className="text-xs">✨</span>
              </motion.div>
            </motion.div>
            <h4 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-3">
              No Videos Yet
            </h4>
            <p className="text-gray-600 text-lg mb-6 max-w-sm leading-relaxed">
              Ask a question to unlock personalized video recommendations!
            </p>
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100 max-w-sm shadow-lg"
            >
              <div className="flex items-center space-x-2 mb-4">
                <span className="text-2xl">💡</span>
                <p className="text-blue-800 font-bold">Pro Tips:</p>
              </div>
              <ul className="text-sm text-blue-700 space-y-3 text-left">
                <motion.li
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.5 }}
                  className="flex items-center space-x-2"
                >
                  <span className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></span>
                  <span>Try math problems or science concepts</span>
                </motion.li>
                <motion.li
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.7 }}
                  className="flex items-center space-x-2"
                >
                  <span className="w-2 h-2 bg-indigo-400 rounded-full"></span>
                  <span>Ask about specific topics you're studying</span>
                </motion.li>
                <motion.li
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.9 }}
                  className="flex items-center space-x-2"
                >
                  <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                  <span>Upload images of problems for visual help</span>
                </motion.li>
              </ul>
            </motion.div>
          </motion.div>
        ) : (
          <div className="space-y-6">
            {/* Enhanced Main Video Player */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="relative bg-black rounded-2xl overflow-hidden aspect-video group shadow-2xl border-4 border-blue-100"
              onMouseEnter={() => setShowControls(true)}
              onMouseLeave={() => setShowControls(false)}
            >
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentVideoIndex}
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 1.05 }}
                  transition={{ duration: 0.4, ease: "easeInOut" }}
                  className="w-full h-full"
                >
                  <iframe
                    className="w-full h-full"
                    src={`https://www.youtube.com/embed/${getYouTubeVideoId(youtubeResults[currentVideoIndex]?.url)}?rel=0&modestbranding=1&autoplay=${isPlaying ? 1 : 0}`}
                    title={youtubeResults[currentVideoIndex]?.title || `Video ${currentVideoIndex + 1}`}
                    frameBorder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                  />
                </motion.div>
              </AnimatePresence>

              {/* Enhanced Video Controls Overlay */}
              <AnimatePresence>
                {showControls && youtubeResults.length > 1 && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/40 pointer-events-none"
                  >
                    {/* Navigation Buttons */}
                    <motion.button
                      whileHover={{ scale: 1.1, x: -5 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={prevVideo}
                      className="absolute left-4 top-1/2 transform -translate-y-1/2 w-14 h-14 bg-gradient-to-r from-blue-500/80 to-indigo-500/80 hover:from-blue-600/90 hover:to-indigo-600/90 rounded-full flex items-center justify-center text-white transition-all duration-300 pointer-events-auto backdrop-blur-sm border border-white/20 shadow-2xl"
                      title="Previous video"
                    >
                      <FaChevronLeft className="text-xl" />
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.1, x: 5 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={nextVideo}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 w-14 h-14 bg-gradient-to-r from-blue-500/80 to-indigo-500/80 hover:from-blue-600/90 hover:to-indigo-600/90 rounded-full flex items-center justify-center text-white transition-all duration-300 pointer-events-auto backdrop-blur-sm border border-white/20 shadow-2xl"
                      title="Next video"
                    >
                      <FaChevronRight className="text-xl" />
                    </motion.button>

                    {/* 🎨 NEW: Video Action Buttons */}
                    <div className="absolute top-4 right-4 flex space-x-2 pointer-events-auto">
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        onClick={() => toggleLike(currentVideoIndex)}
                        className="w-10 h-10 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center border border-white/20"
                      >
                        <FaHeart
                          className={`text-sm ${likedVideos.has(currentVideoIndex) ? "text-red-500" : "text-white"}`}
                        />
                      </motion.button>
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        onClick={() => toggleBookmark(currentVideoIndex)}
                        className="w-10 h-10 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center border border-white/20"
                      >
                        <FaBookmark
                          className={`text-sm ${bookmarkedVideos.has(currentVideoIndex) ? "text-yellow-500" : "text-white"}`}
                        />
                      </motion.button>
                    </div>

                    {/* Enhanced Video Info */}
                    <motion.div
                      initial={{ y: 20, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      className="absolute bottom-4 left-4 right-4 pointer-events-auto"
                    >
                      <div className="bg-gradient-to-r from-black/80 to-black/60 backdrop-blur-md rounded-2xl p-4 border border-white/10 shadow-2xl">
                        <h4 className="text-white font-bold text-lg mb-2 line-clamp-2 leading-tight">
                          {youtubeResults[currentVideoIndex]?.title || `Video ${currentVideoIndex + 1}`}
                        </h4>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            <span className="text-white/90 text-sm font-medium bg-white/10 px-3 py-1 rounded-full">
                              {currentVideoIndex + 1} of {youtubeResults.length}
                            </span>
                            <div className="flex items-center space-x-1 text-white/80 text-sm">
                              <FaEye className="text-xs" />
                              <span>Educational Content</span>
                            </div>
                          </div>
                          <div className="flex space-x-2">
                            <motion.button
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                              className="bg-white/20 hover:bg-white/30 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center space-x-1 transition-all duration-300"
                            >
                              <FaShare className="text-xs" />
                              <span>Share</span>
                            </motion.button>
                            <motion.button
                              whileHover={{ scale: 1.05, y: -2 }}
                              whileTap={{ scale: 0.95 }}
                              onClick={() => window.open(youtubeResults[currentVideoIndex]?.url, "_blank")}
                              className="bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white px-4 py-2 rounded-full text-sm font-medium flex items-center space-x-2 transition-all duration-300 shadow-lg"
                              title="Open in YouTube"
                            >
                              <FaExpand className="text-xs" />
                              <span>Watch on YouTube</span>
                            </motion.button>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>

            {/* 🎨 CUSTOMIZABLE: Video List Section */}
            {youtubeResults.length > 1 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="space-y-4"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <h4 className="text-lg font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                      More Videos
                    </h4>
                    <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                      {viewMode === "grid" ? "Grid View" : "List View"}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <motion.button
                      whileHover={{ scale: 1.1, x: -2 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={prevVideo}
                      className="w-8 h-8 bg-gradient-to-r from-blue-100 to-indigo-100 hover:from-blue-200 hover:to-indigo-200 rounded-full flex items-center justify-center text-blue-600 transition-all duration-300 shadow-md"
                      title="Previous"
                    >
                      <FaChevronLeft className="text-sm" />
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.1, x: 2 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={nextVideo}
                      className="w-8 h-8 bg-gradient-to-r from-blue-100 to-indigo-100 hover:from-blue-200 hover:to-indigo-200 rounded-full flex items-center justify-center text-blue-600 transition-all duration-300 shadow-md"
                      title="Next"
                    >
                      <FaChevronRight className="text-sm" />
                    </motion.button>
                  </div>
                </div>

                {/* 🎨 CUSTOMIZABLE: Grid/List View */}
                <div
                  className={`${viewMode === "grid" ? "grid grid-cols-2 gap-4" : "space-y-3"} max-h-64 overflow-y-auto scrollbar-thin scrollbar-thumb-blue-300 scrollbar-track-blue-50 pr-2`}
                >
                  {youtubeResults.map((video, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: index * 0.1 }}
                      whileHover={{ scale: viewMode === "grid" ? 1.03 : 1.01, y: -2 }}
                      whileTap={{ scale: 0.97 }}
                      onClick={() => goToVideo(index)}
                      onMouseEnter={() => setHoveredThumbnail(index)}
                      onMouseLeave={() => setHoveredThumbnail(null)}
                      className={`relative bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl overflow-hidden cursor-pointer border-2 transition-all duration-300 shadow-lg hover:shadow-2xl ${
                        viewMode === "grid" ? "aspect-video" : "flex items-center p-3 h-20"
                      } ${
                        index === currentVideoIndex
                          ? "border-blue-500 shadow-blue-200 ring-2 ring-blue-200"
                          : "border-blue-200 hover:border-blue-400"
                      }`}
                    >
                      {/* Thumbnail Image */}
                      <div
                        className={`${viewMode === "grid" ? "w-full h-full" : "w-16 h-14 flex-shrink-0"} bg-gradient-to-br from-blue-100 to-indigo-200 flex items-center justify-center relative overflow-hidden rounded-lg`}
                      >
                        <img
                          src={`https://img.youtube.com/vi/${getYouTubeVideoId(video.url)}/mqdefault.jpg`}
                          alt={video.title}
                          className="w-full h-full object-cover transition-transform duration-300 hover:scale-110"
                          onError={(e) => {
                            e.target.style.display = "none"
                            e.target.nextSibling.style.display = "flex"
                          }}
                        />
                        <div
                          className="w-full h-full bg-gradient-to-br from-blue-200 to-indigo-300 flex items-center justify-center"
                          style={{ display: "none" }}
                        >
                          <FaYoutube className="text-blue-500 text-xl" />
                        </div>
                      </div>

                      {/* Video Info for List View */}
                      {viewMode === "list" && (
                        <div className="flex-1 ml-3">
                          <h5 className="text-sm font-medium text-gray-800 line-clamp-2 mb-1">
                            {video.title || `Video ${index + 1}`}
                          </h5>
                          <div className="flex items-center space-x-2">
                            <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full">
                              Educational
                            </span>
                            {likedVideos.has(index) && <FaHeart className="text-red-500 text-xs" />}
                            {bookmarkedVideos.has(index) && <FaBookmark className="text-yellow-500 text-xs" />}
                          </div>
                        </div>
                      )}

                      {/* Enhanced Play Overlay */}
                      <AnimatePresence>
                        {(hoveredThumbnail === index || index === currentVideoIndex) && (
                          <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            exit={{ opacity: 0 }}
                            className={`absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent flex items-center justify-center ${viewMode === "list" ? "rounded-lg" : ""}`}
                          >
                            <motion.div
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              exit={{ scale: 0 }}
                              className={`${viewMode === "grid" ? "w-12 h-12" : "w-8 h-8"} bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center shadow-2xl border-2 border-white/30`}
                            >
                              <FaPlay className={`text-white ${viewMode === "grid" ? "text-lg" : "text-sm"} ml-1`} />
                            </motion.div>
                          </motion.div>
                        )}
                      </AnimatePresence>

                      {/* Current Video Indicator */}
                      {index === currentVideoIndex && (
                        <motion.div
                          initial={{ scale: 0, opacity: 0 }}
                          animate={{ scale: 1, opacity: 1 }}
                          className={`absolute ${viewMode === "grid" ? "top-3 right-3" : "top-2 right-2"}`}
                        >
                          <div className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white px-2 py-1 rounded-full text-xs font-bold shadow-lg border border-white/20 backdrop-blur-sm">
                            <div className="flex items-center space-x-1">
                              <motion.div
                                animate={{ scale: [1, 1.2, 1] }}
                                transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY }}
                                className="w-1.5 h-1.5 bg-white rounded-full"
                              />
                              <span className={viewMode === "grid" ? "" : "hidden"}>Playing</span>
                            </div>
                          </div>
                        </motion.div>
                      )}

                      {/* Enhanced Video Title for Grid View */}
                      {viewMode === "grid" && (
                        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent p-3">
                          <p className="text-white text-xs font-medium line-clamp-2 leading-relaxed">
                            {video.title || `Video ${index + 1}`}
                          </p>
                        </div>
                      )}
                    </motion.div>
                  ))}
                </div>

                {/* Enhanced Dots Indicator */}
                <div className="flex justify-center space-x-2 pt-4">
                  {youtubeResults.map((_, index) => (
                    <motion.button
                      key={index}
                      whileHover={{ scale: 1.2 }}
                      whileTap={{ scale: 0.8 }}
                      onClick={() => goToVideo(index)}
                      className={`h-2 rounded-full transition-all duration-300 ${
                        index === currentVideoIndex
                          ? "bg-gradient-to-r from-blue-500 to-indigo-500 w-8 shadow-lg"
                          : "bg-blue-300 hover:bg-blue-400 w-2"
                      }`}
                      title={`Go to video ${index + 1}`}
                    />
                  ))}
                </div>
              </motion.div>
            )}

            {/* Enhanced Quick Actions */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-4 border border-blue-100 shadow-lg"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <span className="text-sm font-medium text-blue-800 flex items-center space-x-2">
                    <span className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></span>
                    <span>Quick Actions:</span>
                  </span>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setIsPlaying(!isPlaying)}
                    className="flex items-center space-x-2 text-sm bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white px-4 py-2 rounded-full transition-all duration-300 shadow-md"
                  >
                    {isPlaying ? <FaPause className="text-xs" /> : <FaPlay className="text-xs" />}
                    <span>{isPlaying ? "Pause" : "Play"}</span>
                  </motion.button>
                </div>
                <div className="flex items-center space-x-2">
                  {youtubeResults.length > 1 && (
                    <>
                      <motion.button
                        whileHover={{ scale: 1.05, x: -2 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={prevVideo}
                        className="text-sm text-blue-600 hover:text-blue-800 transition-colors px-3 py-2 hover:bg-blue-100 rounded-full font-medium"
                      >
                        ← Previous
                      </motion.button>
                      <motion.button
                        whileHover={{ scale: 1.05, x: 2 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={nextVideo}
                        className="text-sm text-blue-600 hover:text-blue-800 transition-colors px-3 py-2 hover:bg-blue-100 rounded-full font-medium"
                      >
                        Next →
                      </motion.button>
                    </>
                  )}
                </div>
              </div>
            </motion.div>
          </div>
        )}
      </div>

      <style jsx>{`
        .scrollbar-thin::-webkit-scrollbar {
          width: 6px;
        }
        .scrollbar-thin::-webkit-scrollbar-track {
          background: transparent;
        }
        .scrollbar-thumb-blue-300::-webkit-scrollbar-thumb {
          background: linear-gradient(to bottom, #93c5fd, #60a5fa);
          border-radius: 10px;
        }
        .scrollbar-thumb-blue-300::-webkit-scrollbar-thumb:hover {
          background: linear-gradient(to bottom, #60a5fa, #3b82f6);
        }
        .scrollbar-track-blue-50::-webkit-scrollbar-track {
          background: #eff6ff;
          border-radius: 10px;
        }
        .scrollbar-track-transparent::-webkit-scrollbar-track {
          background: transparent;
        }
        .line-clamp-2 {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        .line-clamp-3 {
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      `}</style>
    </div>
  )
}

export default YouTubeVideos
