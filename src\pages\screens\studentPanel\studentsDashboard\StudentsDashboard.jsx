import { useEffect, useCallback, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  User,
  Users,
  School,
  MessageSquareText,
  Phone,
  Mail,
  MailCheck,
  University,
  BookOpen,
  Star,
  Award,
  Home,
  RotateCw,
  Hash,
  Info,
  Calendar,
  Bookmark,
  BarChart2,
  ChevronRight,
  MessageSquare,
  Video,
  FileText,
  Rocket,
  Bot
} from 'lucide-react';
import { useDispatch, useSelector } from 'react-redux';
import {
  setOnBroadingAssessmentStatus,
  useCheckOnBroadingAssessmentStatusMutation
} from '../onBroadingAssesment/onBroadingAssessment.slice';
import { useGetStudentsQuery } from './students.Slice';
import OnBroadingAssessment from '../onBroadingAssesment/OnBroadingAssessment';

const StudentsDashboard = () => {
  const [checkOnBroadingAssessmentStatus] = useCheckOnBroadingAssessmentStatusMutation();
  const dispatch = useDispatch();
  const studentId = sessionStorage.getItem('userId');

  // Fetch student and teacher data
  const {
    data: studentsData,
    isLoading,
    isError,
    error
  } = useGetStudentsQuery(undefined, {
    skip: !studentId
  });

  const onBroadingAssessmentData = useSelector(
    (state) => state.onBroadingAssessment.onBroadingAssessmentStatus
  );

  const [isSkipped, setIsSkipped] = useState(sessionStorage.getItem('isSkip') === 'true');

  const handleCheckAssessmentStatus = useCallback(async () => {
    try {
      const studentId = sessionStorage.getItem('userId');
      if (!studentId) return;

      const res = await checkOnBroadingAssessmentStatus({
        student_id: studentId
      }).unwrap();

      const sessionSkip = sessionStorage.getItem('isSkip');

      if (res.assessment_required && !res.assessment_completed && sessionSkip !== 'true') {
        sessionStorage.setItem('isSkip', 'false');
        setIsSkipped(false);
      }

      dispatch(setOnBroadingAssessmentStatus(res));
    } catch (error) {
      console.error('Error checking assessment status:', error);
    }
  }, [checkOnBroadingAssessmentStatus, dispatch]);

  useEffect(() => {
    handleCheckAssessmentStatus();
  }, [handleCheckAssessmentStatus]);

  const handleSkip = () => {
    setIsSkipped(true);
    sessionStorage.setItem('isSkip', 'true');
  };

  const shouldShowAssessment =
    onBroadingAssessmentData?.assessment_required &&
    !onBroadingAssessmentData?.assessment_completed &&
    !isSkipped;

  // Student details
  const user = studentsData?.student || {
    username: sessionStorage.getItem('name') || 'Unknown',
    center_code: sessionStorage.getItem('centercode') || 'Unknown',
    phone: sessionStorage.getItem('phone') || 'Unknown',
    course: 'Unknown'
  };

  const containerVariants = {
    hidden: { opacity: 0, scale: 0.9, y: 100 },
    visible: {
      opacity: 1,
      scale: 1,
      y: 0,
      transition: { duration: 0.8, ease: 'easeOut', staggerChildren: 0.2 }
    }
  };

  const childVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
  };

  // Dashboard widgets with matching color scheme
  const dashboardWidgets = [
    {
      id: 1,
      title: '24/7 Chatbot Support',
      stat: '150+ Queries',
      description: 'Get instant help anytime with our AI chatbot!',
      icon: '💬',
      color: 'from-[#2563eb] to-[#1d4ed8]',
      bgColor: 'bg-[#2563eb]/5',
      borderColor: 'border-[#2563eb]/20',
      textColor: 'text-[#2563eb]'
    },
    {
      id: 2,
      title: 'E-Book Center',
      stat: '200+ Books',
      description: 'Access a vast library of e-books for learning.',
      icon: '📚',
      color: 'from-[#2563eb] to-[#1d4ed8]',
      bgColor: 'bg-[#2563eb]/5',
      borderColor: 'border-[#2563eb]/20',
      textColor: 'text-[#2563eb]'
    },
    {
      id: 3,
      title: 'Recordings',
      stat: '75 Videos',
      description: 'Watch recorded lectures at your convenience.',
      icon: '🎥',
      color: 'from-[#2563eb] to-[#1d4ed8]',
      bgColor: 'bg-[#2563eb]/5',
      borderColor: 'border-[#2563eb]/20',
      textColor: 'text-[#2563eb]'
    },
    {
      id: 4,
      title: 'Create Your Own Test',
      stat: '50 Tests',
      description: 'Design custom tests to assess your skills.',
      icon: '📝',
      color: 'from-[#2563eb] to-[#1d4ed8]',
      bgColor: 'bg-[#2563eb]/5',
      borderColor: 'border-[#2563eb]/20',
      textColor: 'text-[#2563eb]'
    },
    {
      id: 5,
      title: 'Booster Module',
      stat: '30 Exercises',
      description: 'Boost your performance with targeted exercises.',
      icon: '🚀',
      color: 'from-[#2563eb] to-[#1d4ed8]',
      bgColor: 'bg-[#2563eb]/5',
      borderColor: 'border-[#2563eb]/20',
      textColor: 'text-[#2563eb]'
    },
    {
      id: 6,
      title: 'AI Tutor',
      stat: '100+ Sessions',
      description: 'Personalized learning with an AI tutor.',
      icon: '🤖',
      color: 'from-[#2563eb] to-[#1d4ed8]',
      bgColor: 'bg-[#2563eb]/5',
      borderColor: 'border-[#2563eb]/20',
      textColor: 'text-[#2563eb]'
    }
  ];

  const getInitials = (firstName, lastName) => {
    return `${firstName?.charAt(0) || ''}${lastName?.charAt(0) || ''}`.toUpperCase();
  };

  const getRatingColor = (rating) => {
    if (rating >= 4.8) return 'bg-green-100 text-green-800';
    if (rating >= 4.5) return 'bg-[#2563eb]/10 text-[#2563eb]';
    return 'bg-yellow-100 text-yellow-800';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 p-4 md:p-8">
      {shouldShowAssessment && <OnBroadingAssessment isSkip={handleSkip} />}
      <div className="relative max-w-7xl mx-auto space-y-12 z-10">
        {/* Welcome Header with Animated Gradient */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center pt-8 pb-12">
          <motion.div
            className="inline-block"
            animate={{
              backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: 'linear'
            }}
            style={{
              backgroundSize: '300% 300%',
              WebkitBackgroundClip: 'text',
              backgroundClip: 'text',
              color: 'transparent'
            }}>
            <h1 className="text-4xl md:text-5xl text-[var(--color-student)] font-bold mb-3">
              Welcome back, {user.first_name || user.username}!
            </h1>
          </motion.div>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            "Education is the most powerful weapon which you can use to change the world."
          </p>

          {/* Animated Progress Indicator */}
          <motion.div
            className="mt-5 mx-auto  max-w-md bg-[var(--color-counselor)]
             backdrop-blur-sm rounded-full p-1 mb-0 shadow-inner"
            initial={{ scaleX: 0 }}
            animate={{ scaleX: 1 }}
            transition={{ delay: 0.5, duration: 1 }}>
            <motion.div
              className="h-1 rounded-full bg-bg-[var(--color-counselor)]"
              initial={{ width: 0 }}
              animate={{ width: '65%' }}
              transition={{ delay: 1, duration: 1.5 }}
            />
          </motion.div>
        </motion.div>

        {/* Student Profile Card - Neumorphic Design with 3D Tilt */}
        {!isLoading && !isError && studentsData?.student && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="relative  group">
            <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-blue-100 to-purple-100 shadow-lg  transform group-hover:scale-105 transition-all duration-500" />
            <div className="relative bg-white/80 backdrop-blur-sm hover:cursor-pointer rounded-3xl overflow-hidden border border-white/20 shadow-xl">
              <div className="p-8">
                <div className="flex flex-col md:flex-row items-center gap-8">
                  {/* Avatar with Floating Animation */}
                  <motion.div
                    className="relative"
                    animate={{
                      y: [0, -15, 0]
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                      ease: 'easeInOut'
                    }}>
                    <div className="h-28 w-28 rounded-2xl bg-[var(--color-student)] flex items-center justify-center text-[var(--color-counselor)] text-3xl font-bold shadow-lg">
                      {getInitials(user.first_name, user.last_name)}
                    </div>
                    <motion.div
                      className="absolute -bottom-3 -right-3 h-12 w-12 bg-white rounded-xl flex items-center justify-center shadow-md border-2 border-blue-200"
                      animate={{
                        rotate: [0, 360],
                        scale: [1, 1.1, 1]
                      }}
                      transition={{
                        duration: 8,
                        repeat: Infinity,
                        ease: 'linear'
                      }}>
                      <Award className="text-blue-600" size={20} />
                    </motion.div>
                  </motion.div>

                  <div className="text-center md:text-left">
                    <h2 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-purple-600">
                      {user.first_name} {user.last_name}
                    </h2>
                    <div className="mt-3 inline-flex items-center text-white px-4 py-1.5 rounded-full bg-[var(--color-counselor)] text-sm font-medium">
                      <BookOpen size={16} className="mr-2" /> {user.course}
                    </div>
                  </div>
                </div>

                {/* Info Cards with Hover Effects */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-10">
                  <motion.div
                    className="bg-white p-6 rounded-xl border border-gray-100 shadow-sm hover:shadow-md transition-all"
                    whileHover={{ y: -5 }}>
                    <div className="flex items-center gap-4 mb-4">
                      <div className="p-3 rounded-lg bg-blue-50 text-[var(--color-counselor)]">
                        <User size={20} />
                      </div>
                      <h3 className="text-lg font-semibold">Personal Info</h3>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3 text-gray-700">
                        <Mail size={18} className="text-blue-500" />
                        <span className="truncate">{user.student_email}</span>
                      </div>
                      <div className="flex items-center gap-3 text-gray-700">
                        <Phone size={18} className="text-blue-500" />
                        <span>{user.phone}</span>
                      </div>
                    </div>
                  </motion.div>

                  <motion.div
                    className="bg-white p-6 rounded-xl border border-gray-100 shadow-sm hover:shadow-md transition-all"
                    whileHover={{ y: -5 }}>
                    <div className="flex items-center gap-4 mb-4">
                      <div className="p-3 rounded-lg bg-purple-50 text-[var(--color-counselor)]">
                        <School size={20} />
                      </div>
                      <h3 className="text-lg font-semibold">Education</h3>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3 text-gray-700">
                        <University size={18} className="text-blue-500" />
                        <span>{user.center_name}</span>
                      </div>
                      <div className="flex items-center gap-3 text-gray-700">
                        <Hash size={18} className="text-blue-500" />
                        <span>Center Code: {user.center_code}</span>
                      </div>
                      <div className="flex items-center gap-3 text-gray-700">
                        <MailCheck size={18} className="text-blue-500" />
                        <a href={`mailto:${user.center_email}`}>
                          <span>Center Email: {user.center_email}</span>
                        </a>
                      </div>
                    </div>
                  </motion.div>
                </div>
              </div>
            </div>
          </motion.div>
        )}
        {!isLoading && !isError && studentsData?.faculty?.length > 0 && (
          <motion.section
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="bg-white rounded-2xl p-4 sm:p-6 shadow-xs border border-gray-100">
            {/* Header Section */}
            <div className="mb-6 md:mb-8">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <motion.div className="text-center sm:text-left">
                  <motion.h2
                    className="text-2xl sm:text-3xl font-bold text-[var(--color-student)] inline-block pb-2"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}>
                    Faculty Mentors
                    <motion.span
                      className="block h-1 mt-4 bg-[var(--color-counselor)] rounded-2xl"
                      initial={{ scaleX: 0 }}
                      animate={{ scaleX: 1 }}
                      transition={{ delay: 0.3, duration: 0.8, type: 'spring' }}
                    />
                  </motion.h2>
                  <motion.p
                    className="text-gray-500 text-sm sm:text-base"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.5 }}>
                    Your academic support team
                  </motion.p>
                </motion.div>

                {studentsData.faculty.length > 3 && (
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="hidden sm:flex items-center text-sm font-medium px-4 py-2 rounded-lg bg-gray-100 hover:bg-gray-200 text-gray-700">
                    View All
                    <ChevronRight size={16} className="ml-1" />
                  </motion.button>
                )}
              </div>
            </div>

            {/* Faculty Cards Section */}
            <div className="relative">
              {/* Animated background elements - Hidden on mobile */}
              <motion.div
                className="hidden md:block absolute -left-20 top-1/2 w-40 h-40 rounded-full bg-[--color-counselor] opacity-10 blur-xl"
                animate={{
                  scale: [1, 1.2, 1],
                  x: [0, 20, 0]
                }}
                transition={{
                  duration: 8,
                  repeat: Infinity,
                  ease: 'easeInOut'
                }}
              />

              {/* Cards Container - Improved alignment */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
                {studentsData.faculty.slice(0, 4).map((faculty, index) => (
                  <motion.div
                    key={faculty.id}
                    className="bg-blue-700 rounded-xl hover:cursor-pointer shadow-md overflow-hidden flex flex-col"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    whileHover={{
                      rotateY: 5,
                      rotateX: -2,
                      transition: { duration: 0.3 }
                    }}
                    style={{
                      transformStyle: 'preserve-3d',
                      transformPerspective: 1000,
                      minHeight: '220px' // Ensures consistent card height
                    }}>
                    <div className="p-5 flex-1 flex flex-col">
                      {/* Profile Section - Centered on desktop */}
                      <div className="flex flex-col items-center text-center mb-4 md:mb-6">
                        <div className="w-16 h-16 rounded-full bg-[var(--color-counselor)] text-white font-bold flex items-center justify-center shadow-inner mb-3">
                          {getInitials(faculty.first_name, faculty.last_name)}
                        </div>
                        <div>
                          <h3 className="text-lg font-bold text-white">
                            {faculty.first_name} {faculty.last_name}
                          </h3>
                          <p className="text-sm text-gray-300">Faculty Advisor</p>
                        </div>
                      </div>

                      {/* Contact Info Section - Better spacing */}
                      <div className="mt-auto space-y-3">
                        <div className="flex items-center gap-3 p-3 rounded-lg bg-blue-800/30">
                          <Mail size={18} className="flex-shrink-0 text-[var(--color-counselor)]" />
                          <a href={`mailto:${faculty.email}`}>
                            <span className="text-sm font-medium text-white truncate">
                              {faculty.email}
                            </span>
                          </a>
                        </div>
                        <div className="flex items-center gap-3 p-3 rounded-lg bg-blue-800/30">
                          <Phone
                            size={18}
                            className="flex-shrink-0 text-[var(--color-counselor)]"
                          />
                          <span className="text-sm font-medium text-white">{faculty.phone}</span>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Mobile View All Button */}
              {studentsData.faculty.length > 3 && (
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="sm:hidden mt-6 w-full flex items-center justify-center text-sm font-medium px-4 py-2 rounded-lg bg-gray-100 hover:bg-gray-200 text-gray-700">
                  View All Faculty
                  <ChevronRight size={16} className="ml-1" />
                </motion.button>
              )}
            </div>
          </motion.section>
        )}
        {/* Teachers Section - Three Cards Side by Side with Subject */}

        {!isLoading && !isError && studentsData?.kota_teachers?.length > 0 && (
          <motion.section
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="relative py-12 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
            {/* Header with animated elements */}
            <motion.div
              className="text-center mb-16 relative"
              initial={{ y: -20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.6 }}>
              <motion.div
                className="absolute -left-10 top-1/2 w-8 h-8 rounded-full  opacity-20"
                animate={{
                  scale: [1, 1.5, 1],
                  x: [0, 10, 0]
                }}
                transition={{
                  duration: 6,
                  repeat: Infinity,
                  ease: 'easeInOut'
                }}
              />
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 inline-flex items-center justify-center gap-3">
                <motion.span
                  className="relative"
                  style={{ color: 'var(--color-teacher)' }}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2 }}>
                  Your Expert Guides
                </motion.span>
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{
                    delay: 0.4,
                    type: 'spring',
                    stiffness: 300
                  }}>
                  <Users size={32} className="text-[var(--color-counselor)]" />
                </motion.div>
              </h2>
              <motion.p
                className="text-gray-600 mt-4 max-w-2xl mx-auto text-lg"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.6 }}>
                Meet your dedicated mentors who will shape your academic journey
              </motion.p>
            </motion.div>

            {/* Teacher Cards Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {studentsData.kota_teachers.slice(0, 3).map((teacher, index) => (
                <motion.div
                  key={teacher.id}
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 * index }}
                  whileHover={{
                    y: -5,
                    transition: { duration: 0.3 }
                  }}
                  className="relative group">
                  {/* Card background with subtle pattern */}
                  <div className="absolute inset-0 rounded-2xl bg-white border border-gray-100 shadow-sm group-hover:shadow-md transition-all duration-300 overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-br from-white to-gray-50 opacity-90"></div>
                    <div
                      className="absolute top-0 right-0 w-full h-1"
                      style={{ backgroundColor: 'var(--color-counselor)' }}></div>
                  </div>

                  {/* Main card content */}
                  <div className="relative h-full flex flex-col p-6">
                    {/* Teacher profile header */}
                    <div className="flex items-start gap-5 mb-6">
                      <motion.div className="relative" whileHover={{ rotate: 5 }}>
                        <div
                          className="w-16 h-16 rounded-xl flex items-center justify-center text-white text-2xl font-bold shadow-lg"
                          style={{ backgroundColor: 'var(--color-student)' }}>
                          {getInitials(teacher.first_name, teacher.last_name)}
                        </div>
                        <motion.div
                          className="absolute -inset-2 rounded-xl border-2 border-[var(--color-teacher)] opacity-20"
                          animate={{
                            rotate: [0, 5, -5, 0],
                            scale: [1, 1.05, 1]
                          }}
                          transition={{
                            duration: 8,
                            repeat: Infinity
                          }}
                        />
                      </motion.div>

                      <div className="flex-1">
                        <h3 className="text-xl font-bold text-gray-900">
                          {teacher.first_name} {teacher.last_name}
                        </h3>
                        <p className="text-sm text-gray-500 mb-2">{teacher.course}</p>
                        <div className="inline-flex items-center px-3 py-1 rounded-full bg-[var(--color-counselor)] bg-opacity-10 text-white text-sm font-medium">
                          <BookOpen size={14} className="mr-2" />
                          {teacher.subject || 'General'}
                        </div>
                      </div>
                    </div>

                    {/* Stats grid */}
                    <div className="grid grid-cols-2 gap-4 ">
                      <div className="bg-white bg-opacity-70 rounded-xl p-4 border border-gray-100">
                        <p className="text-xs text-gray-500 mb-1">Experience</p>
                        <p className="text-lg font-bold" style={{ color: 'var(--color-student)' }}>
                          {teacher.experience || '5'}+ years
                        </p>
                      </div>
                      <div className="bg-white bg-opacity-70 rounded-xl p-4 border border-gray-100">
                        <p className="text-xs text-gray-500 mb-1">Rating</p>
                        <div className="flex items-center">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              size={16}
                              className={
                                i < Math.floor(teacher.rating || 4.5)
                                  ? 'text-yellow-400 fill-yellow-400'
                                  : 'text-gray-300'
                              }
                            />
                          ))}
                          <span className="ml-2 text-sm font-medium text-gray-700">
                            {teacher.rating || 4.5}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* View all button */}
            {studentsData.kota_teachers.length > 3 && (
              <motion.div
                className="text-center mt-12"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.8 }}>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="inline-flex items-center px-6 py-3 rounded-xl border border-gray-200 bg-white text-sm font-medium shadow-sm hover:shadow-md transition-all"
                  style={{ color: 'var(--color-teacher)' }}>
                  View All Mentors
                  <ChevronRight size={16} className="ml-2" />
                </motion.button>
              </motion.div>
            )}
          </motion.section>
        )}
        {/* Custom Media Queries for Additional Responsiveness */}
        <style jsx>{`
          @media (max-width: 640px) {
            .max-w-sm {
              max-width: 100%;
            }
            .space-y-10 {
              padding-left: 1rem;
              padding-right: 1rem;
            }
          }
          @media (max-width: 768px) {
            .sm\\:flex-row {
              flex-direction: column;
              align-items: center;
            }
            .sm\\:w-80 {
              width: 100%;
              max-width: 20rem;
            }
            .sm\\:p-6 {
              padding: 1rem;
            }
            .sm\\:text-lg {
              font-size: 1rem;
            }
          }
        `}</style>

        {/* Learning Resources - Interactive Grid */}
        {/* <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6 }}
          className="space-y-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900">
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Learning Resources
              </span>
            </h2>
            <p className="text-gray-600 mt-2 max-w-2xl mx-auto">
              Explore tools and materials to enhance your learning experience
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {dashboardWidgets.map((widget, index) => {
              const icons = [MessageSquare, BookOpen, Video, FileText, Rocket, Bot];
              const IconComponent = icons[index] || BookOpen;

              return (
                <motion.div
                  key={widget.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 * index + 0.6 }}
                  whileHover={{ y: -5 }}
                  className="relative group">
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-100 to-purple-100 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  <div className="relative h-full bg-white/80 backdrop-blur-sm rounded-2xl overflow-hidden border border-white/20 shadow-sm hover:shadow-md transition-all flex flex-col">
                    <div className="p-6 flex-1">
                      <div className="flex items-start justify-between mb-4">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900">{widget.title}</h3>
                          <p className="text-sm text-gray-600 mt-1">{widget.description}</p>
                        </div>
                        <div className="p-3 rounded-lg bg-blue-50 text-blue-600">
                          <IconComponent size={20} />
                        </div>
                      </div>
                      <p className="text-2xl font-bold text-blue-600 mb-6">{widget.stat}</p>
                    </div>
                    <div className="px-6 pb-4">
                      <motion.button
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        className="w-full py-2 px-4 rounded-lg font-medium text-white bg-[var(--color-counselor)] hover:from-blue-600 hover:to-purple-600 transition-all flex items-center justify-center gap-2">
                        Explore Now
                        <ChevronRight size={16} />
                      </motion.button>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </motion.div> */}
      </div>
    </div>
  );
};

export default StudentsDashboard;
