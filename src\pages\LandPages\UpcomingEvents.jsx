'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useRegisterEventMutation, useCheckPaymentStatusQuery } from './upcomingEvents.Slice';

const RegistrationForm = () => {
  const navigate = useNavigate();
  const paymentSectionRef = useRef(null);

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    age: '',
    gender: '',
    organization: '',
    city: '',
    state: ''
  });

  const [errors, setErrors] = useState({});
  const [registerEvent, { isLoading: isRegistering, error: registerError }] =
    useRegisterEventMutation();
  const [paymentId, setPaymentId] = useState(null);
  const [qrError, setQrError] = useState(null);
  const [registrationData, setRegistrationData] = useState(null);
  const [paymentStatus, setPaymentStatus] = useState(null);
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  const {
    data: statusData,
    isFetching: isCheckingStatus,
    error: statusError
  } = useCheckPaymentStatusQuery(paymentId, {
    skip: !paymentId,
    pollingInterval: 10000
  });

  // Auto-scroll to payment section when QR code appears
  useEffect(() => {
    if (registrationData && registrationData.payment_url && paymentSectionRef.current) {
      setTimeout(() => {
        paymentSectionRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
      }, 500);
    }
  }, [registrationData]);

  // Handle payment status updates
  useEffect(() => {
    if (statusData) {
      const status = statusData.status?.toLowerCase();
      const paidAt = statusData.paid_at || null;
      setPaymentStatus({ status, paidAt });
      if (status === 'paid') {
        setShowSuccessModal(true);
      }
    }
    if (statusError) {
      setErrors({ api: statusError.data?.error || 'Failed to check payment status' });
    }
  }, [statusData, statusError]);

  const validateForm = () => {
    const newErrors = {};
    if (!formData.name.trim()) newErrors.name = 'Name is required';
    if (!formData.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Valid email is required';
    }
    if (!formData.phone || !/^\d{10}$/.test(formData.phone)) {
      newErrors.phone = 'Phone must be exactly 10 digits';
    }
    if (!formData.age || isNaN(formData.age) || formData.age <= 0 || formData.age > 120) {
      newErrors.age = 'Valid age is required (1-120)';
    }
    if (!formData.gender) newErrors.gender = 'Gender is required';
    if (!formData.organization.trim()) newErrors.organization = 'Organization is required';
    if (!formData.city.trim()) newErrors.city = 'City is required';
    if (!formData.state.trim()) newErrors.state = 'State is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: null }));
    }

    if (name === 'phone') {
      if (/^\d{0,10}$/.test(value)) {
        setFormData({ ...formData, [name]: value });
      }
    } else if (name === 'age') {
      if (/^\d*$/.test(value) && (value === '' || Number.parseInt(value) <= 120)) {
        setFormData({ ...formData, [name]: value });
      }
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      const firstErrorField = Object.keys(errors)[0];
      const errorElement = document.querySelector(`[name="${firstErrorField}"]`);
      if (errorElement) {
        errorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        errorElement.focus();
      }
      return;
    }

    try {
      const result = await registerEvent(formData).unwrap();
      console.log('Registration successful:', result);
      setRegistrationData({
        payment_id: result.payment_id,
        payment_url: result.payment_url,
        qr_base64: result.qr_base64
      });
      setPaymentId(result.payment_id);
      setErrors({});
      setQrError(null);
    } catch (err) {
      setErrors({ api: err.data?.error || 'Registration failed. Please try again.' });
    }
  };

  const handleModalClose = () => {
    setShowSuccessModal(false);
    setFormData({
      name: '',
      email: '',
      phone: '',
      age: '',
      gender: '',
      organization: '',
      city: '',
      state: ''
    });
    setPaymentId(null);
    setQrError(null);
    setRegistrationData(null);
    setPaymentStatus(null);
    navigate('/');
  };

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1,
        ease: 'easeOut'
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 15 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4, ease: 'easeOut' }
    }
  };

  const inputVariants = {
    focus: {
      scale: 1.01,
      transition: { duration: 0.2 }
    },
    blur: {
      scale: 1,
      transition: { duration: 0.2 }
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <style jsx>{`
        .primary-color {
          color: #002a4b;
        }
        .primary-bg {
          background-color: #002a4b;
        }
        .primary-border {
          border-color: #002a4b;
        }
        .primary-hover:hover {
          background-color: #003d6b;
        }
        .secondary-bg {
          background-color: #f8fafc;
        }
        .accent-color {
          color: #0ea5e9;
        }
        .accent-bg {
          background-color: #0ea5e9;
        }
        .success-color {
          color: #059669;
        }
        .success-bg {
          background-color: #059669;
        }
        .error-color {
          color: #dc2626;
        }
        .error-bg {
          background-color: #dc2626;
        }
        .warning-color {
          color: #d97706;
        }
        .warning-bg {
          background-color: #d97706;
        }
      `}</style>

      <div className="max-w-4xl mx-auto">
        {/* Header Section */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden mb-8">
          {/* Banner Section */}
          <motion.div variants={itemVariants} className="relative">
            <div className="bg-gray-100 overflow-hidden">
              <motion.img
                src="https://frontend2-images-sasthra-demo-mumbai.s3.ap-south-1.amazonaws.com/SAS_EVE.jpg"
                alt="Event Banner"
                className="w-full h-full object-cover" // Retain original responsive behavior
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.3 }}
              />
            </div>

            {/* Overlay Content with Back Icon */}
            <div className="absolute inset-0 bg-opacity-40 flex items-center justify-center">
              <div className="text-center text-white px-4">
                <motion.button
                  variants={itemVariants}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => navigate('/')}
                  className="flex items-center text-white hover:text-gray-200 font-medium transition-colors absolute top-4 left-4">
                  <svg
                    className="w-5 h-5 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M10 19l-7-7m0 0l7-7m-7 7h18"
                    />
                  </svg>
                </motion.button>

                <motion.h1
                  className="text-2xl md:text-4xl font-bold mb-3" // Smaller text for mobile
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}>
                  Event Registration
                </motion.h1>
                <motion.p
                  className="text-sm md:text-lg opacity-90" // Smaller text for mobile
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}>
                  Join us for an amazing experience
                </motion.p>
              </div>
            </div>
          </motion.div>
        </motion.div>

        {/* Form Section */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8 md:p-12">
          <motion.div variants={itemVariants} className="mb-8">
            <h2 className="text-2xl font-bold primary-color mb-2">Registration Details</h2>
            <p className="text-gray-600">
              Please fill in all required information to complete your registration.
            </p>
          </motion.div>

          {/* Form Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            {/* Full Name */}
            <motion.div variants={itemVariants} className="lg:col-span-2">
              <label className="block text-gray-700 font-semibold mb-3 text-sm">
                Full Name <span className="error-color">*</span>
              </label>
              <motion.input
                variants={inputVariants}
                whileFocus="focus"
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className={`w-full p-4 border-2 rounded-xl focus:outline-none transition-all duration-300 bg-gray-50 ${
                  errors.name
                    ? 'border-red-300 focus:border-red-500 bg-red-50'
                    : 'border-gray-200 focus:primary-border focus:bg-white'
                }`}
                placeholder="Enter your full name"
              />
              <AnimatePresence>
                {errors.name && (
                  <motion.p
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="error-color text-sm mt-2 flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                    {errors.name}
                  </motion.p>
                )}
              </AnimatePresence>
            </motion.div>

            {/* Email */}
            <motion.div variants={itemVariants}>
              <label className="block text-gray-700 font-semibold mb-3 text-sm">
                Email Address <span className="error-color">*</span>
              </label>
              <motion.input
                variants={inputVariants}
                whileFocus="focus"
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className={`w-full p-4 border-2 rounded-xl focus:outline-none transition-all duration-300 bg-gray-50 ${
                  errors.email
                    ? 'border-red-300 focus:border-red-500 bg-red-50'
                    : 'border-gray-200 focus:primary-border focus:bg-white'
                }`}
                placeholder="<EMAIL>"
              />
              <AnimatePresence>
                {errors.email && (
                  <motion.p
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="error-color text-sm mt-2 flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                    {errors.email}
                  </motion.p>
                )}
              </AnimatePresence>
            </motion.div>

            {/* Phone */}
            <motion.div variants={itemVariants}>
              <label className="block text-gray-700 font-semibold mb-3 text-sm">
                Phone Number <span className="error-color">*</span>
              </label>
              <motion.input
                variants={inputVariants}
                whileFocus="focus"
                type="text"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                className={`w-full p-4 border-2 rounded-xl focus:outline-none transition-all duration-300 bg-gray-50 ${
                  errors.phone
                    ? 'border-red-300 focus:border-red-500 bg-red-50'
                    : 'border-gray-200 focus:primary-border focus:bg-white'
                }`}
                placeholder="10-digit phone number"
              />
              <AnimatePresence>
                {errors.phone && (
                  <motion.p
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="error-color text-sm mt-2 flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                    {errors.phone}
                  </motion.p>
                )}
              </AnimatePresence>
            </motion.div>

            {/* Age */}
            <motion.div variants={itemVariants}>
              <label className="block text-gray-700 font-semibold mb-3 text-sm">
                Age <span className="error-color">*</span>
              </label>
              <motion.input
                variants={inputVariants}
                whileFocus="focus"
                type="number"
                name="age"
                value={formData.age}
                onChange={handleInputChange}
                className={`w-full p-4 border-2 rounded-xl focus:outline-none transition-all duration-300 bg-gray-50 ${
                  errors.age
                    ? 'border-red-300 focus:border-red-500 bg-red-50'
                    : 'border-gray-200 focus:primary-border focus:bg-white'
                }`}
                placeholder="Your age"
                min="1"
                max="120"
              />
              <AnimatePresence>
                {errors.age && (
                  <motion.p
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="error-color text-sm mt-2 flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                    {errors.age}
                  </motion.p>
                )}
              </AnimatePresence>
            </motion.div>

            {/* Gender */}
            <motion.div variants={itemVariants}>
              <label className="block text-gray-700 font-semibold mb-3 text-sm">
                Gender <span className="error-color">*</span>
              </label>
              <motion.select
                variants={inputVariants}
                whileFocus="focus"
                name="gender"
                value={formData.gender}
                onChange={handleInputChange}
                className={`w-full p-4 border-2 rounded-xl focus:outline-none transition-all duration-300 bg-gray-50 ${
                  errors.gender
                    ? 'border-red-300 focus:border-red-500 bg-red-50'
                    : 'border-gray-200 focus:primary-border focus:bg-white'
                }`}>
                <option value="">Select Gender</option>
                <option value="Male">Male</option>
                <option value="Female">Female</option>
                <option value="Other">Other</option>
              </motion.select>
              <AnimatePresence>
                {errors.gender && (
                  <motion.p
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="error-color text-sm mt-2 flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                    {errors.gender}
                  </motion.p>
                )}
              </AnimatePresence>
            </motion.div>

            {/* Organization */}
            <motion.div variants={itemVariants} className="lg:col-span-2">
              <label className="block text-gray-700 font-semibold mb-3 text-sm">
                Organization <span className="error-color">*</span>
              </label>
              <motion.input
                variants={inputVariants}
                whileFocus="focus"
                type="text"
                name="organization"
                value={formData.organization}
                onChange={handleInputChange}
                className={`w-full p-4 border-2 rounded-xl focus:outline-none transition-all duration-300 bg-gray-50 ${
                  errors.organization
                    ? 'border-red-300 focus:border-red-500 bg-red-50'
                    : 'border-gray-200 focus:primary-border focus:bg-white'
                }`}
                placeholder="Your organization or company"
              />
              <AnimatePresence>
                {errors.organization && (
                  <motion.p
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="error-color text-sm mt-2 flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                    {errors.organization}
                  </motion.p>
                )}
              </AnimatePresence>
            </motion.div>

            {/* City */}
            <motion.div variants={itemVariants}>
              <label className="block text-gray-700 font-semibold mb-3 text-sm">
                City <span className="error-color">*</span>
              </label>
              <motion.input
                variants={inputVariants}
                whileFocus="focus"
                type="text"
                name="city"
                value={formData.city}
                onChange={handleInputChange}
                className={`w-full p-4 border-2 rounded-xl focus:outline-none transition-all duration-300 bg-gray-50 ${
                  errors.city
                    ? 'border-red-300 focus:border-red-500 bg-red-50'
                    : 'border-gray-200 focus:primary-border focus:bg-white'
                }`}
                placeholder="Your city"
              />
              <AnimatePresence>
                {errors.city && (
                  <motion.p
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="error-color text-sm mt-2 flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                    {errors.city}
                  </motion.p>
                )}
              </AnimatePresence>
            </motion.div>

            {/* State */}
            <motion.div variants={itemVariants}>
              <label className="block text-gray-700 font-semibold mb-3 text-sm">
                State <span className="error-color">*</span>
              </label>
              <motion.input
                variants={inputVariants}
                whileFocus="focus"
                type="text"
                name="state"
                value={formData.state}
                onChange={handleInputChange}
                className={`w-full p-4 border-2 rounded-xl focus:outline-none transition-all duration-300 bg-gray-50 ${
                  errors.state
                    ? 'border-red-300 focus:border-red-500 bg-red-50'
                    : 'border-gray-200 focus:primary-border focus:bg-white'
                }`}
                placeholder="Your state"
              />
              <AnimatePresence>
                {errors.state && (
                  <motion.p
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="error-color text-sm mt-2 flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                    {errors.state}
                  </motion.p>
                )}
              </AnimatePresence>
            </motion.div>
          </div>

          {/* API Error */}
          <AnimatePresence>
            {errors.api && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl">
                <div className="flex items-center">
                  <svg className="w-5 h-5 error-color mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fillRule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <p className="error-color font-medium">{errors.api}</p>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Submit Button */}
          <motion.button
            variants={itemVariants}
            whileHover={{ scale: 1.01, y: -1 }}
            whileTap={{ scale: 0.99 }}
            onClick={handleSubmit}
            disabled={isRegistering}
            className="w-full primary-bg primary-hover text-white p-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center">
            {isRegistering ? (
              <span className="flex items-center">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: 'linear' }}
                  className="w-5 h-5 border-2 border-white border-t-transparent rounded-full mr-3"
                />
                Processing Registration...
              </span>
            ) : (
              <span className="flex items-center">
                {/* <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg> */}
                Register
              </span>
            )}
          </motion.button>
        </motion.div>

        {/* Payment Section */}
        <AnimatePresence>
          {registrationData && registrationData.payment_url && (
            <motion.div
              ref={paymentSectionRef}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -30 }}
              className="mt-8 bg-white rounded-2xl shadow-lg border border-gray-200 p-8 md:p-12">
              <div className="text-center">
                <h3 className="text-3xl font-bold primary-color mb-4">Complete Your Payment</h3>
                <p className="text-gray-600 text-lg mb-8 max-w-md mx-auto">
                  You're almost there! Complete your payment of ₹5000 to secure your spot at the
                  event.
                </p>

                {registrationData.qr_base64 ? (
                  <div className="flex flex-col md:flex-row justify-center items-center gap-6 mb-6">
                    {/* QR Code */}
                    <div className="bg-gray-50 p-8 rounded-2xl shadow-inner border border-gray-200">
                      <motion.img
                        src={`data:image/png;base64,${registrationData.qr_base64}`}
                        alt="Payment QR Code"
                        className="w-48 h-48 mx-auto rounded-xl"
                        onError={() =>
                          setQrError('Failed to load QR code. Please use the payment link.')
                        }
                        whileHover={{ scale: 1.02 }}
                        transition={{ duration: 0.3 }}
                      />
                      {qrError ? (
                        <motion.p
                          className="error-color text-sm mt-4 font-medium"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}>
                          {qrError}
                        </motion.p>
                      ) : (
                        <p className="text-gray-600 mt-4 font-medium">Scan with any UPI app</p>
                      )}
                    </div>

                    {/* Pay Now Button */}
                    <motion.a
                      whileHover={{ scale: 1.02, y: -2 }}
                      whileTap={{ scale: 0.98 }}
                      href={registrationData.payment_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center px-8 py-4 success-bg text-white rounded-xl font-semibold text-lg hover:shadow-xl transition-all duration-300">
                      <svg
                        className="w-5 h-5 mr-3"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                        />
                      </svg>
                      Pay Now - ₹5000
                    </motion.a>
                  </div>
                ) : (
                  <p className="error-color font-medium mb-6">
                    QR code not available. Please use the payment link below.
                  </p>
                )}

                {/* Payment Status Indicator */}
                {isCheckingStatus && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-xl">
                    <div className="flex items-center justify-center">
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{
                          duration: 1,
                          repeat: Number.POSITIVE_INFINITY,
                          ease: 'linear'
                        }}
                        className="w-5 h-5 border-2 warning-color border-t-transparent rounded-full mr-3"
                      />
                      <p className="warning-color font-medium">Checking payment status...</p>
                    </div>
                  </motion.div>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Payment Status */}
        <AnimatePresence>
          {paymentStatus && !showSuccessModal && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="mt-8 p-6 bg-yellow-50 border border-yellow-200 rounded-xl text-center shadow-lg">
              <div className="flex items-center justify-center mb-2">
                <svg
                  className="w-6 h-6 warning-color mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <p className="warning-color font-bold text-lg">
                  Payment Status: {paymentStatus.status.toUpperCase()}
                </p>
              </div>
              {paymentStatus.paidAt && (
                <p className="text-yellow-700">
                  Paid At: {new Date(paymentStatus.paidAt).toLocaleString()}
                </p>
              )}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Success Modal */}
        <AnimatePresence>
          {showSuccessModal && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
              <motion.div
                initial={{ scale: 0.8, opacity: 0, y: 50 }}
                animate={{ scale: 1, opacity: 1, y: 0 }}
                exit={{ scale: 0.8, opacity: 0, y: 50 }}
                transition={{ type: 'spring', duration: 0.6 }}
                className="bg-white p-10 rounded-2xl shadow-2xl max-w-md w-full text-center relative overflow-hidden">
                <div className="absolute inset-0 bg-green-50 opacity-50" />

                <motion.div
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
                  className="w-20 h-20 success-bg rounded-full mx-auto mb-6 flex items-center justify-center shadow-lg relative z-10">
                  <svg
                    className="w-10 h-10 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </motion.div>

                <motion.h3
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  className="text-3xl font-bold primary-color mb-4 relative z-10">
                  Registration Successful!
                </motion.h3>

                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                  className="text-gray-600 mb-8 text-lg relative z-10">
                  Congratulations! You have successfully registered for the event. A confirmation
                  email has been sent to your inbox.
                </motion.p>

                <motion.button
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                  whileHover={{ scale: 1.02, y: -2 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={handleModalClose}
                  className="primary-bg primary-hover text-white px-8 py-3 rounded-xl font-semibold text-lg hover:shadow-lg transition-all duration-300 relative z-10">
                  Close
                </motion.button>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default RegistrationForm;
