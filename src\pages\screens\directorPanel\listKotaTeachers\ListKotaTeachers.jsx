"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import {
  useDirectorListKotaTeacherServiceQuery,
  useDirectorMapKotaTeacherServiceMutation,
} from "../addKotaTeachers/addKotaTeacher.Slice"
import { useDirectorListCenterServiceQuery } from "../addCenter/addCenter.Slice"
import Toastify from "../../../../components/PopUp/Toastify"
import { FiUsers, FiMail, FiBook, FiMapPin, FiAlertCircle, FiSearch, FiGrid, FiList } from "react-icons/fi"
import { FaGraduationCap } from "react-icons/fa6"

const ListKotaTeachers = () => {
  const {
    data: kotaTeachers,
    isLoading: teachersLoading,
    isError: teachersError,
    error: teachersErrorData,
  } = useDirectorListKotaTeacherServiceQuery()
  const {
    data: centers,
    isLoading: centersLoading,
    isError: centersError,
    error: centersErrorData,
  } = useDirectorListCenterServiceQuery()
  const [mapKotaTeacherToCenter] = useDirectorMapKotaTeacherServiceMutation()
  const [res, setRes] = useState(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [courseFilter, setCourseFilter] = useState("")
  const [viewMode, setViewMode] = useState("grid") // grid or table
  const [mappingTeacherId, setMappingTeacherId] = useState(null)

  // Safety check: Ensure data is arrays
  const teachersArray = Array.isArray(kotaTeachers) ? kotaTeachers : []
  const centersArray = Array.isArray(centers) ? centers : []

  // Filter teachers based on search and course filter
 const filteredTeachers = teachersArray.filter((teacher) => {
  const matchesSearch =
    `${teacher.first_name} ${teacher.last_name}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
    teacher.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
    teacher.email.toLowerCase().includes(searchTerm.toLowerCase());
  const matchesCourse = !courseFilter || teacher.course === courseFilter;

  console.log('Teacher Check:', {
    name: `${teacher.first_name} ${teacher.last_name}`,
    course: teacher.course_name || 'No course',
    matchesSearch,
    matchesCourse,
    courseFilter
  });

  return matchesSearch && matchesCourse;
});

console.log('Filtered Teachers:', filteredTeachers);

  const handleMapTeacher = async (teacherId, centerCode) => {
    setMappingTeacherId(teacherId)
    try {
      const response = await mapKotaTeacherToCenter({ teacherId, centerCode }).unwrap()
      setRes({
        status: "success",
        message: response.message || "Teacher mapped successfully",
      })
    } catch (error) {
      setRes({
        status: "error",
        message: error.message || error.data?.message || "Failed to map teacher to center",
      })
    } finally {
      setMappingTeacherId(null)
    }
  }

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4, ease: "easeOut" },
    },
  }

  const cardVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.3 },
    },
    hover: {
      scale: 1.02,
      boxShadow: "0px 10px 30px rgba(0, 0, 0, 0.1)",
      transition: { duration: 0.2 },
    },
  }

  const buttonVariants = {
    hover: {
      scale: 1.05,
      boxShadow: "0px 8px 20px rgba(0, 0, 0, 0.15)",
      transition: { duration: 0.2 },
    },
    tap: { scale: 0.95 },
  }

  if (teachersLoading || centersLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 p-4 md:p-8 flex items-center justify-center">
        <motion.div
          className="text-center"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          <motion.div
            className="inline-block w-12 h-12 border-4 border-gray-300 border-t-[var(--color-director)] rounded-full mb-4"
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: "linear" }}
          />
          <p className="text-gray-600 text-lg">Loading Kota teachers...</p>
        </motion.div>
      </div>
    )
  }

  if (teachersError || centersError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 p-4 md:p-8 flex items-center justify-center">
        <motion.div
          className="text-center bg-white rounded-3xl p-8 shadow-2xl"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          <FiAlertCircle className="mx-auto text-6xl text-red-500 mb-4" />
          <h3 className="text-xl font-bold text-gray-900 mb-2">Error Loading Data</h3>
          <p className="text-red-500">
            {teachersErrorData?.message || centersErrorData?.message || "Failed to load data"}
          </p>
        </motion.div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 p-4 md:p-8">
      <Toastify res={res} resClear={() => setRes(null)} />

      <motion.div className="max-w-7xl mx-auto" variants={containerVariants} initial="hidden" animate="visible">
        {/* Header Section */}
        <motion.div className="text-center mb-8" variants={itemVariants}>
          <motion.div
            className="inline-flex items-center justify-center w-16 h-16 rounded-full mb-4"
            style={{
              background: `linear-gradient(135deg, var(--color-director), var(--color-director))`,
              boxShadow: "0px 10px 30px rgba(0, 0, 0, 0.1)",
            }}
            whileHover={{ scale: 1.1, rotate: 5 }}
          >
            <FaGraduationCap className=" text-4xl"  style={{ color: "white" }} />
          </motion.div>
          <motion.h1 className="text-4xl font-bold mb-2" style={{ color: "var(--color-director)" }}>
            Kota Teachers
          </motion.h1>
          <motion.p className="text-gray-600 text-lg">Manage and assign Kota teachers to different centers</motion.p>
          <motion.div className="mt-4 inline-flex items-center px-4 py-2 bg-white rounded-full shadow-lg">
            <FiUsers className="mr-2 text-gray-500" />
            <span className="text-gray-700 font-medium">{filteredTeachers.length} teachers available</span>
          </motion.div>
        </motion.div>

        {/* Controls Section */}
        <motion.div
          className="bg-white rounded-3xl shadow-2xl overflow-hidden mb-8"
          variants={itemVariants}
          style={{
            background: "linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)",
            border: "1px solid rgba(0, 0, 0, 0.05)",
          }}
        >
          <div
            className="px-8 py-6 "
            style={{
              background: `linear-gradient(135deg, var(--color-director), var(--color-director)dd)`,
            }}
          >
            <div className="flex flex-col md:flex-row md:items-center md:justify-between">
              <div>
                <h3 className="text-2xl font-bold">Teacher Management</h3>
                <p className="text-opacity-90 mt-1">Search, filter, and assign teachers to centers</p>
              </div>
              <div className="flex items-center space-x-4 mt-4 md:mt-0">
                {/* Search */}
                <div className="relative">
                  <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2  text-opacity-70" />
                  <input
                    type="text"
                    placeholder="Search teachers..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 pr-4 py-2 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg  placeholder-white placeholder-opacity-70 focus:outline-none focus:bg-opacity-30"
                  />
                </div>
                {/* Course Filter */}
                <select
                  value={courseFilter}
                  onChange={(e) => setCourseFilter(e.target.value)}
                  className="px-4 py-2 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg  focus:outline-none focus:bg-opacity-30"
                  style={{
                    color: 'var(--color-director)',
                  }}
                >
                  <option value="" className="text-gray-900">
                    All Courses
                  </option>
                  <option value="JEE" className="text-gray-900">
                    JEE
                  </option>
                  <option value="NEET" className="text-gray-900">
                    NEET
                  </option>
                </select>
                {/* View Mode Toggle */}
                <div className="flex bg-white bg-opacity-20 rounded-lg p-1">
                  <button
                    onClick={() => setViewMode("grid")}
                    className={`p-2 rounded ${viewMode === "grid" ? "bg-white bg-opacity-30" : ""}`}
                  >
                    <FiGrid style={{ color: "var(--color-director)" }} />
                  </button>
                  <button
                    onClick={() => setViewMode("table")}
                    className={`p-2 rounded ${viewMode === "table" ? "bg-white bg-opacity-30" : ""}`}
                  >
                    <FiList style={{ color: "var(--color-director)" }} />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Teachers Content */}
        <motion.div
          className="bg-white rounded-3xl shadow-2xl overflow-hidden"
          variants={itemVariants}
          style={{
            background: "linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)",
            border: "1px solid rgba(0, 0, 0, 0.05)",
          }}
        >
          <div className="p-8">
            {filteredTeachers.length === 0 ? (
              <div className="text-center py-12">
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5 }}
                >
                  <FaGraduationCap className="mx-auto text-6xl text-gray-300 mb-4" />
                  <h3 className="text-xl font-bold text-gray-900 mb-2">No Teachers Found</h3>
                  <p className="text-gray-600">
                    {searchTerm || courseFilter ? "No teachers match your search criteria" : "No teachers available"}
                  </p>
                </motion.div>
              </div>
            ) : viewMode === "grid" ? (
              /* Grid View */
              <motion.div
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                variants={containerVariants}
                initial="hidden"
                animate="visible"
              >
                {filteredTeachers.map((teacher, index) => (
                  <motion.div
                    key={teacher.id}
                    className="bg-gradient-to-br from-white to-gray-50 rounded-2xl shadow-lg border border-gray-100 overflow-hidden"
                    variants={cardVariants}
                    whileHover="hover"
                    initial="hidden"
                    animate="visible"
                    transition={{ delay: index * 0.1 }}
                  >
                    <div className="p-6">
                      <div className="flex items-center mb-4">
                        <div
                          className="w-12 h-12 rounded-full flex items-center justify-center text-white font-bold mr-4"
                          style={{ backgroundColor: "var(--color-director)" }}
                        >
                          {teacher.first_name.charAt(0)}
                          {teacher.last_name.charAt(0)}
                        </div>
                        <div>
                          <h4 className="font-bold text-gray-900 text-lg">
                            {teacher.first_name} {teacher.last_name}
                          </h4>
                          <p className="text-gray-600 text-sm">@{teacher.username}</p>
                        </div>
                      </div>
                      <div className="space-y-3 mb-4">
                        <div className="flex items-center text-sm text-gray-600">
                          <FiMail className="mr-2" />
                          {teacher.email}
                        </div>
                        <div className="flex items-center text-sm text-gray-600">
                          <FiBook className="mr-2" />
                          <span
                            className="px-2 py-1 rounded-full text-xs font-medium text-white"
                            style={{ backgroundColor: "var(--color-director)" }}
                          >
                            {teacher.course_name}
                          </span>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <label className="text-xs font-medium text-gray-700 flex items-center">
                          <FiMapPin className="mr-1" />
                          Assign to Center
                        </label>
                        <select
                          onChange={(e) => {
                            if (e.target.value) {
                              handleMapTeacher(teacher.id, e.target.value)
                              e.target.value = ""
                            }
                          }}
                          className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[var(--color-director)] focus:border-transparent"
                          disabled={centersArray.length === 0 || mappingTeacherId === teacher.id}
                        >
                          <option value="">
                            {centersArray.length === 0 ? "No centers available" : "Select Center"}
                          </option>
                          {centersArray.map((center) => (
                            <option key={center.center_code} value={center.center_code}>
                              {center.name} ({center.center_code})
                            </option>
                          ))}
                        </select>
                        {mappingTeacherId === teacher.id && (
                          <div className="flex items-center justify-center py-2">
                            <motion.div
                              className="w-4 h-4 border-2 border-gray-300 border-t-[var(--color-director)] rounded-full"
                              animate={{ rotate: 360 }}
                              transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: "linear" }}
                            />
                            <span className="ml-2 text-sm text-gray-600">Mapping...</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            ) : (
              /* Table View */
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 border-b border-gray-200">
                    <tr>
                      <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">Teacher</th>
                      <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">Username</th>
                      <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">Course</th>
                      <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">Email</th>
                      <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-100">
                    <AnimatePresence>
                      {filteredTeachers.map((teacher, index) => (
                        <motion.tr
                          key={teacher.id}
                          className="hover:bg-gray-50 transition-all duration-300"
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: 20 }}
                          transition={{ delay: index * 0.05 }}
                          whileHover={{ backgroundColor: "#f9fafb" }}
                        >
                          <td className="px-6 py-4">
                            <div className="flex items-center">
                              {/* <div
                                className="w-10 h-10 rounded-full flex items-center justify-center text-white font-bold mr-3"
                                style={{ backgroundColor: "var(--color-director)" }}
                              >
                                {teacher.first_name.charAt(0)}
                                {teacher.last_name.charAt(0)}
                              </div> */}
                              <div>
                                <div className="font-medium text-gray-900">
                                  {teacher.first_name} {teacher.last_name}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 text-gray-900 font-medium">@{teacher.username}</td>
                          <td className="px-6 py-4">
                            <span
                              className="px-3 py-1 rounded-full text-xs font-medium text-white"
                              style={{ backgroundColor: "var(--color-director)" }}
                            >
                              {teacher.course_name}
                            </span>
                          </td>
                          <td className="px-6 py-4 text-gray-600">{teacher.email}</td>
                          <td className="px-6 py-4">
                            <div className="flex items-center space-x-2">
                              <select
                                onChange={(e) => {
                                  if (e.target.value) {
                                    handleMapTeacher(teacher.id, e.target.value)
                                    e.target.value = ""
                                  }
                                }}
                                className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[var(--color-director)] focus:border-transparent"
                                disabled={centersArray.length === 0 || mappingTeacherId === teacher.id}
                              >
                                <option value="">{centersArray.length === 0 ? "No centers" : "Assign Center"}</option>
                                {centersArray.map((center) => (
                                  <option key={center.center_code} value={center.center_code}>
                                    {center.name} ({center.center_code})
                                  </option>
                                ))}
                              </select>
                              {mappingTeacherId === teacher.id && (
                                <motion.div
                                  className="w-4 h-4 border-2 border-gray-300 border-t-[var(--color-director)] rounded-full"
                                  animate={{ rotate: 360 }}
                                  transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: "linear" }}
                                />
                              )}
                            </div>
                          </td>
                        </motion.tr>
                      ))}
                    </AnimatePresence>
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </div>
  )
}

export default ListKotaTeachers
