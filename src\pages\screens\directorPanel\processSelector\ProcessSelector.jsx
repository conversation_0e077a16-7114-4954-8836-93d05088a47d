import React, { useState } from 'react';

import TopicMapping from './TopicMapping';
import SubTopicMapping from './SubTopicMapping';
import ContentMapping from './ContentMapping';
import Button from '../../../../components/Field/Button';

const ProcessSelector = () => {
  const [activeTab, setActiveTab] = useState('topic');

  const tabs = [
    { id: 'topic', label: 'Topic Mapping', component: <TopicMapping /> },
    { id: 'subtopic', label: 'Sub-Topic Mapping', component: <SubTopicMapping /> },
    { id: 'content', label: 'Content Mapping', component: <ContentMapping /> }
  ];

  const activeComponent = tabs.find((tab) => tab.id === activeTab)?.component;

  return (
    <div className="w-full mx-auto">
      <div className="border-b border-gray-200">
        <nav className="flex space-x-6" aria-label="Tabs">
          {tabs.map((tab) => (
            <Button
              key={tab.id}
              name={tab.label}
              onClick={() => setActiveTab(tab.id)}
              className={`
                shrink-0 px-1 text-sm font-medium transition-colors duration-200 ease-in-out
                ${
                  activeTab === tab.id
                    ? 'border-b-2 border-[var(--color-director)] text-[var(--color-director)]'
                    : 'border-b-2 border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                }
              `}
            />
          ))}
        </nav>
      </div>

      <div className="p-2">{activeComponent}</div>
    </div>
  );
};

export default ProcessSelector;