import React, { useEffect, useState, useMemo } from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, PerspectiveCamera, Environment } from '@react-three/drei';
import { Avatars } from './Avatars';

export default function AvatarsWorld({ rawPhonemeString = '' }) {
  const [currentViseme, setCurrentViseme] = useState('');
  const [expression, setExpression] = useState('neutral');

  const phonemeTimeline = useMemo(() => {
    if (!rawPhonemeString) return [];
    const rawPhonemes = rawPhonemeString.trim().split(/\s+/);

    return rawPhonemes
      .map((ph, i) => {
        const match = ph.match(/^([a-zA-Z]+)[0-2]?(-ph)?$/);
        if (!match) return null;
        const visemeKey = match[1].toUpperCase();
        return {
          time: i * 100,
          viseme: visemeKey,
          expression: 'neutral',
          duration: 100
        };
      })
      .filter(Boolean);
  }, [rawPhonemeString]);

  useEffect(() => {
    if (phonemeTimeline.length === 0) return;
    let index = 0;
    let timeoutId;

    const playNext = () => {
      const { viseme, expression, duration } = phonemeTimeline[index];
      setCurrentViseme(viseme);
      setExpression(expression);
      index++;

      if (index < phonemeTimeline.length) {
        timeoutId = setTimeout(playNext, duration || 100);
      } else {
        setTimeout(() => {
          setCurrentViseme('');
          setExpression('neutral');
        }, 150);
      }
    };

    playNext();
    return () => clearTimeout(timeoutId);
  }, [phonemeTimeline]);

  return (
    <Canvas shadows dpr={[1, 2]} camera={{ position: [0.5, 0, 1], fov: 20 }}>
      <PerspectiveCamera makeDefault position={[0, 0.1, 1]} fov={20} />
      <directionalLight castShadow position={[0, 2, 2]} intensity={0.6} />
      <directionalLight position={[2, 6, 5]} intensity={0.3} color="#FDF4DC" />
      <Environment preset="sunset" />
      <mesh receiveShadow rotation={[-Math.PI / 2, 0, 0]} position={[0, 0.2, 0]}>
        <planeGeometry args={[10, 10]} />
        <shadowMaterial transparent opacity={0.2} />
      </mesh>

      <Avatars
        position={[0, -1.37, 0]}
        scale={[1.5, 1.5, 1.5]}
        currentViseme={currentViseme}
        expression={expression}
      />

      <OrbitControls
        enablePan={false}
        enableZoom={false}
        enableRotate={false}
        maxPolarAngle={Math.PI / 2}
      />
    </Canvas>
  );
}
