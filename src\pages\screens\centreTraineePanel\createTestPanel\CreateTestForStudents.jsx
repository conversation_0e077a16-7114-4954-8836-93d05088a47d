'use client';

import { useState, useRef, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import { useCreateEvaluationServiceMutation, setEvaluationData } from './evaluation.Slice';
import { useListSubjectsQuery } from '../../directorPanel/subjects/subjects.Slice';
import { useLazyGetCenterStudentsFacultyQuery } from '../overView/centerTraineedOverview.slice';
import Toastify from '../../../../components/PopUp/Toastify';
import {
  FaClipboardList,
  FaPen,
  FaUserGraduate,
  FaIdBadge,
  FaUsers,
  FaGraduationCap,
  FaBookOpen,
  FaGlobe,
  FaBuilding,
  FaFileUpload,
  FaCloudUploadAlt,
  FaCheckCircle,
  FaWeightHanging,
  FaFilePdf,
  FaPlus,
  FaRedo,
  FaExclamationTriangle,
  FaChevronDown,
  FaCog,
  FaTimes,
  FaPencilAlt
} from 'react-icons/fa';

const CreateTestForStudents = () => {
  const dispatch = useDispatch();
  const { EvaluationData } = useSelector((state) => state.evaluation || { EvaluationData: null });

  const [createEvaluation, { isLoading: isCreatingEvaluation }] = useCreateEvaluationServiceMutation();
  const { data: subjectsData, error: subjectsError, isLoading: isLoadingSubjects } = useListSubjectsQuery();
  const [triggerGetCenterData, { data: centerData, error: centerError, isLoading: isLoadingCenterData }] = useLazyGetCenterStudentsFacultyQuery();

  const [res, setRes] = useState(null);
  const [evaluationFile, setEvaluationFile] = useState(null);
  const fileInputRef = useRef(null);

  const [evaluationForm, setEvaluationForm] = useState({
    student_id: '',
    student_name: '',
    batch_id: '',
    exam: '',
    subject: 'General',
    language: '',
    center_code: '',
    faculty_id: ''
  });

  useEffect(() => {
    const facultyId = sessionStorage.getItem('userId');
    if (facultyId) {
      setEvaluationForm((prev) => ({ ...prev, faculty_id: facultyId }));
    } else {
      console.warn('Faculty ID not found in sessionStorage');
    }
  }, []);

  useEffect(() => {
    triggerGetCenterData();
  }, [triggerGetCenterData]);

  useEffect(() => {
    if (centerData?.faculty?.center_code) {
      setEvaluationForm((prev) => ({
        ...prev,
        center_code: centerData.faculty.center_code
      }));
    }
  }, [centerData]);

  useEffect(() => {
    if (centerData) {
      console.log('Center Data:', centerData);
    } else {
      console.log('Center Data is not yet available');
    }
  }, [centerData]);

  useEffect(() => {
    console.log('EvaluationData:', EvaluationData);
  }, [EvaluationData]);

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 15 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4, ease: [0.25, 0.46, 0.45, 0.94] }
    }
  };

  const buttonVariants = {
    hover: {
      scale: 1.02,
      transition: { duration: 0.2 }
    },
    tap: { scale: 0.98, transition: { duration: 0.1 } }
  };

  const floatingVariants = {
    animate: {
      y: [-5, 5, -5],
      transition: {
        duration: 3,
        repeat: Number.POSITIVE_INFINITY,
        ease: 'easeInOut'
      }
    }
  };

  // Animation for dropdowns on page load
  const dropdownVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.5, ease: [0.25, 0.46, 0.45, 0.94] }
    }
  };

  // Corner animation for teacher evaluating
  const teacherAnimationVariants = {
    animate: {
      rotate: [0, 10, -10, 0],
      scale: [1, 1.1, 1],
      transition: {
        rotate: { duration: 1.5, repeat: Number.POSITIVE_INFINITY, ease: 'easeInOut' },
        scale: { duration: 1.5, repeat: Number.POSITIVE_INFINITY, ease: 'easeInOut' }
      }
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    if (name === 'student_id') {
      const selectedStudent = centerData?.students?.find((student) => student.id === value);
      setEvaluationForm((prev) => ({
        ...prev,
        student_id: value,
        student_name: selectedStudent ? `${selectedStudent.first_name} ${selectedStudent.last_name}` : '',
        batch_id: selectedStudent?.batch_id || '',
        exam: selectedStudent?.course || ''
      }));
    } else {
      setEvaluationForm((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      if (file.type !== 'application/pdf') {
        setRes({ status: 400, data: { message: 'Only PDF files are allowed' } });
        return;
      }
      if (file.size > 10 * 1024 * 1024) {
        setRes({ status: 400, data: { message: 'File size exceeds 10MB limit' } });
        return;
      }
      setEvaluationFile(file);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!evaluationFile) {
      setRes({ status: 400, data: { message: 'Please upload a PDF file' } });
      return;
    }
    if (!evaluationForm.batch_id) {
      setRes({ status: 400, data: { message: 'Batch ID is missing' } });
      return;
    }
    if (!evaluationForm.subject) {
      setRes({ status: 400, data: { message: 'Please select a subject' } });
      return;
    }
    if (!evaluationForm.student_id) {
      setRes({ status: 400, data: { message: 'Please select a student' } });
      return;
    }
    if (!evaluationForm.exam) {
      setRes({ status: 400, data: { message: 'Course is missing' } });
      return;
    }
    if (!evaluationForm.language) {
      setRes({ status: 400, data: { message: 'Please select a language' } });
      return;
    }
    if (!evaluationForm.faculty_id) {
      setRes({ status: 400, data: { message: 'Faculty ID is missing in session storage' } });
      return;
    }

    const formData = new FormData();
    formData.append('file', evaluationFile);
    Object.keys(evaluationForm).forEach((key) => {
      formData.append(key, evaluationForm[key]);
    });

    for (const pair of formData.entries()) {
      console.log(`${pair[0]}: ${pair[1]}`);
    }

    try {
      const response = await createEvaluation(formData).unwrap();
      console.log('Backend Response:', response);
      const evaluationData = response.data || response;
      dispatch(setEvaluationData(evaluationData));
      setRes({ status: 200, data: { message: 'Evaluation created successfully!' } });
      resetForm();
    } catch (error) {
      console.error('Submission Error:', error);
      setRes({
        status: error.status || 500,
        data: { message: error.data?.message || 'Failed to create evaluation' }
      });
    }
  };

  const resetForm = () => {
    const facultyId = sessionStorage.getItem('userId');
    setEvaluationForm({
      student_id: '',
      student_name: '',
      batch_id: '',
      exam: '',
      subject: 'General',
      language: '',
      center_code: centerData?.faculty?.center_code || '',
      faculty_id: facultyId || ''
    });
    setEvaluationFile(null);
    if (fileInputRef.current) fileInputRef.current.value = '';
  };

  const resClear = () => {
    setRes(null);
  };

  return (
    <div className="min-h-screen p-6 bg-white font-poppins relative">
      {/* Subtle floating elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <motion.div
          variants={floatingVariants}
          animate="animate"
          className="absolute top-20 left-10 w-2 h-2 bg-[#F59E0B] rounded-full opacity-30"
        />
        <motion.div
          variants={floatingVariants}
          animate="animate"
          style={{ animationDelay: '1s' }}
          className="absolute top-40 right-20 w-1 h-1 bg-[#F59E0B] rounded-full opacity-40"
        />
        <motion.div
          variants={floatingVariants}
          animate="animate"
          style={{ animationDelay: '2s' }}
          className="absolute bottom-20 left-1/4 w-3 h-3 bg-[#F59E0B] rounded-full opacity-20"
        />
      </div>

      {/* Teacher Evaluating Animation in Top-Right Corner */}
      {/* <motion.div
        variants={teacherAnimationVariants}
        animate="animate"
        className="absolute top-4 right-4 w-16 h-16 bg-[#F59E0B] rounded-full flex items-center justify-center shadow-lg z-20"
      >
        <FaPencilAlt className="text-white text-2xl" />
      </motion.div> */}

      <Toastify res={res} resClear={resClear} />

      <div className="relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="max-w-5xl mx-auto space-y-10">
          {/* Clean Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="text-center mb-16">
            <motion.h1
              className="text-5xl md:text-6xl font-bold mb-6 text-[#F59E0B]"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.3 }}
            >
              <FaClipboardList className="inline-block mr-4" />
              Test Evaluation
            </motion.h1>
            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
              className="text-xl text-gray-600 font-light max-w-2xl mx-auto leading-relaxed"
            >
              Create and manage student evaluations with precision and ease
            </motion.p>
          </motion.div>

          {/* Clean Form Container */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="bg-white rounded-3xl shadow-2xl border-2 border-[#F59E0B]/20 bg-gradient-to-br from-white to-[#F59E0B]/5 overflow-hidden"
          >
            <div className="p-8 md:p-12">
              {/* Form Header */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
                className="flex items-center mb-10 pb-6 border-b-2 border-[#F59E0B]/20"
              >
                <motion.div
                  whileHover={{ rotate: 5, scale: 1.05 }}
                  transition={{ duration: 0.3 }}
                  className="w-16 h-16 rounded-2xl bg-[#F59E0B] flex items-center justify-center mr-6 shadow-lg"
                >
                  <FaPen className="text-white text-2xl" />
                </motion.div>
                <div>
                  <h2 className="text-3xl font-bold text-gray-800 mb-2">Create New Evaluation</h2>
                  <p className="text-gray-600 text-lg">
                    Upload student answer sheets and generate detailed assessments
                  </p>
                </div>
              </motion.div>

              <form onSubmit={handleSubmit} className="space-y-8">
                <motion.div
                  variants={containerVariants}
                  initial="hidden"
                  animate="visible"
                  className="grid grid-cols-1 lg:grid-cols-2 gap-8"
                >
                  {/* Student Selection */}
                  <motion.div variants={dropdownVariants} className="space-y-3">
                    <label className="flex items-center text-sm font-semibold text-gray-700 mb-3">
                      <motion.div whileHover={{ scale: 1.1 }} className="mr-3">
                        <FaUserGraduate className="text-[#F59E0B]" />
                      </motion.div>
                      Select Student
                    </label>
                    <motion.div whileHover={{ scale: 1.01 }} className="relative">
                      <select
                        name="student_id"
                        value={evaluationForm.student_id}
                        onChange={handleInputChange}
                        className="w-full px-4 py-4 border-2 border-[#F59E0B]/50 rounded-xl text-gray-700 bg-white shadow-sm focus:border-[#F59E0B] focus:ring-4 focus:ring-[#F59E0B]/20 transition-all duration-300 text-base appearance-none cursor-pointer"
                        disabled={isLoadingCenterData}
                      >
                        <option value="">Choose a student...</option>
                        {centerData?.students?.length > 0 ? (
                          centerData.students.map((student) => (
                            <option key={student.id} value={student.id}>
                              {student.first_name} {student.last_name} ({student.student_email})
                            </option>
                          ))
                        ) : (
                          <option value="" disabled>
                            {isLoadingCenterData ? 'Loading students...' : 'No students available'}
                          </option>
                        )}
                      </select>
                      <FaChevronDown className="absolute right-4 top-1/2 transform -translate-y-1/2 text-[#F59E0B] pointer-events-none" />
                    </motion.div>
                    {centerError && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="flex items-center text-red-600 bg-red-50 p-3 rounded-lg border border-red-200"
                      >
                        <FaExclamationTriangle className="mr-2" />
                        <span className="text-sm">
                          Error loading students: {centerError.data?.message || 'Please try again'}
                        </span>
                      </motion.div>
                    )}
                  </motion.div>

                  {/* Student Name Display */}
                  <motion.div variants={dropdownVariants} className="space-y-3">
                    <label className="flex items-center text-sm font-semibold text-gray-700 mb-3">
                      <motion.div whileHover={{ scale: 1.1 }} className="mr-3">
                        <FaIdBadge className="text-[#F59E0B]" />
                      </motion.div>
                      Student Name
                    </label>
                    <motion.input
                      whileHover={{ scale: 1.01 }}
                      type="text"
                      name="student_name"
                      placeholder="Auto-filled when student is selected"
                      value={evaluationForm.student_name}
                      readOnly
                      className="w-full px-4 py-4 border-2 border-[#F59E0B]/50 rounded-xl text-gray-500 bg-gray-50 cursor-not-allowed text-base shadow-sm transition-all duration-300"
                    />
                  </motion.div>

                  {/* Batch Display */}
                  <motion.div variants={dropdownVariants} className="space-y-3">
                    <label className="flex items-center text-sm font-semibold text-gray-700 mb-3">
                      <motion.div whileHover={{ scale: 1.1 }} className="mr-3">
                        <FaUsers className="text-[#F59E0B]" />
                      </motion.div>
                      Batch
                    </label>
                    <motion.input
                      whileHover={{ scale: 1.01 }}
                      type="text"
                      name="batch_id"
                      placeholder="Auto-filled when student is selected"
                      value={evaluationForm.batch_id}
                      readOnly
                      className="w-full px-4 py-4 border-2 border-[#F59E0B]/50 rounded-xl text-gray-500 bg-gray-50 cursor-not-allowed text-base shadow-sm transition-all duration-300"
                    />
                  </motion.div>

                  {/* Course (Exam) Display */}
                  <motion.div variants={dropdownVariants} className="space-y-3">
                    <label className="flex items-center text-sm font-semibold text-gray-700 mb-3">
                      <motion.div whileHover={{ scale: 1.1 }} className="mr-3">
                        <FaGraduationCap className="text-[#F59E0B]" />
                      </motion.div>
                      Course
                    </label>
                    <motion.input
                      whileHover={{ scale: 1.01 }}
                      type="text"
                      name="exam"
                      placeholder="Auto-filled when student is selected"
                      value={evaluationForm.exam}
                      readOnly
                      className="w-full px-4 py-4 border-2 border-[#F59E0B]/50 rounded-xl text-gray-500 bg-gray-50 cursor-not-allowed text-base shadow-sm transition-all duration-300"
                    />
                  </motion.div>

                  {/* Subject Selection */}
                  <motion.div variants={dropdownVariants} className="space-y-3">
                    <label className="flex items-center text-sm font-semibold text-gray-700 mb-3">
                      <motion.div whileHover={{ scale: 1.1 }} className="mr-3">
                        <FaBookOpen className="text-[#F59E0B]" />
                      </motion.div>
                      Select Subject
                    </label>
                    <motion.div whileHover={{ scale: 1.01 }} className="relative">
                      <select
                        name="subject"
                        value={evaluationForm.subject}
                        onChange={handleInputChange}
                        className="w-full px-4 py-4 border-2 border-[#F59E0B]/50 rounded-xl text-gray-700 bg-white shadow-sm focus:border-[#F59E0B] focus:ring-4 focus:ring-[#F59E0B]/20 transition-all duration-300 text-base appearance-none cursor-pointer"
                        disabled={isLoadingSubjects}
                      >
                        <option value="">Choose a subject...</option>
                        {subjectsData && subjectsData.length > 0 ? (
                          subjectsData.map((subject) => (
                            <option key={subject.subject_id} value={subject.subject_name}>
                              {subject.subject_name}
                            </option>
                          ))
                        ) : (
                          <option value="" disabled>
                            {isLoadingSubjects ? 'Loading subjects...' : 'No subjects available'}
                          </option>
                        )}
                      </select>
                      <FaChevronDown className="absolute right-4 top-1/2 transform -translate-y-1/2 text-[#F59E0B] pointer-events-none" />
                    </motion.div>
                    {subjectsError && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="flex items-center text-red-600 bg-red-50 p-3 rounded-lg border border-red-200"
                      >
                        <FaExclamationTriangle className="mr-2" />
                        <span className="text-sm">
                          Error loading subjects: {subjectsError.data?.message || 'Please try again'}
                        </span>
                      </motion.div>
                    )}
                  </motion.div>

                  {/* Language Selection */}
                  <motion.div variants={dropdownVariants} className="space-y-3">
                    <label className="flex items-center text-sm font-semibold text-gray-700 mb-3">
                      <motion.div whileHover={{ scale: 1.1 }} className="mr-3">
                        <FaGlobe className="text-[#F59E0B]" />
                      </motion.div>
                      Select Language
                    </label>
                    <motion.div whileHover={{ scale: 1.01 }} className="relative">
                      <select
                        name="language"
                        value={evaluationForm.language}
                        onChange={handleInputChange}
                        className="w-full px-4 py-4 border-2 border-[#F59E0B]/50 rounded-xl text-gray-700 bg-white shadow-sm focus:border-[#F59E0B] focus:ring-4 focus:ring-[#F59E0B]/20 transition-all duration-300 text-base appearance-none cursor-pointer"
                      >
                        <option value="">Choose language...</option>
                        <option value="tanglish">Tamil + English</option>
                        <option value="english">English</option>
                        <option value="kanglish">Kannada + English</option>
                      </select>
                      <FaChevronDown className="absolute right-4 top-1/2 transform -translate-y-1/2 text-[#F59E0B] pointer-events-none" />
                    </motion.div>
                  </motion.div>

                  {/* Center Code Display */}
                  <motion.div variants={dropdownVariants} className="space-y-3">
                    <label className="flex items-center text-sm font-semibold text-gray-700 mb-3">
                      <motion.div whileHover={{ scale: 1.1 }} className="mr-3">
                        <FaBuilding className="text-[#F59E0B]" />
                      </motion.div>
                      Center Code
                    </label>
                    <motion.input
                      whileHover={{ scale: 1.01 }}
                      type="text"
                      name="center_code"
                      placeholder="Auto-filled from center data"
                      value={evaluationForm.center_code}
                      readOnly
                      className="w-full px-4 py-4 border-2 border-[#F59E0B]/50 rounded-xl text-gray-500 bg-gray-50 cursor-not-allowed text-base shadow-sm transition-all duration-300"
                    />
                  </motion.div>
                </motion.div>

                {/* File Upload Section */}
                <motion.div variants={itemVariants} className="space-y-4">
                  <label className="flex items-center text-sm font-semibold text-gray-700 mb-4">
                    <motion.div whileHover={{ scale: 1.1 }} className="mr-3">
                      <FaFileUpload className="text-[#F59E0B]" />
                    </motion.div>
                    Upload Answer Sheet (PDF)
                  </label>

                  <motion.div
                    whileHover={{ scale: 1.01 }}
                    whileTap={{ scale: 0.99 }}
                    className="relative group"
                  >
                    <div className="bg-gray-50 border-2 border-dashed border-[#F59E0B]/50 rounded-2xl p-10 text-center hover:border-[#F59E0B] hover:bg-[#F59E0B]/10 transition-all duration-300 group-hover:shadow-lg">
                      <input
                        type="file"
                        name="file"
                        accept="application/pdf"
                        onChange={handleFileChange}
                        className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                        ref={fileInputRef}
                        aria-required="true"
                      />
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
                        className="mb-4"
                      >
                        <FaCloudUploadAlt className="text-5xl text-[#F59E0B] mb-4" />
                      </motion.div>
                      <h3 className="text-xl font-semibold text-gray-700 mb-2">
                        Drop your PDF here
                      </h3>
                      <p className="text-gray-500 mb-4">or click to browse files</p>
                      <div className="flex items-center justify-center space-x-6 text-sm text-gray-500">
                        <span className="flex items-center">
                          <FaCheckCircle className="text-green-500 mr-2" />
                          PDF Format
                        </span>
                        <span className="flex items-center">
                          <FaWeightHanging className="text-[#F59E0B] mr-2" />
                          Max 10MB
                        </span>
                      </div>
                    </div>
                  </motion.div>

                  {evaluationFile && (
                    <motion.div
                      initial={{ opacity: 0, y: 20, scale: 0.95 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      className="bg-green-50 border-2 border-green-200 rounded-2xl p-6"
                    >
                      <div className="flex items-center">
                        <motion.div
                          animate={{ rotate: [0, 5, -5, 0] }}
                          transition={{ duration: 0.5, repeat: 2 }}
                          className="mr-4"
                        >
                          <FaFilePdf className="text-green-600 text-2xl" />
                        </motion.div>
                        <div>
                          <h4 className="text-gray-800 font-semibold text-lg">
                            File Selected Successfully!
                          </h4>
                          <p className="text-green-700 font-medium">{evaluationFile.name}</p>
                          <p className="text-green-600 text-sm">
                            Size: {(evaluationFile.size / (1024 * 1024)).toFixed(2)} MB
                          </p>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </motion.div>

                {/* Action Buttons */}
                <motion.div
                  variants={itemVariants}
                  className="flex flex-col sm:flex-row gap-4 pt-6"
                >
                  <motion.button
                    type="submit"
                    disabled={isCreatingEvaluation || isLoadingSubjects || isLoadingCenterData}
                    variants={buttonVariants}
                    whileHover="hover"
                    whileTap="tap"
                    className="bg-[#F59E0B] text-white px-6 py-3 rounded-2xl font-semibold text-base shadow-md hover:bg-[#D97706] hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 border-2 border-[#F59E0B]"
                  >
                    <span className="flex items-center justify-center">
                      {isCreatingEvaluation ? (
                        <>
                          <motion.div
                            animate={{ rotate: 360 }}
                            transition={{
                              duration: 1,
                              repeat: Number.POSITIVE_INFINITY,
                              ease: 'linear'
                            }}
                            className="w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"
                          />
                          Processing...
                        </>
                      ) : (
                        <>
                          <FaPlus className="mr-2" />
                          Create Evaluation
                        </>
                      )}
                    </span>
                  </motion.button>

                  <motion.button
                    type="button"
                    onClick={resetForm}
                    variants={buttonVariants}
                    whileHover="hover"
                    whileTap="tap"
                    className="px-6 py-3 border-2 border-[#F59E0B]/50 text-[#F59E0B] rounded-2xl font-semibold text-base hover:border-[#F59E0B] hover:text-[#D97706] hover:shadow-md transition-all duration-300"
                  >
                    <FaRedo className="mr-2" />
                    Reset Form
                  </motion.button>
                </motion.div>
              </form>
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Clean Loading Overlay */}
      <AnimatePresence>
        {isCreatingEvaluation && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-white/90 backdrop-blur-sm flex items-center justify-center z-50"
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              className="bg-white rounded-3xl p-12 text-center shadow-2xl border-2 border-[#F59E0B]/20 max-w-md mx-4"
            >
              <motion.div
                animate={{
                  rotate: 360,
                  scale: [1, 1.1, 1]
                }}
                transition={{
                  rotate: { duration: 2, repeat: Number.POSITIVE_INFINITY, ease: 'linear' },
                  scale: { duration: 1, repeat: Number.POSITIVE_INFINITY, ease: 'easeInOut' }
                }}
                className="w-16 h-16 mx-auto mb-6 bg-[#F59E0B] rounded-full flex items-center justify-center shadow-lg"
              >
                <FaCog className="text-white text-xl" />
              </motion.div>

              <motion.h3
                animate={{ opacity: [0.7, 1, 0.7] }}
                transition={{ duration: 1.5, repeat: Number.POSITIVE_INFINITY }}
                className="text-2xl font-bold text-gray-800 mb-4"
              >
                Processing Evaluation
              </motion.h3>

              <motion.p
                animate={{ opacity: [0.5, 0.8, 0.5] }}
                transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
                className="text-gray-600 text-lg mb-6"
              >
                Analyzing your student assessment...
              </motion.p>

              <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                <motion.div
                  animate={{ x: ['-100%', '100%'] }}
                  transition={{
                    duration: 1.5,
                    repeat: Number.POSITIVE_INFINITY,
                    ease: 'easeInOut'
                  }}
                  className="h-full w-1/3 bg-[#F59E0B] rounded-full"
                />
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default CreateTestForStudents;