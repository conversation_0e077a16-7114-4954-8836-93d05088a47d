import { useState } from 'react';
// eslint-disable-next-line no-unused-vars
import { motion, AnimatePresence } from 'framer-motion';
import {
  CheckCircle2,
  Atom,
  FlaskConical,
  Calculator,
  XCircle,
  Clock,
  Eye,
  TrendingUp,
  BarChart3,
  Info,
  ChevronDown,
  Target,
  Award
} from 'lucide-react';

const TestBreakdownDashboard = () => {
  const [selectedSubject, setSelectedSubject] = useState(null);
  const [hoveredCell, setHoveredCell] = useState(null);
  const [showTooltip, setShowTooltip] = useState(null);
//   const [expandedView, setExpandedView] = useState(false);

  // Animation variants
  const containerVariants = {
    initial: { opacity: 0 },
    animate: {
      opacity: 1,
      transition: {
        duration: 0.8,
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const rowVariants = {
    initial: { opacity: 0, x: -50 },
    animate: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.6,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    },
    hover: {
      scale: 1.02,
      transition: {
        duration: 0.2,
        ease: 'easeOut'
      }
    }
  };

  const cellVariants = {
    initial: { opacity: 0, scale: 0.8 },
    animate: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.5,
        ease: 'easeOut'
      }
    },
    hover: {
      scale: 1.05,
      transition: {
        duration: 0.2
      }
    }
  };

  const progressVariants = {
    initial: { width: 0 },
    animate: {
      width: '100%',
      transition: {
        duration: 1.5,
        delay: 0.8,
        ease: 'easeInOut'
      }
    }
  };

  // Test breakdown data
  const testData = [
    {
      id: 1,
      subject: 'Overall',
      icon: CheckCircle2,
      color: 'blue',
      iconColor: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      correct: { value: 57, total: 75, percentage: 76 },
      incorrect: { value: 2, total: 75, percentage: 2.7 },
      unattempted: { value: 16, total: 75, percentage: 21.3 },
      notVisited: { value: 0, total: 75, percentage: 0 }
    },
    {
      id: 2,
      subject: 'Physics',
      icon: Atom,
      color: 'green',
      iconColor: 'text-green-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      correct: { value: 23, total: 25, percentage: 92 },
      incorrect: { value: 1, total: 25, percentage: 4 },
      unattempted: { value: 1, total: 25, percentage: 4 },
      notVisited: { value: 0, total: 25, percentage: 0 }
    },
    {
      id: 3,
      subject: 'Chemistry',
      icon: FlaskConical,
      color: 'orange',
      iconColor: 'text-orange-600',
      bgColor: 'bg-orange-50',
      borderColor: 'border-orange-200',
      correct: { value: 20, total: 25, percentage: 80 },
      incorrect: { value: 0, total: 25, percentage: 0 },
      unattempted: { value: 5, total: 25, percentage: 20 },
      notVisited: { value: 0, total: 25, percentage: 0 }
    },
    {
      id: 4,
      subject: 'Mathematics',
      icon: Calculator,
      color: 'blue',
      iconColor: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      correct: { value: 14, total: 25, percentage: 56 },
      incorrect: { value: 1, total: 25, percentage: 4 },
      unattempted: { value: 10, total: 25, percentage: 40 },
      notVisited: { value: 0, total: 25, percentage: 0 }
    }
  ];

  const categoryConfig = {
    correct: {
      title: 'CORRECT ATTEMPTS',
      icon: CheckCircle2,
      color: 'green',
      bgColor: 'bg-green-100',
      textColor: 'text-green-700',
      progressColor: 'bg-green-500',
      lightBg: 'bg-green-50',
      description: 'Questions answered correctly'
    },
    incorrect: {
      title: 'INCORRECT ATTEMPTS',
      icon: XCircle,
      color: 'red',
      bgColor: 'bg-red-100',
      textColor: 'text-red-700',
      progressColor: 'bg-red-500',
      lightBg: 'bg-red-50',
      description: 'Questions answered incorrectly'
    },
    unattempted: {
      title: 'UNATTEMPTED QS',
      icon: Clock,
      color: 'blue',
      bgColor: 'bg-blue-100',
      textColor: 'text-blue-700',
      progressColor: 'bg-blue-500',
      lightBg: 'bg-blue-50',
      description: 'Questions left unanswered'
    },
    notVisited: {
      title: 'NOT VISITED QS',
      icon: Eye,
      color: 'gray',
      bgColor: 'bg-gray-100',
      textColor: 'text-gray-700',
      progressColor: 'bg-gray-400',
      lightBg: 'bg-gray-50',
      description: 'Questions not viewed'
    }
  };

  const handleSubjectClick = (subjectId) => {
    setSelectedSubject(selectedSubject === subjectId ? null : subjectId);
  };

  const handleCellHover = (subjectId, category) => {
    setHoveredCell(`${subjectId}-${category}`);
  };

  const getAccuracyRate = (correct, incorrect) => {
    const attempted = correct.value + incorrect.value;
    return attempted > 0 ? ((correct.value / attempted) * 100).toFixed(1) : 0;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-4 md:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto">
        {/* Enhanced Header */}
        <motion.div
          className="text-center mb-10"
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: 'easeOut' }}>
          <div className="flex items-center justify-center gap-4 mb-6">
            <div className="p-4 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl shadow-lg">
              <BarChart3 className="w-10 h-10 text-white" />
            </div>
            <div className="text-left">
              <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-800 via-gray-700 to-gray-600 bg-clip-text text-transparent">
                Test Breakdown
              </h1>
              <p className="text-gray-600 text-lg mt-2 max-w-3xl">
                Subject-wise breakdown of your correct, incorrect, unattempted and not visited
                questions in a test.
              </p>
            </div>
          </div>

          {/* Summary Stats */}
          <div className="flex flex-wrap justify-center gap-4 mt-8">
            <div className="flex items-center gap-2 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full shadow-md">
              <Target className="w-5 h-5 text-green-500" />
              <span className="text-sm font-medium text-gray-700">
                Accuracy: {getAccuracyRate(testData[0].correct, testData[0].incorrect)}%
              </span>
            </div>
            <div className="flex items-center gap-2 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full shadow-md">
              <Award className="w-5 h-5 text-blue-500" />
              <span className="text-sm font-medium text-gray-700">
                Attempted: {testData[0].correct.value + testData[0].incorrect.value}/
                {testData[0].correct.total}
              </span>
            </div>
            <div className="flex items-center gap-2 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full shadow-md">
              <TrendingUp className="w-5 h-5 text-purple-500" />
              <span className="text-sm font-medium text-gray-700">
                Completion:{' '}
                {(
                  ((testData[0].correct.value + testData[0].incorrect.value) /
                    testData[0].correct.total) *
                  100
                ).toFixed(1)}
                %
              </span>
            </div>
          </div>
        </motion.div>

        {/* Enhanced Table */}
        <motion.div
          className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-2xl overflow-hidden border border-white/20"
          variants={containerVariants}
          initial="initial"
          animate="animate">
          {/* Table Header */}
          <div className="bg-gradient-to-r from-gray-50 to-gray-100 p-6 border-b border-gray-200">
            <div className="grid grid-cols-5 gap-4">
              <div className="font-semibold text-gray-700">SUBJECT</div>
              {Object.entries(categoryConfig).map(([key, config]) => {
                const IconComponent = config.icon;
                return (
                  <div key={key} className="text-center">
                    <div className="flex items-center justify-center gap-2 mb-2">
                      <IconComponent className={`w-4 h-4 ${config.textColor}`} />
                      <span className="font-semibold text-gray-700 text-sm">{config.title}</span>
                      <div
                        className="relative"
                        onMouseEnter={() => setShowTooltip(key)}
                        onMouseLeave={() => setShowTooltip(null)}>
                        <Info className="w-3 h-3 text-gray-400 cursor-help" />
                        {showTooltip === key && (
                          <div className="absolute top-6 left-1/2 transform -translate-x-1/2 z-50 w-48 p-2 bg-gray-900 text-white text-xs rounded-lg shadow-xl">
                            <p>{config.description}</p>
                            <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-gray-900 rotate-45"></div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Table Body */}
          <div className="p-6">
            {testData.map((subject) => {
              const IconComponent = subject.icon;
              const isSelected = selectedSubject === subject.id;

              return (
                <motion.div
                  key={subject.id}
                  variants={rowVariants}
                  whileHover="hover"
                  className={`grid grid-cols-5 gap-4 p-4 rounded-2xl mb-4 cursor-pointer transition-all duration-300 ${
                    isSelected
                      ? `${subject.bgColor} ${subject.borderColor} border-2 shadow-lg`
                      : 'hover:bg-gray-50 hover:shadow-md'
                  }`}
                  onClick={() => handleSubjectClick(subject.id)}>
                  {/* Subject Column */}
                  <div className="flex items-center gap-3">
                    <div className={`p-3 ${subject.bgColor} rounded-xl`}>
                      <IconComponent className={`w-6 h-6 ${subject.iconColor}`} />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-800">{subject.subject}</h3>
                      {isSelected && (
                        <p className="text-xs text-gray-600 mt-1">
                          Accuracy: {getAccuracyRate(subject.correct, subject.incorrect)}%
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Data Columns */}
                  {Object.entries(categoryConfig).map(([key, config]) => {
                    const data = subject[key];
                    const cellId = `${subject.id}-${key}`;
                    const isHovered = hoveredCell === cellId;

                    return (
                      <motion.div
                        key={key}
                        variants={cellVariants}
                        whileHover="hover"
                        className={`text-center p-4 rounded-xl transition-all duration-300 ${
                          isHovered ? `${config.lightBg} shadow-md` : ''
                        }`}
                        onMouseEnter={() => handleCellHover(subject.id, key)}
                        onMouseLeave={() => setHoveredCell(null)}>
                        <div className="mb-3">
                          <span className={`text-2xl font-bold ${config.textColor}`}>
                            {data.value}
                          </span>
                          <span className="text-gray-500 text-sm ml-1">/ {data.total}</span>
                        </div>

                        {/* Enhanced Progress Bar */}
                        <div className="w-full bg-gray-200 rounded-full h-2 mb-2 overflow-hidden">
                          <motion.div
                            className={`h-full ${config.progressColor} rounded-full relative`}
                            variants={progressVariants}
                            initial="initial"
                            animate="animate"
                            style={{ width: `${data.percentage}%` }}>
                            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform -skew-x-12 animate-pulse"></div>
                          </motion.div>
                        </div>

                        <div className="text-xs text-gray-600">{data.percentage.toFixed(1)}%</div>

                        {/* Expandable Details */}
                        <AnimatePresence>
                          {isSelected && (
                            <motion.div
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: 'auto' }}
                              exit={{ opacity: 0, height: 0 }}
                              transition={{ duration: 0.3 }}
                              className="mt-3 pt-3 border-t border-gray-200">
                              <div className="text-xs space-y-1">
                                <div className="flex justify-between">
                                  <span className="text-gray-600">Questions:</span>
                                  <span className="font-medium">{data.value}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-gray-600">Percentage:</span>
                                  <span className="font-medium">{data.percentage.toFixed(1)}%</span>
                                </div>
                              </div>
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </motion.div>
                    );
                  })}
                </motion.div>
              );
            })}
          </div>
        </motion.div>

        {/* Action Buttons */}
      </div>
    </div>
  );
};

export default TestBreakdownDashboard;
