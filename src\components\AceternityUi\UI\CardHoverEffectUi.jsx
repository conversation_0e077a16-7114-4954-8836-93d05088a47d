import { AnimatePresence, motion } from 'framer-motion'; // Corrected import from 'motion/react' to 'framer-motion'
import { useState } from 'react';
import { cn } from '../../../lib/utils'; // Assuming this utility exists

export const CardHoverEffectUi = ({ items, className }) => {
  let [hoveredIndex, setHoveredIndex] = useState(null);

  return (
    <div className={cn('grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 py-10', className)}>
      {items.map((item, idx) => (
        <a
          href={item?.link || '#'} // Added fallback href
          key={item?.title || idx} // Use title as key, fallback to index
          className="relative group block p-2 h-full w-full"
          onMouseEnter={() => setHoveredIndex(idx)}
          onMouseLeave={() => setHoveredIndex(null)}>
          <AnimatePresence>
            {hoveredIndex === idx && (
              <motion.span
                // OPTIMIZED: The hover background is now our deep blue primary brand color.
                className="absolute inset-0 h-full w-full bg-[#0D2A4B] block rounded-3xl"
                layoutId="hoverBackground"
                initial={{ opacity: 0 }}
                animate={{
                  opacity: 1,
                  transition: { duration: 0.15 },
                }}
                exit={{
                  opacity: 0,
                  transition: { duration: 0.15, delay: 0.2 },
                }}
              />
            )}
          </AnimatePresence>
          {/* The Card component itself now handles the visual changes */}
          <Card>
            <CardTitle>{item.title}</CardTitle>
            <CardDescription>{item.description}</CardDescription>
          </Card>
        </a>
      ))}
    </div>
  );
};

export const Card = ({ className, children }) => {
  return (
    <div
      className={cn(
        // --- OPTIMIZED STYLES ---
        // 1. Default State: A clean, light gray background (`bg-brand-secondary`).
        // 2. Hover State: Becomes transparent to reveal the blue motion.span behind it (`group-hover:bg-transparent`).
        // 3. Border: Subtle border appears on hover for a polished look.
        // 4. Transition: Smoothly animates the background and border color changes.
        'rounded-2xl h-full w-full p-4 overflow-hidden bg-[#F5F7FA] border border-transparent group-hover:bg-transparent group-hover:border-blue-900 relative z-20 transition-colors duration-300',
        className
      )}>
      <div className="relative z-50">
        <div className="p-4">{children}</div>
      </div>
    </div>
  );
};

export const CardTitle = ({ className, children }) => {
  return (
    <h4
      className={cn(
        // --- OPTIMIZED STYLES ---
        // 1. Default State: Dark, primary text color for high readability (`text-text-primary`).
        // 2. Hover State: Inverts to white text to be readable on the new blue background (`group-hover:text-text-on_primary`).
        // 3. Transition: Smoothly animates the text color change.
        'text-gray-800 font-bold tracking-wide mt-4 group-hover:text-white transition-colors duration-300',
        className
      )}>
      {children}
    </h4>
  );
};

export const CardDescription = ({ className, children }) => {
  return (
    <p
      className={cn(
        // --- OPTIMIZED STYLES ---
        // 1. Default State: Softer, secondary text color for visual hierarchy.
        // 2. Hover State: Inverts to a slightly transparent white for hierarchy against the title.
        // 3. Transition: Smoothly animates the text color change.
        'mt-8 text-gray-500 tracking-wide leading-relaxed group-hover:text-white/80 transition-colors duration-300',
        className
      )}>
      {children}
    </p>
  );
};