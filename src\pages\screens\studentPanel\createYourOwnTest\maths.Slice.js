import { createOwnTestMathApi } from "../../../../redux/api/api";

export const createOwnTestMathApiSlice = createOwnTestMathApi.injectEndpoints({
  endpoints: (builder) => ({
    createYourOwnTestMathStartTest: builder.mutation({
      query: (body) => ({
        url: '/start-test-maths',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Create Your Own Test Math Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['CreateOwnTestMath']
    }),
    createYourOwnTestMathSubmitTest: builder.mutation({
      query: (body) => ({
        url: '/evaluate-test-maths',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Create Your Own Test Math Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['CreateOwnTestMath']
    })
  })
});
export const {
  useCreateYourOwnTestMathStartTestMutation,
  useCreateYourOwnTestMathSubmitTestMutation
} = createOwnTestMathApiSlice;