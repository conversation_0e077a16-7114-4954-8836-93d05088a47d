importScripts("https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js");
importScripts("https://www.gstatic.com/firebasejs/9.22.0/firebase-messaging-compat.js");

const firebaseConfig = {
  apiKey: "AIzaSyDT7404eSoYJCMNtgY2xd_ZqEhzjAA2eUo",
  authDomain: "sasthra-3a69e.firebaseapp.com",
  projectId: "sasthra-3a69e",
  storageBucket: "sasthra-3a69e.firebasestorage.app",
  messagingSenderId: "119837146788",
  appId: "1:119837146788:web:6f3c60365aadd3ad677031",
  measurementId: "G-FYJXQ4S2VT"
};

firebase.initializeApp(firebaseConfig);
const messaging = firebase.messaging();

messaging.onBackgroundMessage((payload) => {
  console.log("Background Message:", payload);
  const notificationTitle = payload.notification.title;
  const notificationOptions = {
    body: payload.notification.body,
    icon: "/sasthra_logo.png", // Updated to reference the logo in the public folder
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});