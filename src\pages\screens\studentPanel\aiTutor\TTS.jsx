import React, { useState, useEffect, useRef } from 'react';

function TTS({ contentToSpeak, pageNumber, numPages, goToNextPage, setError, isTransitioning, setPhonemeData }) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [currentAudio, setCurrentAudio] = useState(null);
  const [hasAudioForCurrentPage, setHasAudioForCurrentPage] = useState(false);
  const audioRef = useRef(null);

  // Handle automatic slide navigation during playback - but only when audio ends naturally
  useEffect(() => {
    if (isPlaying && !isTransitioning && !hasAudioForCurrentPage) {
      if (contentToSpeak) {
        console.log(`Playing slide ${pageNumber} with content:`, contentToSpeak);
        speakSlide(contentToSpeak);
      } else if (pageNumber <= numPages) {
        console.log(`No slide content for slide ${pageNumber}, moving to next slide.`);
        goToNextPage();
      } else {
        console.log('Reached end of presentation or no content, stopping playback.');
        if (audioRef.current) {
          audioRef.current.pause();
          audioRef.current.currentTime = 0;
          console.log('Paused audio due to end of presentation.');
        }
        setIsPlaying(false);
      }
    }
  }, [isPlaying, pageNumber, contentToSpeak, numPages, isTransitioning, goToNextPage, hasAudioForCurrentPage]);

  // Reset audio state when page changes
  useEffect(() => {
    setHasAudioForCurrentPage(false);
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      URL.revokeObjectURL(audioRef.current.src);
      audioRef.current = null;
      setCurrentAudio(null);
      console.log('Cleaned up audio due to page change.');
    }
  }, [pageNumber]);

  // Cleanup audio on component unmount
  useEffect(() => {
    return () => {
      console.log('Cleaning up audio on unmount.');
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
        URL.revokeObjectURL(audioRef.current.src);
      }
    };
  }, []);

  const speakSlide = async (content) => {
    if (!content) {
      console.log('No content to speak, checking next page.');
      if (pageNumber < numPages) {
        goToNextPage();
      } else {
        setIsPlaying(false);
        console.log('No more pages, stopping playback.');
      }
      return;
    }

    setIsLoading(true);
    console.log(`Speaking page ${pageNumber} with content:`, content);

    try {
      const response = await fetch('https://sasthra.in/tts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ text: content })
      });

      if (!response.ok) {
        throw new Error(`TTS request failed: ${response.status}`);
      }

      const ttsData = await response.json();
      console.log('TTS response received:', ttsData);
      console.log('Received phonemes for current audio file:', ttsData.phonemes);

      setPhonemeData(ttsData.phonemes);

      // Convert base64 audio to blob
      const audioBytes = atob(ttsData.audio.replace(/^data:audio\/mpeg;base64,/, ''));
      const arrayBuffer = new ArrayBuffer(audioBytes.length);
      const uint8Array = new Uint8Array(arrayBuffer);
      for (let i = 0; i < audioBytes.length; i++) {
        uint8Array[i] = audioBytes.charCodeAt(i);
      }
      const audioBlob = new Blob([arrayBuffer], { type: ttsData.mimetype });
      const audioUrl = URL.createObjectURL(audioBlob);
      const audio = new Audio(audioUrl);

      // Clean up previous audio if any
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
        URL.revokeObjectURL(audioRef.current.src);
        console.log('Cleaned up previous audio.');
      }

      audioRef.current = audio;
      setCurrentAudio(audio);
      setHasAudioForCurrentPage(true);

      audio.onended = () => {
        console.log(`Audio ended for page ${pageNumber}`);
        URL.revokeObjectURL(audioUrl);

        if (pageNumber < numPages && isPlaying) {
          goToNextPage();
        } else {
          setIsPlaying(false);
          console.log('No more pages or playback stopped.');
        }
      };

      audio.onerror = () => {
        console.error('Audio playback error');
        URL.revokeObjectURL(audioUrl);
        setIsPlaying(false);
        setIsLoading(false);
        setCurrentAudio(null);
        setHasAudioForCurrentPage(false);
      };

      await audio.play();
      setIsPlaying(true);
      console.log(`Playing audio for page ${pageNumber}`);
    } catch (error) {
      console.error('TTS Error:', error);
      setError(`TTS Error: ${error.message}`);
      setIsPlaying(false);
      setHasAudioForCurrentPage(false);
    } finally {
      setIsLoading(false);
      console.log('TTS request complete, loading state set to false.');
    }
  };

  const handlePlayPause = () => {
    if (isPlaying) {
      if (audioRef.current) {
        audioRef.current.pause();
        console.log('Paused audio playback.');
      }
      setIsPlaying(false);
    } else {
      if (audioRef.current && hasAudioForCurrentPage) {
        // Restart from beginning
        audioRef.current.currentTime = 0;
        audioRef.current.play();
        setIsPlaying(true);
        console.log('Resumed audio playback from beginning.');
      } else {
        if (contentToSpeak) {
          console.log(`Starting playback for page ${pageNumber}`);
          setIsPlaying(true);
        } else if (pageNumber < numPages) {
          console.log('No slide content, moving to next page.');
          goToNextPage();
        }
      }
    }
  };

  return (
    <button
      onClick={handlePlayPause}
      disabled={isLoading || isTransitioning || !contentToSpeak}
      className="flex items-center space-x-2 px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed rounded-lg transition-colors text-white">
      {isLoading ? (
        <>
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
          <span>Loading...</span>
        </>
      ) : isTransitioning ? (
        <>
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
          <span>Transitioning...</span>
        </>
      ) : isPlaying ? (
        <>
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path
              fillRule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z"
              clipRule="evenodd"
            />
          </svg>
          <span>Pause</span>
        </>
      ) : (
        <>
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path
              fillRule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
              clipRule="evenodd"
            />
          </svg>
          <span>Play</span>
        </>
      )}
    </button>
  );
}

export default TTS;