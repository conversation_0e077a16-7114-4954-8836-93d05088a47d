import React, { useEffect } from 'react';
import { useLazyGetKotaTeachersQuery } from '../mappedCenters/mappedCenter.slice';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUser,
  faEnvelope,
  faPhone,
  faBook,
  faChalkboardUser,
  faLightbulb,

  faHandshake,
  faChartLine
} from '@fortawesome/free-solid-svg-icons';

// Custom animated underline component
const AnimatedUnderline = () => (
  <motion.div
    initial={{ scaleX: 0 }}
    animate={{ scaleX: 1 }}
    transition={{ duration: 0.8, delay: 0.3 }}
    className="h-1 w-full bg-[var(--color-teacher)] origin-left"
  />
);

// Floating dots background component
const FloatingDots = ({ count = 20 }) => {
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {[...Array(count)].map((_, i) => (
        <motion.div
          key={i}
          initial={{
            x: Math.random() * 100,
            y: Math.random() * 100,
            opacity: 0
          }}
          animate={{
            y: [0, Math.random() * 30 - 15],
            opacity: [0, 0.1, 0],
            transition: {
              duration: 5 + Math.random() * 10,
              repeat: Infinity,
              delay: Math.random() * 2
            }
          }}
          className="absolute w-2 h-2 rounded-full bg-[var(--color-teacher)]"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`
          }}
        />
      ))}
    </div>
  );
};

const TeacherOverview = () => {
  const [getKotaTeachers, { data, isLoading, isError, error }] = useLazyGetKotaTeachersQuery();

  useEffect(() => {
    getKotaTeachers();
  }, [getKotaTeachers]);

  const getSessionUser = () => {
  return {
    name: sessionStorage.getItem('name') || 'Unknown',
    role: sessionStorage.getItem('role') || 'Unknown',
    centername: sessionStorage.getItem('centername') || 'Unknown',
    phone: sessionStorage.getItem('phone') || 'Unknown',
    centercode: sessionStorage.getItem('centercode') || 'Unknown',
    designation: sessionStorage.getItem('designation') || 'Unknown'
  };
};
 const user = data?.user || getSessionUser();
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.4
      }
    }
  };

  const cardVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 80,
        damping: 10
      }
    }
  };

  const avatarVariants = {
    hidden: { scale: 0, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 150,
        delay: 0.6
      }
    }
  };

  if (isLoading) {
    return (
      <motion.div
        className="flex justify-center items-center h-screen"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}>
        <motion.div
          animate={{
            rotate: 360,
            scale: [1, 1.1, 1],
            transition: {
              duration: 1.5,
              repeat: Infinity,
              ease: 'easeInOut'
            }
          }}
          className="h-20 w-20 rounded-full border-4 border-t-[var(--color-teacher)] border-b-[var(--color-teacher)] border-l-transparent border-r-transparent"
        />
      </motion.div>
    );
  }

  if (isError) {
    return (
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        className="p-6 bg-red-50 rounded-xl border-l-4 border-red-500">
        <div className="flex items-center">
          <motion.div
            animate={{
              x: [-5, 5, -5],
              transition: { repeat: Infinity, duration: 2 }
            }}
            className="p-2 bg-red-100 rounded-full mr-4">
            <FontAwesomeIcon icon={faUser} className="text-red-500" />
          </motion.div>
          <div>
            <h3 className="font-bold text-red-700">Error</h3>
            <p className="text-red-600">{error?.data?.message || 'Failed to fetch teacher data'}</p>
          </div>
        </div>
      </motion.div>
    );
  }

  if (!data?.teacher) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="p-6 bg-yellow-50 rounded-xl border-l-4 border-yellow-400">
        <div className="flex items-center">
          <motion.div
            animate={{
              scale: [1, 1.05, 1],
              transition: { repeat: Infinity, duration: 3 }
            }}
            className="p-2 bg-yellow-100 rounded-full mr-4">
            <FontAwesomeIcon icon={faUser} className="text-yellow-600" />
          </motion.div>
          <div>
            <h3 className="font-bold text-yellow-800">No Data Available</h3>
            <p className="text-yellow-700">
              Teacher information could not be found. Please check the API response.
            </p>
          </div>
        </div>
      </motion.div>
    );
  }

  const { teacher } = data;

  return (
    <div className="max-w-7xl mx-auto p-4 md:p-6 relative">
      <FloatingDots />

      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-12 text-center">
        <motion.h2
          className="text-4xl font-bold text-[var(--color-teacher)] mb-3"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}>
       Teacher Dashboard
        </motion.h2>
        <AnimatedUnderline />
      </motion.div>

      <AnimatePresence>
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 lg:grid-cols-3 gap-8 relative z-10">
          {/* Profile Card - Creative Geometric Design */}
          <motion.div
            variants={cardVariants}
            whileHover={{
              y: -5,
              transition: { duration: 0.3 }
            }}
            className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100 relative">
            {/* Geometric decoration */}
            <motion.div
              className="absolute top-0 right-0 w-32 h-32 bg-[var(--color-teacher-light)] opacity-20 clip-triangle"
              initial={{ opacity: 0, rotate: -90 }}
              animate={{ opacity: 0.2, rotate: 0 }}
              transition={{ delay: 0.4 }}
            />

            <div className="p-6">
              {/* Avatar with creative animation */}
              <motion.div variants={avatarVariants} className="relative mx-auto mb-6">
                <div className="h-40 w-40 rounded-full border-4 border-[var(--color-teacher)] bg-white shadow-md overflow-hidden flex items-center justify-center">
                  <div className="h-full w-full bg-[var(--color-teacher-light)] flex items-center justify-center text-[var(--color-teacher)] text-5xl font-bold">
                    {teacher.first_name.charAt(0)}
                    {teacher.last_name.charAt(0)}
                  </div>
                </div>
               
              </motion.div>

              {/* Profile Info */}
              <div className="text-center">
                <motion.h3
                  className="text-2xl font-bold text-gray-800 mb-1"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.8 }}>
                  {teacher.first_name} {teacher.last_name}
                </motion.h3>
                <motion.p
                  className="text-[var(--color-teacher)] font-medium mb-6 text-lg"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.9 }}>
                  {teacher.course} Educator
                </motion.p>

                <div className="space-y-4 mt-8">
                  
                  <motion.div
                    className="flex items-center justify-center"
                    initial={{ x: -20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 1.1 }}>
                    <motion.div
                      whileHover={{ scale: 1.1 }}
                      className="p-3 bg-[var(--color-teacher-light)] rounded-full mr-4">
                      <FontAwesomeIcon icon={faPhone} className="text-[var(--color-teacher)]" />
                    </motion.div>
                    <div className="text-left">
                      <p className="text-sm text-gray-500">Phone</p>
                      <p className="font-medium">{teacher.phone}</p>
                  
                     
                    </div>
                  </motion.div>
                  <motion.div
                    className="flex items-center justify-center"
                    initial={{ x: -20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 1.0 }}>
                    <motion.div
                      whileHover={{ scale: 1.1 }}
                      className="p-3 bg-[var(--color-teacher-light)] rounded-full mr-4">
                      <FontAwesomeIcon icon={faEnvelope} className="text-[var(--color-teacher)]" />
                    </motion.div>
                    <div className="text-left">
                      <p className="text-sm text-gray-500">Email</p>
                      <p className="font-medium">{teacher.email}</p>
                    </div>
                  </motion.div>

                </div>
              </div>
            </div>
          </motion.div>

          {/* Stats Card - Creative Layout */}
          <motion.div
            variants={cardVariants}
            whileHover={{
              y: -5,
              transition: { duration: 0.3 }
            }}
            className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100 relative">
            {/* Diagonal decoration */}
            <motion.div
              className="absolute bottom-0 left-0 w-full h-16 bg-[var(--color-teacher-light)] opacity-10 clip-diagonal"
              initial={{ opacity: 0, x: -100 }}
              animate={{ opacity: 0.1, x: 0 }}
              transition={{ delay: 0.5 }}
            />

            <div className="p-6">
              <div className="flex items-center mb-8">
                <motion.div
                  whileHover={{ rotate: 15 }}
                  className="p-3 bg-[var(--color-teacher-light)] rounded-lg mr-4">
                  <FontAwesomeIcon
                    icon={faChalkboardUser}
                    className="text-[var(--color-teacher)] text-xl"
                  />
                </motion.div>
                <motion.h3
                  className="text-2xl font-bold text-gray-800"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6 }}>
                  Teaching Stats
                </motion.h3>
              </div>

              <div className="space-y-6">
                  <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.7 }}
                  className="p-4 bg-gray-50 rounded-xl border-l-4 border-[var(--color-teacher)]">
                  <div className="flex items-center">
                    <motion.div
                      whileHover={{ rotate: 15 }}
                      className="p-2 bg-white rounded-lg mr-4 shadow-sm">
                      <FontAwesomeIcon icon={faBook} className="text-[var(--color-teacher)]" />
                    </motion.div>
                    <div>
                      <p className="text-sm text-gray-500">Designation</p>
                        <p className="font-medium">{user.designation}</p>
                    </div>
                  </div>
                </motion.div>
                

                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.8 }}
                  className="p-4 bg-gray-50 rounded-xl border-l-4 border-[var(--color-teacher)]">
                  <div className="flex items-center">
                    <motion.div
                      whileHover={{ rotate: 15 }}
                      className="p-2 bg-white rounded-lg mr-4 shadow-sm">
                      <FontAwesomeIcon icon={faChartLine} className="text-[var(--color-teacher)]" />
                    </motion.div>
                    <div>
                      <p className="text-sm text-gray-500">Experience</p>
                      <p className="font-medium">5+ Years</p>
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.9 }}
                  className="p-4 bg-gray-50 rounded-xl border-l-4 border-[var(--color-teacher)]">
                  <div className="flex items-center">
                    <motion.div
                      whileHover={{ rotate: 15 }}
                      className="p-2 bg-white rounded-lg mr-4 shadow-sm">
                      <FontAwesomeIcon icon={faHandshake} className="text-[var(--color-teacher)]" />
                    </motion.div>
                    <div>
                      <p className="text-sm text-gray-500">Sessions</p>
                      <p className="font-medium">120+ Completed</p>
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>
          </motion.div>

          {/* Schedule Card - Creative Layout */}
          <motion.div
            variants={cardVariants}
            whileHover={{
              y: -5,
              transition: { duration: 0.3 }
            }}
            className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100 relative">
            {/* Wave decoration */}
            <motion.div
              className="absolute top-0 left-0 w-full h-2 bg-[var(--color-teacher)] opacity-20"
              initial={{ scaleX: 0 }}
              animate={{ scaleX: 1 }}
              transition={{ duration: 1, delay: 0.4 }}
            />

         <div className="p-6 relative overflow-hidden">
  {/* Animated background elements */}
  <motion.div 
    className="absolute top-0 right-0 w-32 h-32 bg-[var(--color-teacher-light)] opacity-10 rounded-full"
    animate={{
      scale: [1, 1.2, 1],
      opacity: [0.1, 0.2, 0.1],
      transition: {
        duration: 8,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }}
  />
  
  <div className="flex items-center mb-8">
    <motion.div
      whileHover={{ rotate: 15, scale: 1.1 }}
      className="p-3 bg-[var(--color-teacher-light)] rounded-lg mr-4"
    >
      <FontAwesomeIcon
        icon={faLightbulb}
        className="text-[var(--color-teacher)] text-xl"
      />
    </motion.div>
    <motion.h3
      className="text-2xl font-bold text-gray-800"
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: 0.6 }}
    >
      Educator Inspiration
    </motion.h3>
  </div>

  <div className="space-y-6">
    {/* Daily Teaching Quote */}
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.7 }}
      className="p-6 bg-gradient-to-br from-[var(--color-teacher-light)] to-white rounded-xl border-l-4 border-[var(--color-teacher)]"
    >
      <div className="flex items-start">
        <div className="text-[var(--color-teacher)] text-3xl mr-3">"</div>
        <div>
          <p className="italic text-gray-700 text-lg">
            Education is not the filling of a pail, but the lighting of a fire.
          </p>
          <p className="text-right text-[var(--color-teacher)] mt-2 font-medium">
            — William Butler Yeats
          </p>
        </div>
      </div>
    </motion.div>

    {/* Teaching Tip */}
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.8 }}
      className="p-5 bg-white rounded-xl shadow-sm border border-gray-100"
    >
      <div className="flex items-center">
        <div className="p-2 bg-[var(--color-teacher-light)] rounded-lg mr-4">
          <FontAwesomeIcon 
            icon={faChalkboardUser} 
            className="text-[var(--color-teacher)]" 
          />
        </div>
        <div>
          <p className="text-sm text-gray-500">Today's Teaching Tip</p>
          <p className="font-medium">Use think-pair-share to engage all students</p>
        </div>
      </div>
    </motion.div>

  
  </div>

  {/* Floating decorative elements */}
  <motion.div
    className="absolute bottom-4 right-4 w-8 h-8 bg-[var(--color-teacher)] opacity-20 rounded-full"
    animate={{
      y: [0, -10, 0],
      transition: {
        duration: 4,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }}
  />
</div>
          </motion.div>
        </motion.div>
      </AnimatePresence>

      {/* Add this to your CSS */}
      <style jsx>{`
        .clip-triangle {
          clip-path: polygon(0 0, 100% 100%, 100% 0);
        }
        .clip-diagonal {
          clip-path: polygon(0 100%, 100% 0, 100% 100%);
        }
      `}</style>
    </div>
  );
};

export default TeacherOverview;
