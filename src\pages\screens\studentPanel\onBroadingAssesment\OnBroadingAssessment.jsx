import React, { useEffect, useState, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import {
  setOnBroadingAssessmentData,
  useCompleteOnBroadingAssessmentMutation,
  useGetOnBroadingAssessmentPostServiceMutation,
  usePostOnBroadingAssessmentServiceMutation
} from './onBroadingAssessment.slice';
import MathRenderer from '../../../../components/Layout/MathRenderer';
import Button from '../../../../components/Field/Button';
import { CircularProgressbar, buildStyles } from 'react-circular-progressbar';
import 'react-circular-progressbar/dist/styles.css';
// import Toastify from '../../../../components/PopUp/Toastify';

const Spinner = () => (
  <svg
    className="animate-spin h-8 w-8 text-indigo-500"
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24">
    <circle
      className="opacity-25"
      cx="12"
      cy="12"
      r="10"
      stroke="currentColor"
      strokeWidth="4"></circle>
    <path
      className="opacity-75"
      fill="currentColor"
      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
  </svg>
);

const OnBroadingAssessment = ({ isSkip }) => {
  const [OnBroadingAssessmentApi, { isLoading, error }] =
    useGetOnBroadingAssessmentPostServiceMutation();
  const [PostOnBroadingAssessmentApi] = usePostOnBroadingAssessmentServiceMutation();
  const [completeOnBroadingAssessmentApi] = useCompleteOnBroadingAssessmentMutation();
  const questionData = useSelector((state) => state.onBroadingAssessment.onBroadingAssessmentData);
  const dispatch = useDispatch();

  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState({});
  const [timeLeft, setTimeLeft] = useState(120);
  const timerRef = useRef(null);

  useEffect(() => {
    const handleOnBroadingAssessment = async () => {
      try {
        const userId = sessionStorage.getItem('userId');
        const res = await OnBroadingAssessmentApi({ user_id: userId }).unwrap();

        const flatQuestions = res.sections.flatMap((section) =>
          section.questions.map((question) => ({
            ...question,
            section_type: section.type
          }))
        );

        dispatch(
          setOnBroadingAssessmentData({
            assessment_id: res.assessment_id,
            questions: flatQuestions
          })
        );
      } catch (error) {
        console.error('Error fetching OnBroading Assessment:', error);
      }
    };

    handleOnBroadingAssessment();
  }, [OnBroadingAssessmentApi, dispatch]);

  useEffect(() => {
    if (isLoading) return;

    setTimeLeft(120);
    clearInterval(timerRef.current);

    timerRef.current = setInterval(() => {
      setTimeLeft((prevTime) => {
        if (prevTime <= 0) {
          clearInterval(timerRef.current);
          if (questionData?.questions?.length > currentQuestionIndex + 1) {
            handleNext();
          } else {
            handleSubmit();
          }
          return 0;
        }
        return prevTime - 1;
      });
    }, 1000);

    return () => clearInterval(timerRef.current);
  }, [currentQuestionIndex, isLoading, questionData?.questions?.length]);

  const formatTime = (timeInSeconds) => {
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = timeInSeconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleOptionSelect = (questionNumber, optionKey) => {
    setAnswers((prev) => ({
      ...prev,
      [questionNumber]: optionKey
    }));
  };

  const handleNext = () => {
    if (currentQuestionIndex < questionData.questions.length - 1) {
      setCurrentQuestionIndex((prev) => prev + 1);
    }
  };

  const handlePrev = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex((prev) => prev - 1);
    }
  };

  const handleSubmit = async () => {
    clearInterval(timerRef.current);
    const payload = {
      assessment_id: questionData.assessment_id,
      answers: answers
    };
    try {
      const res = await PostOnBroadingAssessmentApi(payload).unwrap();
      console.log('Assessment submitted successfully:', res);
      await completeOnBroadingAssessmentApi({
        student_id: sessionStorage.getItem('userId')
      }).unwrap();
      if (isSkip) isSkip();
    } catch (error) {
      console.error('Error submitting assessment:', error);
    }
  };

  const currentQuestion = questionData?.questions?.[currentQuestionIndex];
  const totalQuestions = questionData?.questions?.length || 0;
  const isLastQuestion = currentQuestionIndex === totalQuestions - 1;
  const progressPercentage =
    totalQuestions > 0 ? ((currentQuestionIndex + 1) / totalQuestions) * 100 : 0;
  const isAnswerSelected = currentQuestion && answers[currentQuestion.question_number];

  const modalVariants = {
    initial: { opacity: 0, scale: 0.8 },
    animate: { opacity: 1, scale: 1, transition: { duration: 0.3, ease: 'easeOut' } },
    exit: { opacity: 0, scale: 0.8, transition: { duration: 0.2, ease: 'easeIn' } }
  };

  const contentVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0, transition: { duration: 0.3, delay: 0.1 } },
    exit: { opacity: 0, y: 20, transition: { duration: 0.2 } }
  };

  return (
    <motion.div
      className="fixed inset-0 z-50 flex justify-center items-center p-4 bg-black/70 backdrop-blur-sm"
      variants={modalVariants}
      initial="initial"
      animate="animate"
      exit="exit">
        {/* <Toastify res={error} resClear={() => setError(null)}} /> */}
      <Button
        name={'Ask me later'}
        onClick={isSkip}
        className="absolute top-4 right-4 transition-colors bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg"
      />
      <motion.div
        className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl overflow-hidden flex flex-col"
        variants={contentVariants}
        initial="initial"
        animate="animate"
        exit="exit">
        {isLoading || !currentQuestion ? (
          <div className="flex flex-col items-center justify-center h-80">
            <Spinner />
            {error && <p className="text-red-500 mt-4">{error.data.message}</p>}
            <p className="mt-4 text-lg text-gray-600">Loading Onboarding Assessment...</p>
          </div>
        ) : (
          <>
            <div className="p-8 pb-4 border-b border-gray-200 flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-800">Onboarding Assessment</h1>
                <p className="mt-2 text-gray-600">
                  This short quiz will help us understand your starting point and personalize your
                  learning journey. Do your best!
                </p>
              </div>
              <div className="w-20">
                <CircularProgressbar
                  value={(timeLeft / 120) * 100}
                  text={formatTime(timeLeft)}
                  styles={buildStyles({
                    textColor: '#4A55A2',
                    pathColor: '#667EEA',
                    trailColor: '#D2D3F7'
                  })}
                />
              </div>
            </div>

            <div className="w-full bg-gray-200 h-2">
              <motion.div
                className="bg-gradient-to-r from-indigo-500 to-purple-600 h-2"
                initial={{ width: 0 }}
                animate={{ width: `${progressPercentage}%` }}
                transition={{ duration: 0.5 }}
              />
            </div>

            <AnimatePresence mode="wait">
              <motion.div
                key={currentQuestionIndex}
                className="p-8"
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.3, ease: 'easeInOut' }}>
                <div className="mb-6">
                  <p className="text-sm font-semibold text-indigo-600 mb-1 uppercase">
                    {currentQuestion.section_type?.replace(/[-_]/g, ' ')}
                  </p>
                  <div className="text-xl font-semibold text-gray-900">
                    <span className="text-gray-500 font-medium mr-2">
                      Q{currentQuestion.question_number}:
                    </span>
                    <MathRenderer content={currentQuestion.question_text} />
                  </div>
                </div>

                {currentQuestion.image_base64 && (
                  <motion.div
                    className="my-6 flex justify-center"
                    whileHover={{ scale: 1.05 }}
                    transition={{ duration: 0.2 }}>
                    <img
                      src={`data:image/png;base64,${currentQuestion.image_base64}`}
                      alt="Question illustration"
                      className="max-w-full h-auto rounded-lg shadow-md border"
                    />
                  </motion.div>
                )}

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {Object.entries(currentQuestion.options).map(([key, value]) => {
                    const isSelected = answers[currentQuestion.question_number] === key;
                    return (
                      <motion.button
                        key={key}
                        onClick={() => handleOptionSelect(currentQuestion.question_number, key)}
                        className={`p-4 rounded-lg border-2 text-left transition-all duration-200 flex items-start ${
                          isSelected
                            ? 'bg-indigo-600 border-indigo-700 text-white shadow-lg'
                            : 'bg-white border-gray-300 text-gray-800 hover:border-indigo-400 hover:bg-indigo-50'
                        }`}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}>
                        <span
                          className={`font-bold mr-3 py-1 px-2.5 rounded-md ${
                            isSelected ? 'bg-white/20 text-white' : 'bg-indigo-100 text-indigo-700'
                          }`}>
                          {key}
                        </span>
                        <div className="flex-1 pt-1">
                          <MathRenderer content={value} />
                        </div>
                      </motion.button>
                    );
                  })}
                </div>

                <div className="mt-10 flex justify-between items-center">
                  <motion.button
                    onClick={handlePrev}
                    disabled={currentQuestionIndex === 0}
                    className="px-6 py-2 font-semibold text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}>
                    Previous
                  </motion.button>
                  <p className="text-gray-600 font-medium">
                    {currentQuestionIndex + 1} / {totalQuestions}
                  </p>
                  <motion.button
                    onClick={isLastQuestion ? handleSubmit : handleNext}
                    disabled={!isAnswerSelected}
                    className={`px-6 py-2 font-semibold text-white rounded-lg transition-all duration-200 shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed disabled:shadow-none ${
                      isLastQuestion
                        ? 'bg-green-500 hover:bg-green-600 focus:ring-green-500'
                        : 'bg-indigo-600 hover:bg-indigo-700 focus:ring-indigo-600'
                    }`}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}>
                    {isLastQuestion ? 'Submit Assessment' : 'Next'}
                  </motion.button>
                </div>
              </motion.div>
            </AnimatePresence>
          </>
        )}
      </motion.div>
    </motion.div>
  );
};

export default OnBroadingAssessment;
