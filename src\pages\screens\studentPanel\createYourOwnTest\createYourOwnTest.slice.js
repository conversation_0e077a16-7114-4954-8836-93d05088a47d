

import { createOwnTestApi } from '../../../../redux/api/api';

export const createYourOwnTestSlice = createOwnTestApi.injectEndpoints({
  endpoints: (builder) => ({
    createYourOwnTestExamNames: builder.query({
      query: () => '/exam_names',
      providesTags: ['CreateOwnTest']
    }),

    createYourOwnTestExamModules: builder.query({
      query: (examName) => `/exam_modules?exam_name=${examName}`,
      providesTags: ['CreateOwnTest']
    }),

    createYourOwnTestExamUnits: builder.query({
      query: ({ examName, subject }) => `/units?exam_name=${examName}&subject=${subject}`,
      providesTags: ['CreateOwnTest']
    }),

    createYourOwnTestExamSubtopics: builder.query({
      query: ({ examName, module, unit }) =>
        `/subtopics?exam_name=${examName}&subject=${module}&unit=${unit}`,
      providesTags: ['CreateOwnTest']
    })
  })
});

export const {
  useLazyCreateYourOwnTestExamNamesQuery,
  useLazyCreateYourOwnTestExamModulesQuery,
  useLazyCreateYourOwnTestExamUnitsQuery,
  useLazyCreateYourOwnTestExamSubtopicsQuery
} = createYourOwnTestSlice;
