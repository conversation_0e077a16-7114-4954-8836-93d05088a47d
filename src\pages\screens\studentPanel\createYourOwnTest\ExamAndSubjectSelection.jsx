import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
// eslint-disable-next-line no-unused-vars
import { motion, AnimatePresence } from 'framer-motion';
import {
  useLazyCreateYourOwnTestExamNamesQuery,
  useLazyCreateYourOwnTestExamModulesQuery
} from './createYourOwnTest.slice';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faAtom,
  faDna,
  faSquareRootAlt,
  faSpinner,
  faExclamationTriangle,
  faChevronLeft,
  faFlaskVial,
  faGears,
  faStethoscope
} from '@fortawesome/free-solid-svg-icons';
import { ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router';

const AuroraFlowBackground = () => {
  return (
    <div className="absolute inset-0 z-0 bg-slate-50 overflow-hidden">
      <motion.div
        className="absolute top-[-30%] left-[-30%] w-[60%] h-[60%] bg-[var(--color-student)]/40 rounded-full"
        style={{ filter: 'blur(120px)' }}
        animate={{
          x: ['-10%', '10%', '-10%'],
          y: ['-5%', '5%', '-5%'],
          scale: [1, 1.1, 1]
        }}
        transition={{
          duration: 30,
          repeat: Infinity,
          repeatType: 'mirror',
          ease: 'easeInOut'
        }}
      />
      <motion.div
        className="absolute bottom-[-30%] right-[-30%] w-[70%] h-[70%] bg-blue-300/40 rounded-full"
        style={{ filter: 'blur(120px)' }}
        animate={{
          x: ['10%', '-10%', '10%'],
          y: ['5%', '-5%', '5%'],
          scale: [1, 0.9, 1]
        }}
        transition={{
          duration: 35,
          repeat: Infinity,
          repeatType: 'mirror',
          ease: 'easeInOut',
          delay: 5
        }}
      />
      <motion.div
        className="absolute bottom-[20%] left-[10%] w-[40%] h-[40%] bg-blue-400/30 rounded-full"
        style={{ filter: 'blur(100px)' }}
        animate={{
          x: ['5%', '-5%', '5%'],
          y: ['-5%', '5%', '-5%'],
          rotate: [0, 15, 0]
        }}
        transition={{
          duration: 40,
          repeat: Infinity,
          repeatType: 'mirror',
          ease: 'easeInOut',
          delay: 10
        }}
      />
    </div>
  );
};

const ExamAndSubjectSelection = ({
  setSelectedExam,
  setSelectedModule,
  selectedExam,
  selectedModule
}) => {
  const [triggerExamNames, { data: examNamesData, isLoading: isLoadingExams, error: examError }] =
    useLazyCreateYourOwnTestExamNamesQuery();
  const [triggerModules, { data: modulesData, isLoading: isLoadingModules, error: moduleError }] =
    useLazyCreateYourOwnTestExamModulesQuery();

  const navigate = useNavigate();
  useEffect(() => {
    triggerExamNames();
  }, [triggerExamNames]);

  useEffect(() => {
    if (selectedExam) {
      triggerModules(selectedExam);
      setSelectedModule('');
    }
  }, [selectedExam, triggerModules, setSelectedModule]);

  const getExamNames = () => examNamesData?.exam_names?.map((exam) => exam.exam_name) || [];
  const getModules = () => modulesData?.subjects || [];

  const subjectIcons = {
    Physics: faAtom,
    Chemistry: faFlaskVial,
    Biology: faDna,
    Mathematics: faSquareRootAlt
  };

  const examIcons = {
    JEE: faGears,
    NEET: faStethoscope
  };

  const renderLoader = (text) => (
    <div className="flex items-center justify-center p-8 text-center space-x-3 text-lg text-slate-500">
      <FontAwesomeIcon
        icon={faSpinner}
        className="text-2xl text-[var(--color-student)] animate-spin"
      />
      <span>{text}</span>
    </div>
  );

  const renderError = (error, message) => (
    <div className="p-4 my-4 bg-red-50 border border-red-200 rounded-lg text-red-700 flex items-center justify-center space-x-3">
      <FontAwesomeIcon icon={faExclamationTriangle} className="text-xl" />
      <span>{error?.data?.error || message}</span>
    </div>
  );

  return (
    <div
      className="min-h-screen bg-slate-50 text-slate-800 p-4 font-sans overflow-hidden flex items-center justify-center"
      style={{ '--color-student': '#2563eb' }}>
      <AuroraFlowBackground />
      <div className="relative z-10 w-full max-w-5xl mx-auto flex flex-col items-center justify-center">
        <AnimatePresence mode="wait">
          {!selectedExam ? (
            <motion.div
              key="exam-selection"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -50 }}
              transition={{ duration: 0.6, ease: [0.22, 1, 0.36, 1] }}
              className="w-full flex flex-col items-center">
              <motion.button
                onClick={() => navigate('/sasthra')}
                className="fixed top-8 left-8 w-16 h-16 hover:cursor-pointer rounded-full bg-indigo-600 text-white shadow-xl flex items-center justify-center"
                whileHover={{
                  scale: 1.1,
                  boxShadow: '0 5px 20px rgba(79, 70, 229, 0.4)'
                }}
                whileTap={{ scale: 0.9 }}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}>
                <motion.div
                  animate={{ x: [-2, 2, -2] }}
                  transition={{ duration: 1.5, repeat: Infinity }}>
                  <ArrowLeft className="h-8 w-8" />
                </motion.div>

                {/* Ripple effect */}
                <motion.span
                  className="absolute inset-0 rounded-full border-2 border-indigo-400"
                  animate={{
                    scale: [1, 1.5],
                    opacity: [0.6, 0]
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity
                  }}
                />
              </motion.button>
              <h1 className="text-4xl md:text-5xl font-bold text-center mb-4 text-slate-900">
                Create Your Own Test
              </h1>
              <p className="text-slate-600 text-lg mb-12">Select your exam stream to begin.</p>
              {isLoadingExams ? (
                renderLoader('Initializing streams...')
              ) : examError ? (
                renderError(examError, 'Stream initialization failed')
              ) : (
                <div className="flex flex-wrap  justify-center items-center gap-12">
                  {getExamNames().map((exam, i) => (
                    <motion.div
                      key={exam}
                      onClick={() => setSelectedExam(exam)}
                      className="group relative w-64 h-40 rounded-2xl cursor-pointer"
                      style={{ transformStyle: 'preserve-3d' }}
                      initial={{ opacity: 0, y: 50 }}
                      animate={{
                        opacity: 1,
                        y: 0,
                        transition: { delay: i * 0.15 }
                      }}
                      whileHover="hover">
                      <motion.div
                        className="absolute inset-0 bg-white/60 backdrop-blur-lg rounded-2xl border border-white/20 shadow-lg"
                        style={{ transform: 'translateZ(-1px)' }}
                      />
                      <motion.div
                        className="absolute inset-0 rounded-2xl"
                        style={{
                          background:
                            'linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 50%)'
                        }}
                      />
                      <motion.div
                        className="absolute inset-0 rounded-2xl border-2 border-[var(--color-student)] opacity-0"
                        variants={{ hover: { opacity: 1 } }}
                        transition={{ duration: 0.3 }}
                      />
                      <div
                        className="relative z-10 flex items-center justify-center h-full flex-col"
                        style={{ transform: 'translateZ(20px)' }}>
                        <FontAwesomeIcon
                          icon={examIcons[exam] || faGears}
                          className="text-5xl text-[var(--color-student)]"
                        />
                        <h3 className="text-2xl font-bold text-slate-800 mt-4">{exam}</h3>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </motion.div>
          ) : (
            <motion.div
              key="subject-selection"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -50 }}
              transition={{ duration: 0.6, ease: [0.22, 1, 0.36, 1] }}
              className="w-full flex flex-col items-center">
              <button
                onClick={() => setSelectedExam('')}
                className="group flex items-center text-sm font-semibold text-[var(--color-student)] hover:text-slate-900 transition-colors mb-8">
                <FontAwesomeIcon
                  icon={faChevronLeft}
                  className="mr-2 transition-transform group-hover:-translate-x-1"
                />
                Change Exam Stream
              </button>
              <h2 className="text-3xl font-bold text-center mb-16">
                Select Subject for{' '}
                <span className="text-[var(--color-student)]">{selectedExam}</span>
              </h2>

              {isLoadingModules ? (
                renderLoader('Loading subject nodes...')
              ) : moduleError ? (
                renderError(moduleError, 'Could not load nodes')
              ) : (
                <div className="relative w-96 h-96 flex items-center justify-center">
                  <div className="absolute w-32 h-32">
                    <motion.div
                      className="absolute inset-0 rounded-full"
                      style={{
                        background:
                          'radial-gradient(circle, var(--color-student) 50%, #1e40af 100%)',
                        boxShadow:
                          '0 0 20px var(--color-student), 0 0 40px var(--color-student), 0 0 60px #1e40af'
                      }}
                      animate={{ scale: [1, 1.05, 1] }}
                      transition={{ duration: 3, repeat: Infinity, ease: 'easeInOut' }}
                    />
                    <div className="relative w-full h-full flex flex-col items-center justify-center text-center p-2 z-10">
                      <p className="font-bold text-lg text-white drop-shadow-lg">{selectedExam}</p>
                    </div>
                  </div>

                  {getModules().map((module, i) => {
                    const angle = (i / getModules().length) * 2 * Math.PI - Math.PI / 2;
                    const radius = 180;
                    const x = radius * Math.cos(angle);
                    const y = radius * Math.sin(angle);
                    const isSelected = selectedModule === module;

                    return (
                      <motion.div
                        key={module}
                        className="absolute top-1/2 left-1/2"
                        initial={{ x: '-50%', y: '-50%', opacity: 0 }}
                        animate={{
                          translateX: x,
                          translateY: y,
                          opacity: 1
                        }}
                        transition={{ delay: i * 0.1, type: 'spring' }}>
                        <motion.div
                          onClick={() => setSelectedModule(module)}
                          className={`group relative w-32 h-32 rounded-full flex flex-col items-center justify-center cursor-pointer`}
                          whileHover={{ scale: 1.1, zIndex: 10 }}>
                          <motion.div
                            className={`absolute inset-0 rounded-full transition-all duration-300 shadow-xl ${
                              isSelected
                                ? 'bg-[var(--color-student)] border-2 border-blue-300'
                                : 'bg-white/60 backdrop-blur-lg border border-white/20'
                            }`}
                          />
                          <motion.div
                            className="absolute -inset-1 rounded-full border-2 border-[var(--color-student)] opacity-0"
                            variants={{
                              hover: { opacity: 1, rotate: 360 },
                              initial: { opacity: 0 }
                            }}
                            transition={{ duration: 1, ease: 'linear' }}
                          />
                          <FontAwesomeIcon
                            icon={subjectIcons[module]}
                            className={`relative text-3xl mb-2 transition-colors duration-300 ${
                              isSelected ? 'text-white' : 'text-[var(--color-student)]'
                            }`}
                          />
                          <span
                            className={`relative font-semibold text-sm transition-colors duration-300 ${
                              isSelected ? 'text-white' : 'text-slate-800'
                            }`}>
                            {module}
                          </span>
                        </motion.div>
                      </motion.div>
                    );
                  })}
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

ExamAndSubjectSelection.propTypes = {
  setSelectedExam: PropTypes.func.isRequired,
  setSelectedModule: PropTypes.func.isRequired,
  selectedExam: PropTypes.string.isRequired,
  selectedModule: PropTypes.string.isRequired
};

export default ExamAndSubjectSelection;
