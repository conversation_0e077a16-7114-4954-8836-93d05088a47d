import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faUsers, 
  faSearch, 
  faSpinner, 
  faExclamationTriangle,
  faGraduationCap,
  faEnvelope,
  faPhone,
  faSort,
  faFilter,
  faChevronRight
} from '@fortawesome/free-solid-svg-icons';
import { useLazyGetListStudentsQuery } from '../centreCounselorDashboard/centreCounselorDashboard.slice';

const ListStudents = () => {
  const [getListStudents, { data: studentsData, isLoading, error }] = useLazyGetListStudentsQuery();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCourse, setSelectedCourse] = useState('All');
  const [expandedStudent, setExpandedStudent] = useState(null);

  useEffect(() => {
    getListStudents();
  }, [getListStudents]);

  const students = studentsData?.students || [];
  
  // Filter students based on search and course filter
  const filteredStudents = students.filter(student => {
    const matchesSearch = searchTerm === '' || 
      `${student.first_name} ${student.last_name}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCourse = selectedCourse === 'All' || student.course === selectedCourse;
    
    return matchesSearch && matchesCourse;
  });

  // Get unique courses for filter
  const courses = ['All', ...new Set(students.map(student => student.course))];

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { 
        duration: 0.5,
        when: "beforeChildren",
        staggerChildren: 0.08
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: { y: 0, opacity: 1 }
  };

  // Generate random pastel color based on name
  const getColorFromName = (name) => {
    const colors = [
      'bg-blue-100 text-blue-800',
      'bg-green-100 text-green-800',
      'bg-purple-100 text-purple-800',
      'bg-pink-100 text-pink-800',
      'bg-yellow-100 text-yellow-800',
      'bg-indigo-100 text-indigo-800',
      'bg-red-100 text-red-800',
      'bg-teal-100 text-teal-800'
    ];
    
    const hash = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colors[hash % colors.length];
  };

  return (
    <motion.div
      className="bg-white rounded-xl shadow-lg overflow-hidden"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header */}
      <div className="bg-gradient-to-r from-[var(--color-counselor)] to-amber-400 p-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <h2 className="text-2xl font-bold text-white flex items-center">
            <FontAwesomeIcon icon={faUsers} className="mr-3" />
            Student Directory
          </h2>
          
          <div className="flex flex-col sm:flex-row gap-3 w-full md:w-auto">
            {/* Search */}
            <div className="relative">
              <input
                type="text"
                placeholder="Search students..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-white w-full sm:w-64"
              />
              <FontAwesomeIcon 
                icon={faSearch} 
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              />
            </div>
            
            {/* Course Filter */}
            <div className="relative">
              <select
                value={selectedCourse}
                onChange={(e) => setSelectedCourse(e.target.value)}
                className="pl-10 pr-4 py-2 rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-white appearance-none w-full sm:w-48"
              >
                {courses.map(course => (
                  <option key={course} value={course}>{course}</option>
                ))}
              </select>
              <FontAwesomeIcon 
                icon={faFilter} 
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              />
            </div>
          </div>
        </div>
        
        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
          <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4">
            <p className="text-white text-sm font-medium">Total Students</p>
            <p className="text-white text-2xl font-bold">{students.length}</p>
          </div>
          <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4">
            <p className="text-white text-sm font-medium">Active Courses</p>
            <p className="text-white text-2xl font-bold">{courses.length - 1}</p>
          </div>
          <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4">
            <p className="text-white text-sm font-medium">Avg. 12th Marks</p>
            <p className="text-white text-2xl font-bold">
              {students.length > 0 
                ? (students.reduce((sum, student) => sum + parseFloat(student.marks_12th || 0), 0) / students.length).toFixed(1) + '%'
                : 'N/A'}
            </p>
          </div>
          <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4">
            <p className="text-white text-sm font-medium">Avg. 10th Marks</p>
            <p className="text-white text-2xl font-bold">
              {students.length > 0 
                ? (students.reduce((sum, student) => sum + parseFloat(student.marks_10th || 0), 0) / students.length).toFixed(1) + '%'
                : 'N/A'}
            </p>
          </div>
        </div>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex flex-col items-center justify-center py-16">
          <FontAwesomeIcon 
            icon={faSpinner} 
            spin 
            className="text-[var(--color-counselor)] text-4xl mb-4" 
          />
          <p className="text-gray-500 text-lg">Loading student records...</p>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="flex flex-col items-center justify-center py-16 px-4">
          <FontAwesomeIcon 
            icon={faExclamationTriangle} 
            className="text-red-500 text-4xl mb-4" 
          />
          <p className="text-red-500 text-lg font-medium">Failed to load students</p>
          <p className="text-gray-500 mt-2 text-center">
            {error.message || "Please try again later or contact support"}
          </p>
        </div>
      )}

      {/* Students List */}
      {!isLoading && !error && (
        <div className="p-6">
          {filteredStudents.length > 0 ? (
            <motion.div 
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              className="grid grid-cols-1 gap-4"
            >
              {filteredStudents.map((student) => (
                <motion.div
                  key={student.id}
                  variants={itemVariants}
                  className="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow"
                >
                  {/* Student Card */}
                  <div 
                    className="p-4 cursor-pointer"
                    onClick={() => setExpandedStudent(expandedStudent === student.id ? null : student.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className={`w-12 h-12 rounded-full flex items-center justify-center text-lg font-bold ${getColorFromName(student.first_name)}`}>
                          {student.first_name.charAt(0)}{student.last_name.charAt(0)}
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900">
                            {student.first_name} {student.last_name}
                          </h3>
                          <div className="flex items-center text-sm text-gray-500">
                            <FontAwesomeIcon icon={faGraduationCap} className="mr-1" />
                            <span>{student.course}</span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center">
                        <div className="hidden md:block mr-6">
                          <div className="flex flex-col items-end">
                            <div className="text-sm font-medium">10th: <span className="text-green-600">{student.marks_10th}%</span></div>
                            <div className="text-sm font-medium">12th: <span className="text-blue-600">{student.marks_12th}%</span></div>
                          </div>
                        </div>
                        <FontAwesomeIcon 
                          icon={faChevronRight} 
                          className={`text-gray-400 transition-transform ${expandedStudent === student.id ? 'rotate-90' : ''}`}
                        />
                      </div>
                    </div>
                  </div>
                  
                  {/* Expanded Details */}
                  <AnimatePresence>
                    {expandedStudent === student.id && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="border-t border-gray-200 bg-gray-50"
                      >
                        <div className="p-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <h4 className="text-sm font-medium text-gray-500 mb-2">Contact Information</h4>
                            <div className="space-y-2">
                              <div className="flex items-center text-sm">
                                <FontAwesomeIcon icon={faEnvelope} className="text-gray-400 w-5 mr-2" />
                                <span className="text-gray-800">{student.email}</span>
                              </div>
                              <div className="flex items-center text-sm">
                                <FontAwesomeIcon icon={faPhone} className="text-gray-400 w-5 mr-2" />
                                <span className="text-gray-800">{student.phone}</span>
                              </div>
                            </div>
                          </div>
                          
                          <div>
                            <h4 className="text-sm font-medium text-gray-500 mb-2">Academic Performance</h4>
                            <div className="space-y-3">
                              <div>
                                <div className="flex justify-between text-sm mb-1">
                                  <span className="font-medium">10th Standard</span>
                                  <span className="font-bold text-green-600">{student.marks_10th}%</span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-2">
                                  <div 
                                    className="bg-green-600 h-2 rounded-full" 
                                    style={{ width: `${Math.min(100, student.marks_10th)}%` }}
                                  ></div>
                                </div>
                              </div>
                              
                              <div>
                                <div className="flex justify-between text-sm mb-1">
                                  <span className="font-medium">12th Standard</span>
                                  <span className="font-bold text-blue-600">{student.marks_12th}%</span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-2">
                                  <div 
                                    className="bg-blue-600 h-2 rounded-full" 
                                    style={{ width: `${Math.min(100, student.marks_12th)}%` }}
                                  ></div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              ))}
            </motion.div>
          ) : (
            <motion.div 
              className="text-center py-16 px-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 mb-4">
                <FontAwesomeIcon icon={faUsers} className="text-gray-400 text-xl" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Students Found</h3>
              <p className="text-gray-500 max-w-md mx-auto">
                {students.length > 0 
                  ? "No students match your current search criteria. Try adjusting your filters."
                  : "There are no students registered in the system yet. Add students to see them listed here."}
              </p>
            </motion.div>
          )}
        </div>
      )}
    </motion.div>
  );
};

export default ListStudents;
