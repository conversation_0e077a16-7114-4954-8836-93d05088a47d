import { useState, useMemo, useEffect, useCallback, Fragment } from 'react';
import PropTypes from 'prop-types';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Search,
  X,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  FileText,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  Table2,
  ArrowDown,
  ArrowUp
} from 'lucide-react';
import clsx from 'clsx';
import Input from '../Field/Input'; // Your existing component
import Button from '../Field/Button'; // Your existing component

const DOTS = '...';

// --- Animation Variants (for cleaner code) ---
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      when: 'beforeChildren',
      staggerChildren: 0.05
    }
  }
};

const rowVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { type: 'spring', stiffness: 100, damping: 15 } },
  exit: { opacity: 0, x: -20, transition: { duration: 0.2 } }
};

// --- Sub-components for better structure ---

const SkeletonRow = ({ columns }) => (
  <tr className="animate-pulse">
    {columns.map((_, index) => (
      <td key={index} className="px-6 py-4">
        <div className="h-4 bg-gray-200 rounded w-full"></div>
      </td>
    ))}
    <td className="px-6 py-4">
      <div className="h-4 bg-gray-200 rounded w-16 mx-auto"></div>
    </td>
  </tr>
);

const MotionButton = ({ children, className, ...props }) => (
  <motion.button
    whileHover={{ scale: 1.05, y: -1 }}
    whileTap={{ scale: 0.95, y: 0 }}
    transition={{ type: 'spring', stiffness: 300, damping: 20 }}
    className={className}
    {...props}>
    {children}
  </motion.button>
);

// --- The Main Table Component ---

export default function Table({
  title = 'Table',
  titleIcon = <Table2 className="h-5 w-5" />,
  buttonName = 'Add New',
  onAddNew,
  header = [],
  data = [],
  searchBy = [],
  searchPlaceholder = 'Search...',
  onView,
  onEdit,
  onDelete,
  onPdf,
  customColumn,
  itemsPerPage = 10,
  loading = false,
  emptyMessage = 'No records found'
}) {
  const [currentPage, setCurrentPage] = useState(1);
  const [search, setSearch] = useState('');
  const [pageSize, setPageSize] = useState(itemsPerPage);
  const [sortConfig, setSortConfig] = useState(null);

  const visibleHeaders = useMemo(() => header.filter((col) => col.show), [header]);

  // --- Data Processing Pipeline (Memoized for performance) ---
  const processedData = useMemo(() => {
    let filtered = [...data];

    // 1. Search Filtering
    if (search.trim()) {
      const lowercasedSearchTerm = search.toLowerCase();
      filtered = data.filter((item) => {
        const keysToSearch =
          searchBy?.length > 0 ? searchBy : visibleHeaders.map((h) => h.data).filter(Boolean);
        return keysToSearch.some((key) => {
          const value = key
            .split('.')
            .reduce((o, i) => (o && o[i] !== undefined ? o[i] : null), item);
          return String(value).toLowerCase().includes(lowercasedSearchTerm);
        });
      });
    }

    // 2. Sorting
    if (sortConfig !== null) {
      filtered.sort((a, b) => {
        const valA = sortConfig.key.split('.').reduce((o, i) => o?.[i], a);
        const valB = sortConfig.key.split('.').reduce((o, i) => o?.[i], b);

        if (valA < valB) return sortConfig.direction === 'ascending' ? -1 : 1;
        if (valA > valB) return sortConfig.direction === 'ascending' ? 1 : -1;
        return 0;
      });
    }

    return filtered;
  }, [search, data, visibleHeaders, searchBy, sortConfig]);

  const totalItems = processedData.length;
  const totalPages = Math.ceil(totalItems / pageSize);

  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    return processedData.slice(startIndex, startIndex + pageSize);
  }, [processedData, currentPage, pageSize]);

  // --- Effects and Handlers ---
  useEffect(() => {
    setCurrentPage(1);
  }, [search, pageSize, sortConfig]);

  useEffect(() => {
    if (currentPage > totalPages && totalPages > 0) {
      setCurrentPage(totalPages);
    }
  }, [totalPages, currentPage]);

  const handleSort = (key) => {
    let direction = 'ascending';
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }
    setSortConfig({ key, direction });
  };

  // --- Render Logic ---
  const renderTableHeader = () => (
    <div className="flex flex-col md:flex-row items-center justify-between gap-4 p-4 border-b border-gray-200">
      <div className="flex items-center gap-3">
        {titleIcon && <span className="text-blue-600">{titleIcon}</span>}
        <h2 className="text-xl font-bold text-gray-800">{title}</h2>
      </div>
      <div className="w-full md:w-auto flex flex-col sm:flex-row items-stretch sm:items-center gap-3">
        <div className="relative flex-1 sm:w-64 md:w-80">
          <Input
            placeholder={searchPlaceholder}
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="border border-gray-300 rounded-md placeholder:text-sm"
            leftIcon={<Search className="h-4 w-4 text-gray-400" />}
            rightIcon={
              <AnimatePresence>
                {search && (
                  <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} exit={{ scale: 0 }}>
                    <Button
                      onClick={() => setSearch('')}
                      name={<X className="h-4 w-4" />}
                      className="h-7 w-7 p-0 hover:bg-gray-100 rounded-full text-gray-400 hover:text-gray-600"
                    />
                  </motion.div>
                )}
              </AnimatePresence>
            }
          />
        </div>
        {onAddNew && (
          <MotionButton
            onClick={onAddNew}
            className="whitespace-nowrap bg-blue-600 text-white hover:bg-blue-700 px-4 py-2 rounded-md h-10 text-sm font-medium flex items-center justify-center">
            {buttonName}
          </MotionButton>
        )}
      </div>
    </div>
  );

  const renderTableBody = () => (
    <div className="relative overflow-x-auto h-[60vh] min-h-[300px]">
      <AnimatePresence>
        {loading && (
          <motion.div
            key="loader"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-white/70 backdrop-blur-sm flex items-center justify-center z-20">
            <div className="flex items-center gap-2 text-gray-600">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ repeat: Infinity, duration: 1, ease: 'linear' }}
                className="h-5 w-5 border-b-2 border-blue-600 rounded-full"
              />
              <span>Loading...</span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      <table className="w-full min-w-[700px]">
        <thead className="bg-gray-50 sticky top-0 z-10 backdrop-blur-sm">
          <tr>
            {visibleHeaders.map((column) => (
              <th
                key={column.data || column.header}
                className="px-6 py-3 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">
                <div
                  className={clsx('flex items-center gap-2', {
                    'cursor-pointer hover:text-gray-800': column.sortable
                  })}
                  onClick={() => column.sortable && handleSort(column.data)}>
                  {column.header}
                  {column.sortable && sortConfig?.key === column.data && (
                    <motion.div initial={{ opacity: 0, y: -5 }} animate={{ opacity: 1, y: 0 }}>
                      {sortConfig.direction === 'ascending' ? (
                        <ArrowUp size={14} />
                      ) : (
                        <ArrowDown size={14} />
                      )}
                    </motion.div>
                  )}
                </div>
              </th>
            ))}
            {(onView || onEdit || onDelete || onPdf || customColumn) && (
              <th className="px-6 py-3 text-center text-xs font-semibold text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            )}
          </tr>
        </thead>
        <motion.tbody
          layout
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="divide-y divide-gray-200">
          <AnimatePresence mode="sync">
            {paginatedData.length > 0 ? (
              paginatedData.map((item, i) => {
                const startIndex = (currentPage - 1) * pageSize;
                return (
                  <motion.tr
                    key={item.id || startIndex + i}
                    layout="position"
                    variants={rowVariants}
                    exit="exit"
                    className="hover:bg-blue-50/50 transition-colors duration-200 even:bg-gray-50/50">
                    {visibleHeaders.map((column) => (
                      <td
                        key={column.data}
                        className={`px-6 py-4 text-sm text-gray-800 ${column.className || ''}`}>
                        {column.render
                          ? column.render(item, startIndex + i)
                          : column.data === 'sno'
                            ? startIndex + i + 1
                            : (item[column.data] ?? '–')}
                      </td>
                    ))}
                    {(onView || onEdit || onDelete || onPdf || customColumn) && (
                      <td className="px-6 py-4 text-center">
                        <ActionsCell
                          item={item}
                          index={i}
                          {...{ onView, onEdit, onDelete, onPdf }}
                        />
                      </td>
                    )}
                  </motion.tr>
                );
              })
            ) : (
              <motion.tr
                key="empty"
                variants={rowVariants}
                initial="hidden"
                animate="visible"
                exit="exit">
                <td
                  colSpan={visibleHeaders.length + 1}
                  className="px-6 py-16 text-center text-gray-500">
                  {search ? `No results found for "${search}"` : emptyMessage}
                </td>
              </motion.tr>
            )}
          </AnimatePresence>
        </motion.tbody>
      </table>
    </div>
  );

  const renderTableFooter = () =>
    totalItems > 0 &&
    totalPages > 1 && (
      <div className="flex flex-col sm:flex-row items-center justify-between gap-4 p-4 border-t border-gray-200">
        <div className="flex items-center gap-2 text-sm text-gray-700">
          <span>Show</span>
          <PageSizeSelector pageSize={pageSize} setPageSize={setPageSize} />
          <span>of {totalItems} results</span>
        </div>
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={setCurrentPage}
        />
      </div>
    );

  return (
    <div className="w-full bg-white rounded-lg border border-gray-200 shadow-sm">
      {renderTableHeader()}
      {renderTableBody()}
      {renderTableFooter()}
    </div>
  );
}

// --- Helper Sub-components for Actions and Pagination ---

const ActionsCell = ({ item, index, onView, onEdit, onDelete, onPdf }) => {
  const [isOpen, setIsOpen] = useState(false);
  // Same logic as before, just isolated
  const actions = [
    onView && {
      key: 'view',
      onClick: () => onView(item),
      icon: item.uploadstatus === false ? <EyeOff size={16} /> : <Eye size={16} />,
      label: 'View',
      disabled: item.uploadstatus === false
    },
    onEdit && { key: 'edit', onClick: () => onEdit(item), icon: <Edit size={16} />, label: 'Edit' },
    onPdf && {
      key: 'pdf',
      onClick: () => onPdf(item),
      icon: <FileText size={16} />,
      label: 'Download PDF'
    },
    onDelete && {
      key: 'delete',
      onClick: () => onDelete(item),
      icon: <Trash2 size={16} />,
      label: 'Delete',
      variant: 'destructive'
    }
  ].filter(Boolean);

  if (actions.length <= 2) {
    return (
      <div className="flex items-center gap-1 justify-center">
        {actions.map((action) => (
          <ActionButton key={action.key} {...action} />
        ))}
      </div>
    );
  }

  return (
    <div className="relative" onClick={(e) => e.stopPropagation()}>
      <MotionButton
        className="h-8 w-8 p-0 hover:bg-gray-100 rounded-full flex items-center justify-center"
        onClick={() => setIsOpen(!isOpen)}>
        <MoreHorizontal size={16} />
      </MotionButton>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -10 }}
            className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-50 origin-top-right"
            onMouseLeave={() => setIsOpen(false)}>
            <div className="py-1">
              {actions.map((action) => (
                <button
                  key={action.key}
                  onClick={() => {
                    action.onClick();
                    setIsOpen(false);
                  }}
                  disabled={action.disabled}
                  className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 flex items-center gap-3 ${action.disabled ? 'text-gray-400 cursor-not-allowed' : 'text-gray-700'}`}>
                  <span className={clsx({ 'text-red-500': action.variant === 'destructive' })}>
                    {action.icon}
                  </span>
                  <span className={clsx({ 'text-red-500': action.variant === 'destructive' })}>
                    {action.label}
                  </span>
                </button>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

const ActionButton = ({ icon, onClick, label, disabled, variant }) => (
  <MotionButton
    title={label}
    onClick={onClick}
    disabled={disabled}
    className={clsx('h-8 w-8 p-0 rounded-full flex items-center justify-center transition-colors', {
      'text-red-500 hover:text-red-600 hover:bg-red-50': variant === 'destructive',
      'text-gray-400 cursor-not-allowed': disabled,
      'text-gray-600 hover:text-gray-900 hover:bg-gray-100': !disabled && variant !== 'destructive'
    })}>
    {icon}
  </MotionButton>
);

const PageSizeSelector = ({ pageSize, setPageSize }) => {
  const [isOpen, setIsOpen] = useState(false);
  return (
    <div className="relative">
      <MotionButton
        onClick={() => setIsOpen(!isOpen)}
        className="w-16 h-8 border border-gray-300 rounded-md text-sm bg-white hover:bg-gray-50 flex items-center justify-center">
        {pageSize}
      </MotionButton>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            className="absolute bottom-full mb-1 left-0 bg-white border rounded shadow-lg z-50 origin-bottom-left"
            onMouseLeave={() => setIsOpen(false)}>
            {[10, 20, 50, 100].map((size) => (
              <button
                key={size}
                onClick={() => {
                  setPageSize(size);
                  setIsOpen(false);
                }}
                className="block w-full text-left px-4 py-2 text-sm hover:bg-gray-100">
                {size}
              </button>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

const Pagination = ({ currentPage, totalPages, onPageChange }) => {
  const range = useMemo(() => {
    // Same pagination logic as before
    const totalPageNumbers = 7;
    if (totalPages <= totalPageNumbers) return Array.from({ length: totalPages }, (_, i) => i + 1);
    const siblingCount = 1;
    const leftSiblingIndex = Math.max(currentPage - siblingCount, 1);
    const rightSiblingIndex = Math.min(currentPage + siblingCount, totalPages);
    const shouldShowLeftDots = leftSiblingIndex > 2;
    const shouldShowRightDots = rightSiblingIndex < totalPages - 2;
    if (!shouldShowLeftDots && shouldShowRightDots) {
      let leftRange = Array.from({ length: 5 }, (_, i) => i + 1);
      return [...leftRange, DOTS, totalPages];
    }
    if (shouldShowLeftDots && !shouldShowRightDots) {
      let rightRange = Array.from({ length: 5 }, (_, i) => totalPages - 4 + i);
      return [1, DOTS, ...rightRange];
    }
    if (shouldShowLeftDots && shouldShowRightDots) {
      let middleRange = Array.from(
        { length: rightSiblingIndex - leftSiblingIndex + 1 },
        (_, i) => leftSiblingIndex + i
      );
      return [1, DOTS, ...middleRange, DOTS, totalPages];
    }
    return [];
  }, [totalPages, currentPage]);

  return (
    <div className="flex items-center gap-1">
      <MotionButton
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className="h-8 w-8 p-0 border rounded-md flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50">
        <ChevronLeft size={16} />
      </MotionButton>
      {range.map((page, i) =>
        page === DOTS ? (
          <span key={`${page}-${i}`} className="px-2 py-1 text-gray-500 text-sm">
            ...
          </span>
        ) : (
          <MotionButton
            key={page}
            onClick={() => onPageChange(page)}
            className={clsx('h-8 w-8 p-0 rounded-md text-sm', {
              'bg-blue-600 text-white font-bold': page === currentPage,
              'border border-gray-300 hover:bg-gray-50': page !== currentPage
            })}>
            {page}
          </MotionButton>
        )
      )}
      <MotionButton
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className="h-8 w-8 p-0 border rounded-md flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50">
        <ChevronRight size={16} />
      </MotionButton>
    </div>
  );
};

// PropTypes remain the same
Table.propTypes = {
  title: PropTypes.string,
  titleIcon: PropTypes.node,
  buttonName: PropTypes.string,
  onAddNew: PropTypes.func,
  header: PropTypes.arrayOf(
    PropTypes.shape({
      header: PropTypes.string.isRequired,
      show: PropTypes.bool.isRequired,
      data: PropTypes.string,
      className: PropTypes.string,
      render: PropTypes.func
    })
  ).isRequired,
  data: PropTypes.arrayOf(PropTypes.object).isRequired,
  searchBy: PropTypes.arrayOf(PropTypes.string),
  searchPlaceholder: PropTypes.string,
  onView: PropTypes.func,
  onEdit: PropTypes.func,
  onDelete: PropTypes.func,
  onPdf: PropTypes.func,
  customHeader: PropTypes.node,
  customColumn: PropTypes.bool,
  itemsPerPage: PropTypes.number,
  loading: PropTypes.bool,
  emptyMessage: PropTypes.string
};