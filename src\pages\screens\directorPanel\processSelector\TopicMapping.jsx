import React, { useEffect, useState } from 'react';
import Button from '../../../../components/Field/Button';
import PopUp from '../../../../components/PopUp/PopUp';
import SearchableDropdown from '../../../../components/Field/SearchableDropdown';
import Input from '../../../../components/Field/Input';
import {
  setCouresData,
  setSubjectData,
  setTopicData,
  useCreateTopicServiceMutation,
  useLazyGetAllTopicsServiceQuery,
  useLazyGetCourseServiceQuery,
  useLazyGetSubjectServiceQuery
} from './processSelector.slice';
import { useDispatch, useSelector } from 'react-redux';
import Toastify from '../../../../components/PopUp/Toastify';
import Table from '../../../../components/Layout/Table';
import { TopicMappingHeader } from './TableHeaderData';

const TopicMapping = () => {
  const [popUp, setPopUp] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState({ id: '', name: '' });
  const [selectedSubject, setSelectedSubject] = useState({ id: '', name: '' });
  const [topic, setTopic] = useState('');
  const [res, setRes] = useState(null);

  const [getCourseData] = useLazyGetCourseServiceQuery();
  const [getSubjectData] = useLazyGetSubjectServiceQuery();
  const [createTopicApi] = useCreateTopicServiceMutation();
  const [getTopicDataService] = useLazyGetAllTopicsServiceQuery();
  const dispatch = useDispatch();

  const courseData = useSelector((state) => state.processSelector.courseData);
  const subjectData = useSelector((state) => state.processSelector.subjectData);
  const topicData = useSelector((state) => state.processSelector.topicsData);

  useEffect(() => {
    fetchCourse();
    fetchSubject();
    fetchAllTopicsService();
  }, []);

  const fetchCourse = async () => {
    try {
      const res = await getCourseData().unwrap();
      dispatch(setCouresData(res));
    } catch (error) {
      setRes(error);
    }
  };

  const fetchSubject = async () => {
    try {
      const res = await getSubjectData().unwrap();
      dispatch(setSubjectData(res));
    } catch (error) {
      setRes(error);
    }
  };

  const fetchAllTopicsService = async () => {
    try {
      const res = await getTopicDataService().unwrap();
      dispatch(setTopicData(res));
      // console.log('All Topics Data:', res);
    } catch (error) {
      setRes(error);
    }
  };

  const createTopicPostService = async () => {
    try {
      const res = await createTopicApi({
        course_id: selectedCourse.id,
        course_name: selectedCourse.name,
        subject_id: selectedSubject.id,
        subject_name: selectedSubject.name,
        topic_name: topic
      }).unwrap();
      fetchAllTopicsService();
      setRes(res);
    } catch (error) {
      setRes(error);
    } finally {
      setPopUp(false);
      setSelectedCourse({ id: '', name: '' });
      setSelectedSubject({ id: '', name: '' });
      setTopic('');
    }
  };

  return (
    <div>
      <Toastify res={res} resClear={() => setRes(null)} />
      {popUp && (
        <PopUp
          title={'Create Topic Mapping'}
          onClose={() => {
            setPopUp(false);
            setSelectedCourse({ id: '', name: '' });
            setSelectedSubject({ id: '', name: '' });
            setTopic('');
          }}
          post={createTopicPostService}>
          <div className="grid grid-cols-2 gap-2 mb-4">
            <SearchableDropdown
              label={'Course Name'}
              value={selectedCourse?.id}
              placeholder="Select/Search the Course Name"
              options={courseData}
              onChange={(e) => setSelectedCourse(e)}
              required
            />
            <SearchableDropdown
              label={'Subject Name'}
              value={selectedSubject?.id}
              placeholder={'Select/Search the Subject Name'}
              options={subjectData}
              onChange={(e) => setSelectedSubject(e)}
              disabled={!selectedCourse?.id}
              required
            />
          </div>
          <Input
            label={'Topic Name'}
            value={topic}
            onChange={(e) => setTopic(e.target.value)}
            className={'border rounded'}
            disabled={!selectedCourse?.id || !selectedSubject?.id}
            required
          />
        </PopUp>
      )}
      {/* <section className="flex justify-between items-center">
        <div></div>
        <div>
          <Button name={'Topic Mapping'} className={''} onClick={() => setPopUp(true)} />
        </div>
      </section> */}
      <Table
        title="Topic Mapping"
        onAddNew={() => setPopUp(true)}
        buttonName="Create Topic Mapping"
        header={TopicMappingHeader}
        data={topicData}
        searchBy={['course_name', 'subject_name', 'topic_name']}
        searchPlaceholder='Search by Course, Subject or Topic Name'
      />
    </div>
  );
};

export default TopicMapping;
