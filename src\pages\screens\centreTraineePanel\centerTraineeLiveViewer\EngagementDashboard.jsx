import React from 'react';
import { X } from 'lucide-react';

const EngagementDashboard = ({ quizId, onClose }) => {
  return (
    <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-8 border border-white/10 max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-white">Engagement Dashboard</h2>
        <button onClick={onClose} className="text-white hover:text-blue-400">
          <X size={24} />
        </button>
      </div>
      
      <div className="text-white">
        <p className="text-lg mb-4">Quiz ID: {quizId}</p>
        <p className="text-gray-300">Engagement analytics will be displayed here...</p>
      </div>
    </div>
  );
};

export default EngagementDashboard;
