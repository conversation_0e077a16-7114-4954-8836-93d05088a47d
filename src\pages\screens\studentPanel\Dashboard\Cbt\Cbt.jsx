import React, { useEffect, useState } from 'react';
import { setCbt, useLazyGetCbtServiceQuery } from '../dashboard.slice';
import Toastify from '../../../../../components/PopUp/Toastify';
import { useDispatch, useSelector } from 'react-redux';
import Table from '../../../../../components/Layout/Table';
import { MathsTableHeader } from './MathsTableHeader'; // Assume similar headers for other subjects
import PopUp from '../../../../../components/PopUp/PopUp';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';

const Cbt = () => {
  const [res, setRes] = useState(null);
  const [open, setOpen] = useState(false);
  const [score, setScore] = useState({});
  const [selectedSubject, setSelectedSubject] = useState('maths'); // Default to 'maths'

  const [getCbtDataService] = useLazyGetCbtServiceQuery();
  const dispatch = useDispatch();
  const cbtData = useSelector((state) => state.studentDashboard.cbt);

  // Fetch CBT data on component mount
  useEffect(() => {
    getCbtData();
  }, []);

  const getCbtData = async () => {
    try {
      const res = await getCbtDataService({ userId: sessionStorage.userId }).unwrap();
      dispatch(setCbt(res));
    } catch (error) {
      setRes(error);
    }
  };

  // Handle clicking "View" for a test
  const handleViewCbtData = (testData) => {
    setScore(testData);
    setOpen(true);
  };

  // Define table headers for each subject (you can customize these as needed)
  const tableHeaders = {
    maths: MathsTableHeader,
    physics: MathsTableHeader, // Replace with PhysicsTableHeader if defined
    chemistry: MathsTableHeader, // Replace with ChemistryTableHeader if defined
    biology: MathsTableHeader // Replace with BiologyTableHeader if defined
  };

  // Combine data from all subjects for table rendering
  const combinedData = cbtData
    ? Object.keys(cbtData).reduce((acc, subject) => {
        const subjectData = cbtData[subject].map((test) => ({
          ...test,
          subject_name: subject.charAt(0).toUpperCase() + subject.slice(1) // Capitalize subject name
        }));
        return [...acc, ...subjectData];
      }, [])
    : [];

  // Filter data based on selected subject
  const filteredData = selectedSubject
    ? combinedData.filter((test) => test.subject_name.toLowerCase() === selectedSubject)
    : combinedData;

  // Highcharts options for the score chart
  const chartOptions = {
    chart: {
      type: 'column'
    },
    title: {
      text: `${score.subject_name || 'Selected Subject'} CBT Score`
    },
    credits: {
      enabled: false
    },
    xAxis: {
      categories: ['Correct', 'Incorrect', 'Unattempted'],
      title: {
        text: 'Question Type'
      }
    },
    yAxis: {
      min: 0,
      title: {
        text: 'Number of Questions'
      },
      gridLineColor: '#444'
    },
    legend: {
      enabled: false
    },
    tooltip: {
      pointFormat: '<b>{point.y} questions</b>'
    },
    series: [
      {
        name: 'Questions',
        data: [
          {
            name: 'Correct',
            y: score?.evaluation_results?.score_summary?.num_correct || 0,
            color: '#4caf50'
          },
          {
            name: 'Incorrect',
            y: score?.evaluation_results?.score_summary?.num_incorrect || 0,
            color: '#f44336'
          },
          {
            name: 'Unattempted',
            y: score?.evaluation_results?.score_summary?.num_unattempted || 0,
            color: '#9e9e9e'
          }
        ]
      }
    ],
    plotOptions: {
      column: {
        borderRadius: 5,
        dataLabels: {
          enabled: true,
          style: {
            color: '#ffffff'
          }
        }
      }
    }
  };

  return (
    <div>
      {/* Subject Selection Dropdown */}
      <div style={{ marginBottom: '20px' }}>
        <label htmlFor="subject-select">Select Subject: </label>
        <select
          id="subject-select"
          value={selectedSubject}
          onChange={(e) => setSelectedSubject(e.target.value)}
          style={{ padding: '8px', fontSize: '16px' }}>
          <option value="">All Subjects</option>
          <option value="maths">Maths</option>
          <option value="physics">Physics</option>
          <option value="chemistry">Chemistry</option>
          <option value="biology">Biology</option>
        </select>
      </div>

      {/* Popup for displaying chart */}
      {open && (
        <PopUp
          title={score.unit_name}
          onClose={() => {
            setOpen(false);
            setScore({});
          }}
          width="lg">
          <HighchartsReact highcharts={Highcharts} options={chartOptions} />
        </PopUp>
      )}

      {/* Toastify for error handling */}
      <Toastify res={res} resClear={() => setRes(null)} />

      {/* Table displaying test data */}
      <Table
        header={tableHeaders[selectedSubject] || MathsTableHeader} // Use subject-specific header or default to Maths
        data={filteredData}
        title={
          selectedSubject
            ? `${selectedSubject.charAt(0).toUpperCase() + selectedSubject.slice(1)} Tests`
            : 'All Subject Tests'
        }
        onView={handleViewCbtData}
        searchBy={['exam_name', 'subject_name', 'unit_name', 'sub_topics']}
        searchPlaceholder="Search by Subject, Unit Name, or Sub Topic Name"
      />
    </div>
  );
};

export default Cbt;
