"use client"

import { useState, useEffect, useMemo } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { useDispatch } from "react-redux"
import {
  useDirectorListCenterServiceQuery,
  setListCenterData,
  useDirectorListCenterStudentsServiceQuery,
  useDirectorViewStudentDetailsServiceQuery,
} from "../addCenter/addCenter.Slice"
import Toastify from "../../../../components/PopUp/Toastify"
import {
  FiUsers,
  FiEye,
  FiMail,
  FiPhone,
  FiMapPin,
  FiUser,
  FiBook,
  FiCalendar,
  FiX,
  FiSearch,
  FiGrid,
  FiList,
} from "react-icons/fi"

const ListCenters = () => {
  const dispatch = useDispatch()
  const { data, error, isLoading } = useDirectorListCenterServiceQuery()
  const [res, setRes] = useState(null)
  const [selectedCenterCode, setSelectedCenterCode] = useState(null)
  const [selectedStudentId, setSelectedStudentId] = useState(null)
  const [showModal, setShowModal] = useState(false)
  const [sortBy, setSortBy] = useState("name")
  const [viewMode, setViewMode] = useState("grid") // grid or list
  const [searchTerm, setSearchTerm] = useState("")

  // Fetch students for the selected center
  const {
    data: centerStudents,
    error: centerStudentsError,
    isLoading: centerStudentsLoading,
  } = useDirectorListCenterStudentsServiceQuery(selectedCenterCode, {
    skip: !selectedCenterCode,
  })

  // Fetch individual student details
  const {
    data: studentDetails,
    error: studentDetailsError,
    isLoading: studentDetailsLoading,
  } = useDirectorViewStudentDetailsServiceQuery(selectedStudentId, {
    skip: !selectedStudentId,
  })

  // Dispatch centers to Redux on successful fetch
  useEffect(() => {
    if (data && !error) {
      dispatch(setListCenterData(data))
    }
  }, [data, error, dispatch])

  // Handle errors
  useEffect(() => {
    if (error) {
      const errorMessage = error.data?.message || "Failed to fetch centers"
      setRes({ status: "error", message: errorMessage })
    }
  }, [error])

  useEffect(() => {
    if (centerStudentsError) {
      const errorMessage = centerStudentsError.data?.message || "Failed to fetch students for this center"
      setRes({ status: "error", message: errorMessage })
    }
  }, [centerStudentsError])

  useEffect(() => {
    if (studentDetailsError) {
      const errorMessage = studentDetailsError.data?.message || "Failed to fetch student details"
      setRes({ status: "error", message: errorMessage })
    }
  }, [studentDetailsError])

  const centers = Array.isArray(data) ? data : []
  const students = Array.isArray(centerStudents) ? centerStudents : []

  // Filter and sort students
  const filteredAndSortedStudents = useMemo(() => {
    if (!students.length) return []

    let filtered = students
    if (searchTerm) {
      filtered = students.filter(
        (student) =>
          `${student.first_name} ${student.last_name}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
          student.course.toLowerCase().includes(searchTerm.toLowerCase()) ||
          student.email.toLowerCase().includes(searchTerm.toLowerCase()),
      )
    }

    const sorted = [...filtered]
    if (sortBy === "name") {
      return sorted.sort((a, b) => `${a.first_name} ${a.last_name}`.localeCompare(`${b.first_name} ${b.last_name}`))
    } else if (sortBy === "course") {
      return sorted.sort((a, b) => a.course.localeCompare(b.course))
    }
    return sorted
  }, [students, sortBy, searchTerm])

  const handleViewStudents = (centerCode) => {
    setSelectedCenterCode(centerCode)
    setSearchTerm("")
  }

  const handleViewStudentDetails = (studentId) => {
    setSelectedStudentId(studentId)
    setShowModal(true)
  }

  const closeModal = () => {
    setShowModal(false)
    setSelectedStudentId(null)
  }

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4, ease: "easeOut" },
    },
  }

  const cardVariants = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.3 },
    },
    hover: {
      scale: 1.02,
      boxShadow: "0px 10px 30px rgba(0, 0, 0, 0.1)",
      transition: { duration: 0.2 },
    },
  }

  const modalVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.3, ease: "easeOut" },
    },
    exit: {
      opacity: 0,
      scale: 0.8,
      transition: { duration: 0.2 },
    },
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 p-4 md:p-8">
      <Toastify res={res} resClear={() => setRes(null)} />

      <motion.div className="max-w-7xl mx-auto" variants={containerVariants} initial="hidden" animate="visible">
        {/* Header Section */}
        <motion.div className="text-center mb-8" variants={itemVariants}>
          <motion.div
            className="inline-flex items-center justify-center w-16 h-16 rounded-full mb-4"
            style={{
              background: `linear-gradient(135deg, var(--color-director), var(--color-director))`,
              boxShadow: "0px 10px 30px rgba(0, 0, 0, 0.1)",
            }}
            whileHover={{ scale: 1.1, rotate: 5 }}
          >
            <FiUsers className=" text-2xl"  style={{ color: 'white' }}/>
          </motion.div>
          <motion.h1 className="text-4xl font-bold mb-2" style={{ color: "var(--color-director)" }}>
            Registered Centers
          </motion.h1>
          <motion.p className="text-gray-600 text-lg">
            Manage and view all registered centers and their students
          </motion.p>
        </motion.div>

        {/* Centers Table Card */}
        <motion.div
          className="bg-white rounded-3xl shadow-2xl overflow-hidden mb-8"
          variants={itemVariants}
          style={{
            background: "linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)",
            border: "1px solid rgba(0, 0, 0, 0.05)",
          }}
        >
          {isLoading ? (
            <div className="p-12 text-center">
              <motion.div
                className="inline-block w-8 h-8 border-4 border-gray-300 border-t-[var(--color-director)] rounded-full"
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: "linear" }}
              />
              <p className="mt-4 text-gray-600 text-lg">Loading centers...</p>
            </div>
          ) : error ? (
            <div className="p-12 text-center">
              <div className="text-red-500 text-lg">Error: {error.data?.message || "Failed to fetch centers"}</div>
            </div>
          ) : centers.length === 0 ? (
            <div className="p-12 text-center">
              <FiUsers className="mx-auto text-6xl text-gray-300 mb-4" />
              <p className="text-gray-600 text-lg">No centers found</p>
            </div>
          ) : (
            <div className="overflow-hidden">
              {/* Table Header */}
              <div
                className="px-8 py-6 text-white"
                style={{
                  background: `linear-gradient(135deg, var(--color-director), var(--color-director)dd)`,
                }}
              >
                <h2 className="text-2xl text-[var(--color-director)] font-bold">Centers Overview</h2>
                <p className="text-black text-opacity-90 mt-1">
                  {centers.length} center{centers.length !== 1 ? "s" : ""} registered
                </p>
              </div>

              {/* Table Content */}
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 border-b border-gray-200">
                    <tr>
                      <th className="px-8 py-4 text-left text-sm font-semibold text-gray-700">Center Code</th>
                      <th className="px-8 py-4 text-left text-sm font-semibold text-gray-700">Name</th>
                      <th className="px-8 py-4 text-left text-sm font-semibold text-gray-700">Email</th>
                      <th className="px-8 py-4 text-left text-sm font-semibold text-gray-700">Phone</th>
                      <th className="px-8 py-4 text-left text-sm font-semibold text-gray-700">Action</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-100">
                    {centers.map((center, index) => (
                      <motion.tr
                        key={center.center_code}
                        className="hover:bg-gray-50 transition-all duration-300"
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        whileHover={{ backgroundColor: "#f9fafb" }}
                      >
                        <td className="px-8 py-6">
                          <div className="flex items-center">
                            
                            <span className="font-medium text-gray-900">{center.center_code}</span>
                          </div>
                        </td>
                        <td className="px-8 py-6 text-gray-900 font-medium">{center.name}</td>
                        <td className="px-4 py-6">
                          <div className="flex items-center text-gray-600">
                            <FiMail className="mr-2 text-sm" />
                            {center.email}
                          </div>
                        </td>
                        <td className="px-4 py-6">
                          <div className="flex items-center text-gray-600">
                            <FiPhone className="mr-2 text-sm" />
                            {center.phone}
                          </div>
                        </td>
                        <td className="px-8 py-6">
                          <motion.button
                            onClick={() => handleViewStudents(center.center_code)}
                            className="inline-flex items-center px-4 py-2 rounded-lg text-white font-medium transition-all duration-200"
                            style={{ backgroundColor: "var(--color-director)" }}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                          >
                            <FiEye className="mr-2" />
                            View Students
                          </motion.button>
                        </td>
                      </motion.tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </motion.div>

        {/* Students Section */}
        <AnimatePresence>
          {selectedCenterCode && (
            <motion.div
              className="bg-white rounded-3xl shadow-2xl overflow-hidden"
              variants={itemVariants}
              initial="hidden"
              animate="visible"
              exit="hidden"
              style={{
                background: "linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)",
                border: "1px solid rgba(0, 0, 0, 0.05)",
              }}
            >
              {/* Students Header */}
              <div
                className="px-8 py-6 text-white"
                style={{
                  background: `linear-gradient(135deg, var(--color-director), var(--color-director)dd)`,
                }}
              >
                <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                  <div>
                    <h3 className="text-2xl font-bold">Students in Center {selectedCenterCode}</h3>
                    <p className="text-white text-opacity-90 mt-1">
                      {filteredAndSortedStudents.length} student{filteredAndSortedStudents.length !== 1 ? "s" : ""}{" "}
                      found
                    </p>
                  </div>
                  <div className="flex items-center space-x-4 mt-4 md:mt-0">
                    {/* Search */}
                    <div className="relative">
                      <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white text-opacity-70" />
                      <input
                        type="text"
                        placeholder="Search students..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 pr-4 py-2 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg text-white placeholder-white placeholder-opacity-70 focus:outline-none focus:bg-opacity-30"
                      />
                    </div>
                    {/* Sort */}
                    <select
                      value={sortBy}
                      onChange={(e) => setSortBy(e.target.value)}
                      className="px-4 py-2 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg text-white focus:outline-none focus:bg-opacity-30"
                    >
                      <option value="name" className="text-gray-900">
                        Sort by Name
                      </option>
                      <option value="course" className="text-gray-900">
                        Sort by Course
                      </option>
                    </select>
                    {/* View Mode Toggle */}
                    <div className="flex bg-white bg-opacity-20 rounded-lg p-1">
                      <button
                        onClick={() => setViewMode("grid")}
                        className={`p-2 rounded ${viewMode === "grid" ? "bg-white bg-opacity-30" : ""}`}
                      >
                        <FiGrid className="text-white" />
                      </button>
                      <button
                        onClick={() => setViewMode("list")}
                        className={`p-2 rounded ${viewMode === "list" ? "bg-white bg-opacity-30" : ""}`}
                      >
                        <FiList className="text-white" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Students Content */}
              <div className="p-8">
                {centerStudentsLoading ? (
                  <div className="text-center py-12">
                    <motion.div
                      className="inline-block w-8 h-8 border-4 border-gray-300 border-t-[var(--color-director)] rounded-full"
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: "linear" }}
                    />
                    <p className="mt-4 text-gray-600 text-lg">Loading students...</p>
                  </div>
                ) : centerStudentsError ? (
                  <div className="text-center py-12">
                    <div className="text-red-500 text-lg">
                      Error: {centerStudentsError.data?.message || "Failed to fetch students"}
                    </div>
                  </div>
                ) : filteredAndSortedStudents.length === 0 ? (
                  <div className="text-center py-12">
                    <FiUser className="mx-auto text-6xl text-gray-300 mb-4" />
                    <p className="text-gray-600 text-lg">
                      {searchTerm ? "No students match your search" : "No students found for this center"}
                    </p>
                  </div>
                ) : (
                  <motion.div
                    className={
                      viewMode === "grid" ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" : "space-y-4"
                    }
                    variants={containerVariants}
                    initial="hidden"
                    animate="visible"
                  >
                    {filteredAndSortedStudents.map((student, index) => (
                      <motion.div
                        key={student.id}
                        className={`bg-gradient-to-br from-white to-gray-50 rounded-2xl shadow-lg border border-gray-100 overflow-hidden ${
                          viewMode === "list" ? "flex items-center" : ""
                        }`}
                        variants={cardVariants}
                        whileHover="hover"
                        initial="hidden"
                        animate="visible"
                        transition={{ delay: index * 0.1 }}
                      >
                        <div className={`p-6 ${viewMode === "list" ? "flex-1" : ""}`}>
                          <div className="flex items-center mb-4">
                            <div
                              className="w-12 h-12 rounded-full flex items-center justify-center text-white font-bold mr-4"
                              style={{ backgroundColor: "var(--color-director)" }}
                            >
                              {student.first_name.charAt(0)}
                              {student.last_name.charAt(0)}
                            </div>
                            <div>
                              <h4 className="font-bold text-gray-900 text-lg">
                                {student.first_name} {student.last_name}
                              </h4>
                              <p className="text-gray-600 text-sm flex items-center">
                                <FiBook className="mr-1" />
                                {student.course}
                              </p>
                            </div>
                          </div>
                          <div className="space-y-2 text-sm text-gray-600">
                            <p className="flex items-center">
                              <FiMail className="mr-2" />
                              {student.email}
                            </p>
                          </div>
                          <motion.button
                            onClick={() => handleViewStudentDetails(student.id)}
                            className="mt-4 w-full px-4 py-2 rounded-lg text-white font-medium transition-all duration-200"
                            style={{ backgroundColor: "var(--color-director)" }}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            View Details
                          </motion.button>
                        </div>
                      </motion.div>
                    ))}
                  </motion.div>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* Student Details Modal */}
      <AnimatePresence>
        {showModal && (
          <motion.div
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={closeModal}
          >
            <motion.div
              className="bg-white rounded-3xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto"
              variants={modalVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
              onClick={(e) => e.stopPropagation()}
              style={{
                background: "linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)",
              }}
            >
              {/* Modal Header */}
              <div
                className="px-8 py-6 text-white flex items-center justify-between"
                style={{
                  background: `linear-gradient(135deg, var(--color-director), var(--color-director)dd)`,
                }}
              >
                <div>
                  <h3 className="text-2xl font-bold">Student Details</h3>
                  <p className="text-white text-opacity-90 mt-1">Complete student information</p>
                </div>
                <motion.button
                  onClick={closeModal}
                  className="p-2 hover:bg-white hover:bg-opacity-20 rounded-full transition-colors"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <FiX className="text-2xl" />
                </motion.button>
              </div>

              {/* Modal Content */}
              <div className="p-8">
                {studentDetailsLoading ? (
                  <div className="text-center py-12">
                    <motion.div
                      className="inline-block w-8 h-8 border-4 border-gray-300 border-t-[var(--color-director)] rounded-full"
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: "linear" }}
                    />
                    <p className="mt-4 text-gray-600 text-lg">Loading student details...</p>
                  </div>
                ) : studentDetailsError ? (
                  <div className="text-center py-12">
                    <div className="text-red-500 text-lg">
                      Error: {studentDetailsError.data?.message || "Failed to fetch student details"}
                    </div>
                  </div>
                ) : studentDetails ? (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Student Information */}
                    <div className="bg-gradient-to-br from-white to-gray-50 rounded-2xl p-6 border border-gray-100">
                      <div className="flex items-center mb-6">
                        <div
                          className="w-16 h-16 rounded-full flex items-center justify-center text-white font-bold text-xl mr-4"
                          style={{ backgroundColor: "var(--color-director)" }}
                        >
                          {studentDetails.student?.first_name?.charAt(0)}
                          {studentDetails.student?.last_name?.charAt(0)}
                        </div>
                        <div>
                          <h4 className="text-xl font-bold text-gray-900">Student Information</h4>
                          <p className="text-gray-600">Personal and academic details</p>
                        </div>
                      </div>
                      <div className="space-y-4">
                        <div className="flex items-center">
                          <FiUser className="mr-3 text-gray-400" />
                          <div>
                            <p className="text-sm text-gray-600">Full Name</p>
                            <p className="font-medium text-gray-900">
                              {studentDetails.student?.first_name} {studentDetails.student?.last_name}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center">
                          <FiMail className="mr-3 text-gray-400" />
                          <div>
                            <p className="text-sm text-gray-600">Email</p>
                            <p className="font-medium text-gray-900">{studentDetails.student?.email}</p>
                          </div>
                        </div>
                        <div className="flex items-center">
                          <FiPhone className="mr-3 text-gray-400" />
                          <div>
                            <p className="text-sm text-gray-600">Phone</p>
                            <p className="font-medium text-gray-900">{studentDetails.student?.phone}</p>
                          </div>
                        </div>
                        <div className="flex items-center">
                          <FiBook className="mr-3 text-gray-400" />
                          <div>
                            <p className="text-sm text-gray-600">Course</p>
                            <p className="font-medium text-gray-900">{studentDetails.student?.course}</p>
                          </div>
                        </div>
                        <div className="flex items-center">
                          <FiCalendar className="mr-3 text-gray-400" />
                          <div>
                            <p className="text-sm text-gray-600">Date of Birth</p>
                            <p className="font-medium text-gray-900">{studentDetails.student?.dob}</p>
                          </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm text-gray-600">10th Marks</p>
                            <p className="font-medium text-gray-900">{studentDetails.student?.marks_10th}%</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">12th Marks</p>
                            <p className="font-medium text-gray-900">{studentDetails.student?.marks_12th}%</p>
                          </div>
                        </div>
                        <div className="flex items-center">
                          <FiMapPin className="mr-3 text-gray-400" />
                          <div>
                            <p className="text-sm text-gray-600">Center</p>
                            <p className="font-medium text-gray-900">{studentDetails.student?.center_name}</p>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Parent Information */}
                    {studentDetails.parent && (
                      <div className="bg-gradient-to-br from-white to-gray-50 rounded-2xl p-6 border border-gray-100">
                        <div className="flex items-center mb-6">
                          <div
                            className="w-16 h-16 rounded-full flex items-center justify-center text-white font-bold text-xl mr-4"
                            style={{ backgroundColor: "var(--color-director)" }}
                          >
                            {studentDetails.parent?.first_name?.charAt(0)}
                            {studentDetails.parent?.last_name?.charAt(0)}
                          </div>
                          <div>
                            <h4 className="text-xl font-bold text-gray-900">Parent Information</h4>
                            <p className="text-gray-600">Guardian contact details</p>
                          </div>
                        </div>
                        <div className="space-y-4">
                          <div className="flex items-center">
                            <FiUser className="mr-3 text-gray-400" />
                            <div>
                              <p className="text-sm text-gray-600">Full Name</p>
                              <p className="font-medium text-gray-900">
                                {studentDetails.parent.first_name} {studentDetails.parent.last_name}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center">
                            <FiMail className="mr-3 text-gray-400" />
                            <div>
                              <p className="text-sm text-gray-600">Email</p>
                              <p className="font-medium text-gray-900">{studentDetails.parent.email}</p>
                            </div>
                          </div>
                          <div className="flex items-center">
                            <FiPhone className="mr-3 text-gray-400" />
                            <div>
                              <p className="text-sm text-gray-600">Phone</p>
                              <p className="font-medium text-gray-900">{studentDetails.parent.phone}</p>
                            </div>
                          </div>
                          <div className="flex items-center">
                            <FiUsers className="mr-3 text-gray-400" />
                            <div>
                              <p className="text-sm text-gray-600">Relationship</p>
                              <p className="font-medium text-gray-900">{studentDetails.parent.relationship}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <FiUser className="mx-auto text-6xl text-gray-300 mb-4" />
                    <p className="text-gray-600 text-lg">No student details available</p>
                  </div>
                )}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default ListCenters
