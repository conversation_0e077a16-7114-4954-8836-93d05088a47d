'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  BookO<PERSON>,
  Clock,
  CheckCircle,
  ChevronLeft,
  ChevronRight,
  Play,
  Pause,
  Flag,
  Eye,
  EyeOff,
  AlertCircle,
  Trophy,
  Target
} from 'lucide-react';

const MockTestSimulation = () => {
  const [selectedExam, setSelectedExam] = useState('NEET');
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [timeLeft, setTimeLeft] = useState(3600); // 60 minutes
  const [isTestStarted, setIsTestStarted] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [answers, setAnswers] = useState({});
  const [flaggedQuestions, setFlaggedQuestions] = useState(new Set());
  const [showQuestionPalette, setShowQuestionPalette] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  // Sample questions for NEET and JEE
  const neetQuestions = [
    {
      id: 1,
      subject: 'Biology',
      question: 'Which of the following is a characteristic of prokaryotic cells?',
      options: [
        'Presence of nucleus',
        'Membrane-bound organelles',
        'Single circular DNA',
        'Complex cytoskeleton'
      ],
      correct: 2,
      difficulty: 'Medium'
    },
    {
      id: 2,
      subject: 'Chemistry',
      question: 'What is the hybridization of carbon in methane (CH4)?',
      options: ['sp', 'sp²', 'sp³', 'dsp²'],
      correct: 2,
      difficulty: 'Easy'
    },
    {
      id: 3,
      subject: 'Physics',
      question: 'What is the unit of electric field strength?',
      options: ['Newton/Coulomb', 'Joule/Coulomb', 'Volt/Meter', 'Both A and C'],
      correct: 3,
      difficulty: 'Medium'
    },
    {
      id: 4,
      subject: 'Biology',
      question: 'Which organelle is known as the powerhouse of the cell?',
      options: ['Nucleus', 'Mitochondria', 'Ribosome', 'Endoplasmic Reticulum'],
      correct: 1,
      difficulty: 'Easy'
    },
    {
      id: 5,
      subject: 'Chemistry',
      question: 'What is the molecular formula of glucose?',
      options: ['C6H12O6', 'C12H22O11', 'C6H6', 'CH4'],
      correct: 0,
      difficulty: 'Easy'
    }
  ];

  const jeeQuestions = [
    {
      id: 1,
      subject: 'Physics',
      question: 'A body moves with constant velocity. What is its acceleration?',
      options: ['Zero', 'Constant', 'Increasing', 'Decreasing'],
      correct: 0,
      difficulty: 'Easy'
    },
    {
      id: 2,
      subject: 'Chemistry',
      question: 'What is the oxidation state of chlorine in HClO₄?',
      options: ['+1', '+3', '+5', '+7'],
      correct: 3,
      difficulty: 'Medium'
    },
    {
      id: 3,
      subject: 'Mathematics',
      question: 'What is the derivative of sin(x) with respect to x?',
      options: ['cos(x)', '-sin(x)', '-cos(x)', 'sin(x)'],
      correct: 0,
      difficulty: 'Medium'
    },
    {
      id: 4,
      subject: 'Physics',
      question: "What is Newton's second law of motion?",
      options: ['F = ma', 'F = mv', 'F = m/a', 'F = a/m'],
      correct: 0,
      difficulty: 'Easy'
    },
    {
      id: 5,
      subject: 'Mathematics',
      question: 'What is the integral of x²?',
      options: ['x³/3 + C', '2x + C', 'x³ + C', '3x² + C'],
      correct: 0,
      difficulty: 'Medium'
    }
  ];

  const questions = selectedExam === 'NEET' ? neetQuestions : jeeQuestions;

  // Timer logic
  useEffect(() => {
    if (isTestStarted && !isPaused && timeLeft > 0 && !isSubmitted) {
      const timer = setInterval(() => {
        setTimeLeft((prev) => prev - 1);
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [isTestStarted, isPaused, timeLeft, isSubmitted]);

  const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours}:${minutes < 10 ? '0' : ''}${minutes}:${secs < 10 ? '0' : ''}${secs}`;
  };

  const handleStartTest = () => {
    setIsTestStarted(true);
    setCurrentQuestion(0);
    setTimeLeft(3600);
    setAnswers({});
    setFlaggedQuestions(new Set());
    setIsSubmitted(false);
  };

  const handleAnswerSelect = (optionIndex) => {
    setAnswers({ ...answers, [currentQuestion]: optionIndex });
  };

  const handleFlagQuestion = () => {
    const newFlagged = new Set(flaggedQuestions);
    if (newFlagged.has(currentQuestion)) {
      newFlagged.delete(currentQuestion);
    } else {
      newFlagged.add(currentQuestion);
    }
    setFlaggedQuestions(newFlagged);
  };

  const handleSubmitTest = () => {
    setIsSubmitted(true);
    setIsPaused(true);
  };

  const getQuestionStatus = (index) => {
    if (answers[index] !== undefined) return 'answered';
    if (flaggedQuestions.has(index)) return 'flagged';
    return 'unanswered';
  };

  const getAnsweredCount = () => Object.keys(answers).length;
  const getFlaggedCount = () => flaggedQuestions.size;
  const getUnansweredCount = () => questions.length - getAnsweredCount();

  const calculateScore = () => {
    let correct = 0;
    Object.entries(answers).forEach(([questionIndex, answerIndex]) => {
      if (questions[questionIndex].correct === answerIndex) {
        correct++;
      }
    });
    return {
      correct,
      total: questions.length,
      percentage: Math.round((correct / questions.length) * 100)
    };
  };

  if (isSubmitted) {
    const score = calculateScore();
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-6">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-white rounded-3xl p-8 max-w-2xl w-full text-center shadow-xl">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: 'spring' }}
            className="w-20 h-20 bg-student rounded-full flex items-center justify-center mx-auto mb-6">
            <Trophy className="text-white" size={40} />
          </motion.div>
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Test Completed!</h2>
          <div className="grid grid-cols-3 gap-4 mb-8">
            <div className="bg-green-50 p-4 rounded-xl">
              <div className="text-2xl font-bold text-green-600">{score.correct}</div>
              <div className="text-sm text-green-600">Correct</div>
            </div>
            <div className="bg-red-50 p-4 rounded-xl">
              <div className="text-2xl font-bold text-red-600">{score.total - score.correct}</div>
              <div className="text-sm text-red-600">Incorrect</div>
            </div>
            <div className="bg-student/10 p-4 rounded-xl">
              <div className="text-2xl font-bold text-student">{score.percentage}%</div>
              <div className="text-sm text-student">Score</div>
            </div>
          </div>
          <motion.button
            onClick={() => {
              setIsTestStarted(false);
              setIsSubmitted(false);
              setCurrentQuestion(0);
            }}
            className="bg-student text-white px-8 py-3 rounded-xl font-semibold hover:bg-student/90 transition-colors"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}>
            Take Another Test
          </motion.button>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {!isTestStarted ? (
        // Test Setup Screen
        <div className="min-h-screen flex items-center justify-center p-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-3xl p-8 max-w-2xl w-full shadow-xl">
            <div className="text-center mb-8">
              <motion.div
                className="w-16 h-16 bg-student rounded-2xl flex items-center justify-center mx-auto mb-4"
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.6 }}>
                <Target className="text-white" size={32} />
              </motion.div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Mock Test Simulation</h1>
              <p className="text-gray-600">Practice with real exam environment</p>
            </div>

            {/* Exam Selection */}
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Select Exam</h3>
              <div className="grid grid-cols-2 gap-4">
                {['NEET', 'JEE'].map((exam) => (
                  <motion.button
                    key={exam}
                    onClick={() => setSelectedExam(exam)}
                    className={`p-6 rounded-2xl border-2 transition-all ${
                      selectedExam === exam
                        ? 'border-student bg-student/5 text-student'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}>
                    <div className="text-xl font-bold">{exam}</div>
                    <div className="text-sm text-gray-600 mt-1">
                      {exam === 'NEET' ? 'Medical Entrance' : 'Engineering Entrance'}
                    </div>
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Test Info */}
            <div className="bg-gray-50 rounded-2xl p-6 mb-8">
              <h3 className="font-semibold text-gray-800 mb-4">Test Information</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Clock size={16} className="text-student" />
                  <span>Duration: 60 minutes</span>
                </div>
                <div className="flex items-center gap-2">
                  <BookOpen size={16} className="text-student" />
                  <span>Questions: {questions.length}</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle size={16} className="text-student" />
                  <span>Marks: +4 for correct</span>
                </div>
                <div className="flex items-center gap-2">
                  <AlertCircle size={16} className="text-red-500" />
                  <span>Negative: -1 for wrong</span>
                </div>
              </div>
            </div>

            <motion.button
              onClick={handleStartTest}
              className="w-full bg-student text-white py-4 rounded-2xl font-semibold text-lg flex items-center justify-center gap-3 hover:bg-student/90 transition-colors"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}>
              <Play size={20} />
              Start {selectedExam} Mock Test
            </motion.button>
          </motion.div>
        </div>
      ) : (
        // Test Interface
        <div className="flex h-screen ">
          {/* Question Palette Sidebar */}
          <AnimatePresence>
            {showQuestionPalette && (
              <motion.div
                initial={{ x: -300 }}
                animate={{ x: 0 }}
                exit={{ x: -300 }}
                className="w-80 bg-white border-r border-gray-200 p-6 overflow-y-auto">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="font-semibold text-gray-800">Question Palette</h3>
                  <button
                    onClick={() => setShowQuestionPalette(false)}
                    className="p-2 hover:bg-gray-100 rounded-lg">
                    <EyeOff size={16} />
                  </button>
                </div>

                {/* Status Summary */}
                <div className="grid grid-cols-3 gap-2 mb-6 text-xs">
                  <div className="bg-green-50 p-2 rounded-lg text-center">
                    <div className="font-semibold text-green-600">{getAnsweredCount()}</div>
                    <div className="text-green-600">Answered</div>
                  </div>
                  <div className="bg-yellow-50 p-2 rounded-lg text-center">
                    <div className="font-semibold text-yellow-600">{getFlaggedCount()}</div>
                    <div className="text-yellow-600">Flagged</div>
                  </div>
                  <div className="bg-gray-50 p-2 rounded-lg text-center">
                    <div className="font-semibold text-gray-600">{getUnansweredCount()}</div>
                    <div className="text-gray-600">Unanswered</div>
                  </div>
                </div>

                {/* Question Grid */}
                <div className="grid grid-cols-5 gap-2">
                  {questions.map((_, index) => {
                    const status = getQuestionStatus(index);
                    return (
                      <motion.button
                        key={index}
                        onClick={() => setCurrentQuestion(index)}
                        className={`w-10 h-10 rounded-lg font-semibold text-sm ${
                          currentQuestion === index
                            ? 'bg-student text-white'
                            : status === 'answered'
                              ? 'bg-green-100 text-green-600'
                              : status === 'flagged'
                                ? 'bg-yellow-100 text-yellow-600'
                                : 'bg-gray-100 text-gray-600'
                        }`}
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}>
                        {index + 1}
                      </motion.button>
                    );
                  })}
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Main Test Area */}
          <div className="flex-1 flex flex-col">
            {/* Header */}
            <div className="bg-[var(--color-student)] border-b rounded-2xl border-gray-200 p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <button
                    onClick={() => setShowQuestionPalette(!showQuestionPalette)}
                    className="p-2 text-white hover:bg-gray-100 rounded-lg">
                    {showQuestionPalette ? <EyeOff size={20} /> : <Eye size={20} />}
                  </button>
                  <div className="text-lg font-semibold text-white">
                    {selectedExam} Mock Test
                  </div>
                </div>

                <div className="flex items-center gap-6">
                  {/* Timer */}
                  <div className="flex items-center gap-2">
                    <Clock
                      className={`${timeLeft < 300 ? 'text-red-500' : 'text-white'}`}
                      size={20}
                    />
                    <span
                      className={`font-mono text-lg font-semibold ${timeLeft < 300 ? 'text-red-500' : 'text-white'}`}>
                      {formatTime(timeLeft)}
                    </span>
                  </div>


                  {/* Submit */}
                  <motion.button
                    onClick={handleSubmitTest}
                    className="bg-white text-blue-500 px-6 py-2 rounded-lg font-semibold hover:bg-student/90 transition-colors"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}>
                    Submit Test
                  </motion.button>
                </div>
              </div>

              {/* Progress Bar */}
              <div className="mt-4">
                <div className="flex justify-between text-sm text-white mb-2">
                  <span>
                    Question {currentQuestion + 1} of {questions.length}
                  </span>
                  <span>{getAnsweredCount()} answered</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <motion.div
                    className="bg-[var(--color-counselor)] h-2 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${((currentQuestion + 1) / questions.length) * 100}%` }}
                    transition={{ duration: 0.3 }}
                  />
                </div>
              </div>
            </div>

            {/* Question Content */}
            <div className="flex-1 p-8 ">
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentQuestion}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                  className="max-w-4xl mx-auto">
                  {/* Question Header */}
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center gap-3">
                      <span className="bg-student/10 text-student px-3 py-1 rounded-full text-sm font-medium">
                        {questions[currentQuestion].subject}
                      </span>
                      <span className="bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-sm">
                        {questions[currentQuestion].difficulty}
                      </span>
                    </div>
                    <motion.button
                      onClick={handleFlagQuestion}
                      className={`p-2 rounded-lg ${
                        flaggedQuestions.has(currentQuestion)
                          ? 'bg-yellow-100 text-yellow-600'
                          : 'hover:bg-gray-100'
                      }`}
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}>
                    
                    </motion.button>
                  </div>

                  {/* Question */}
                  <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100 mb-8">
                    <h2 className="text-xl font-semibold text-gray-900 mb-6 leading-relaxed">
                      {questions[currentQuestion].question}
                    </h2>

                    {/* Options */}
                    <div className="space-y-3">
                      {questions[currentQuestion].options.map((option, index) => (
                        <motion.button
                          key={index}
                          onClick={() => handleAnswerSelect(index)}
                          className={`w-full text-left p-4 rounded-xl border-2 transition-all ${
                            answers[currentQuestion] === index
                              ? 'border-student bg-student/5 text-student'
                              : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                          }`}
                          whileHover={{ scale: 1.01 }}
                          whileTap={{ scale: 0.99 }}>
                          <div className="flex items-center gap-3">
                            <div
                              className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                                answers[currentQuestion] === index
                                  ? 'border-student bg-student text-white'
                                  : 'border-gray-300'
                              }`}>
                              {answers[currentQuestion] === index && <CheckCircle size={16} />}
                            </div>
                            <span className="font-medium">
                              {String.fromCharCode(65 + index)}. {option}
                            </span>
                          </div>
                        </motion.button>
                      ))}
                    </div>
                  </div>

                  {/* Navigation */}
                  <div className="flex justify-between">
                    <motion.button
                      onClick={() => setCurrentQuestion(Math.max(0, currentQuestion - 1))}
                      disabled={currentQuestion === 0}
                      className="flex items-center gap-2 px-6 py-3 bg-gray-100 text-gray-600 rounded-xl font-semibold disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-200 transition-colors"
                      whileHover={{ scale: currentQuestion > 0 ? 1.02 : 1 }}
                      whileTap={{ scale: currentQuestion > 0 ? 0.98 : 1 }}>
                      <ChevronLeft size={20} />
                      Previous
                    </motion.button>

                    <motion.button
                      onClick={() =>
                        setCurrentQuestion(Math.min(questions.length - 1, currentQuestion + 1))
                      }
                      disabled={currentQuestion === questions.length - 1}
                      className="flex items-center gap-2 px-6 py-3 bg-student text-white rounded-xl font-semibold disabled:opacity-50 disabled:cursor-not-allowed hover:bg-student/90 transition-colors"
                      whileHover={{ scale: currentQuestion < questions.length - 1 ? 1.02 : 1 }}
                      whileTap={{ scale: currentQuestion < questions.length - 1 ? 0.98 : 1 }}>
                      Next
                      <ChevronRight size={20} />
                    </motion.button>
                  </div>
                </motion.div>
              </AnimatePresence>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MockTestSimulation;
