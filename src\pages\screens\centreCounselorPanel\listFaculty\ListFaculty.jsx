import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUserTie,
  faSearch,
  faSpinner,
  faExclamationTriangle,
  faCalendarAlt,
  faEnvelope,
  faPhone,
  faFilter
} from '@fortawesome/free-solid-svg-icons';
import { useLazyGetListFacultyQuery } from '../centreCounselorDashboard/centreCounselorDashboard.slice';

const ListFaculty = () => {
  const [getListFaculty, { data: facultyData, isLoading, error }] = useLazyGetListFacultyQuery();
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredFaculty, setFilteredFaculty] = useState([]);

  useEffect(() => {
    getListFaculty();
  }, [getListFaculty]);

  // Debug logging to see what data we're getting
  useEffect(() => {
    console.log('Faculty Data:', facultyData);
  }, [facultyData]);

  // Make sure we're accessing the data correctly
  const faculty = facultyData?.faculty || [];

  // Debug logging for faculty array
  useEffect(() => {
    console.log('Faculty Array:', faculty);
    console.log('Faculty Length:', faculty.length);
  }, [faculty]);

  // Update filtered faculty whenever search term or faculty data changes
  useEffect(() => {
    if (!faculty || faculty.length === 0) {
      setFilteredFaculty([]);
      return;
    }

    if (!searchTerm.trim()) {
      setFilteredFaculty(faculty);
      return;
    }

    const lowercasedSearch = searchTerm.toLowerCase();
    const results = faculty.filter(
      (member) =>
        `${member.first_name} ${member.last_name}`.toLowerCase().includes(lowercasedSearch) ||
        member.email?.toLowerCase().includes(lowercasedSearch) ||
        member.username?.toLowerCase().includes(lowercasedSearch) ||
        member.phone?.toLowerCase().includes(lowercasedSearch)
    );

    setFilteredFaculty(results);
  }, [searchTerm, faculty]);

  // Debug logging for filtered faculty
  useEffect(() => {
    console.log('Filtered Faculty:', filteredFaculty);
  }, [filteredFaculty]);

  // Animation variants



  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  // Clear search
  const clearSearch = () => {
    setSearchTerm('');
  };

  return (
    <motion.div
      className="bg-white rounded-xl shadow-lg"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}>
      {/* Header */}
      <div className="bg-gradient-to-r from-[var(--color-counselor)] to-amber-400 p-4 sm:p-6 rounded-t-xl">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <h2 className="text-xl sm:text-2xl font-bold text-white flex items-center">
            <FontAwesomeIcon icon={faUserTie} className="mr-3" />
            Faculty Members
          </h2>

          <div className="relative w-full md:w-auto">
            <input
              type="text"
              placeholder="Search by name, email..."
              value={searchTerm}
              onChange={handleSearchChange}
              className="pl-10 pr-10 py-2 rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-white w-full md:w-64"
            />
            <FontAwesomeIcon
              icon={faSearch}
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
            />
            {searchTerm && (
              <button
                onClick={clearSearch}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
                ✕
              </button>
            )}
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 mt-4">
          <div className="bg-white/20 backdrop-blur-sm rounded-lg p-3">
            <p className="text-white text-xs sm:text-sm font-medium">Total Faculty</p>
            <p className="text-white text-xl sm:text-2xl font-bold">{faculty?.length || 0}</p>
          </div>
          <div className="bg-white/20 backdrop-blur-sm rounded-lg p-3">
            <p className="text-white text-xs sm:text-sm font-medium">Search Results</p>
            <p className="text-white text-xl sm:text-2xl font-bold">
              {filteredFaculty?.length || 0}
            </p>
          </div>
          <div className="bg-white/20 backdrop-blur-sm rounded-lg p-3">
            <p className="text-white text-xs sm:text-sm font-medium">Recently Added</p>
            <p className="text-white text-xl sm:text-2xl font-bold">
              {faculty?.filter((member) => {
                if (!member?.created_at) return false;
                const joinDate = new Date(member.created_at);
                const thirtyDaysAgo = new Date();
                thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                return joinDate > thirtyDaysAgo;
              })?.length || 0}
            </p>
          </div>
        </div>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex flex-col items-center justify-center py-12">
          <FontAwesomeIcon
            icon={faSpinner}
            spin
            className="text-[var(--color-counselor)] text-3xl mb-3"
          />
          <p className="text-gray-500 text-base">Loading faculty members...</p>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="flex flex-col items-center justify-center py-12 px-4">
          <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-500 text-3xl mb-3" />
          <p className="text-red-500 text-base font-medium">Failed to load faculty members</p>
          <p className="text-gray-500 mt-2 text-center text-sm">
            {error.message || 'Please try again later or contact support'}
          </p>
        </div>
      )}

      {/* Debug Info - Remove in production */}

      {/* Faculty Table */}
      {!isLoading && !error && (
        <div className="p-4">
          {filteredFaculty && filteredFaculty.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr>
                    <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Faculty Name
                    </th>
                    <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Username
                    </th>
                    <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Contact Information
                    </th>
                    <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Joined Date
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredFaculty.map((member, index) => (
                    <tr key={member?.id || index} className="hover:bg-gray-50 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10 bg-[var(--color-counselor)] rounded-full flex items-center justify-center text-white font-bold">
                            {member?.first_name?.charAt(0) || '?'}
                            {member?.last_name?.charAt(0) || '?'}
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {member?.first_name || 'Unknown'} {member?.last_name || ''}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{member?.username || 'unknown'}</div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900 flex flex-col space-y-1">
                          <span className="flex items-center">
                            <FontAwesomeIcon icon={faEnvelope} className="text-gray-400 mr-2" />
                            {member?.email || 'No email provided'}
                          </span>
                          <span className="flex items-center">
                            <FontAwesomeIcon icon={faPhone} className="text-gray-400 mr-2" />
                            {member?.phone || 'No phone provided'}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 flex items-center">
                          <FontAwesomeIcon icon={faCalendarAlt} className="text-gray-400 mr-2" />
                          {member?.created_at
                            ? new Date(member.created_at).toLocaleDateString('en-US', {
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric'
                              })
                            : 'Unknown date'}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <motion.div
              className="text-center py-12 px-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}>
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 mb-4">
                <FontAwesomeIcon icon={faFilter} className="text-gray-400 text-xl" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {faculty && faculty.length > 0
                  ? 'No matching faculty members'
                  : 'No Faculty Members Yet'}
              </h3>
              <p className="text-gray-500 max-w-md mx-auto">
                {faculty && faculty.length > 0
                  ? `No faculty members match your search for "${searchTerm}". Try a different search term.`
                  : 'There are no faculty members registered in the system. Add faculty members to see them listed here.'}
              </p>
              {faculty && faculty.length > 0 && searchTerm && (
                <button
                  onClick={clearSearch}
                  className="mt-4 px-4 py-2 bg-[var(--color-counselor)] text-white rounded-md hover:bg-amber-500 transition-colors">
                  Clear Search
                </button>
              )}
            </motion.div>
          )}
        </div>
      )}
    </motion.div>
  );
};

export default ListFaculty;
