import React from 'react';
import { InlineMath } from 'react-katex';

// This component splits text by math delimiters and renders math parts with KaTeX
const MathRenderer = ({ content }) => {
  if (!content) return null;

  // Regex to split the string by \(...\) delimiters, keeping the delimiters
  const parts = content.split(/(\\\(.*?\))/);

  return (
    <span>
      {parts.map((part, index) => {
        // Check if the part is a math expression
        if (part.startsWith('\\(') && part.endsWith('\\)')) {
          // Extract the math content and replace double backslashes for KaTeX
          const math = part.substring(2, part.length - 2).replace(/\\\\/g, '\\');
          return <InlineMath key={index} math={math} />;
        }
        // Otherwise, it's just plain text
        return part;
      })}
    </span>
  );
};

export default MathRenderer;