import { useState } from 'react';
import PerformanceBasedDashboard from './PerformanceBasedDashboard';
import TestPerformanceDashboard from './TestPerformanceDashboard';
import TestBreakdownDashboard from './TestBreakdownDashboard';
import TimeAnalysisDashboard from './TimeAnalysisDashboard';
import OnboardAssessmentDashboard from './OnboardAssessment/OnboardAssessmentDashboard';
import { AnimatePresence, motion } from 'framer-motion';
import Cbt from './Cbt/Cbt';

const tabItems = [
  { value: 'performance', label: 'Performance Based', shortLabel: 'Performance' },
  { value: 'test-performance', label: 'Test Performance', shortLabel: 'Test Perf' },
  { value: 'test-breakdown', label: 'Test Breakdown', shortLabel: 'Breakdown' },
  { value: 'time-analysis', label: 'Time Analysis', shortLabel: 'Time' },
  { value: 'onboarding-assessment', label: 'Onboarding Assessment', shortLabel: 'Onboarding' },
  { value: 'cbt', label: 'CBT', shortLabel: 'Cbt' },
];

const dashboards = {
  performance: <PerformanceBasedDashboard />,
  'test-performance': <TestPerformanceDashboard />,
  'test-breakdown': <TestBreakdownDashboard />,
  'time-analysis': <TimeAnalysisDashboard />,
  'onboarding-assessment': <OnboardAssessmentDashboard />,
  'cbt':<Cbt />
};

const NewDashboard = () => {
  const [activeTab, setActiveTab] = useState('performance');

  const handleTabChange = (event, value) => {
    if (!event || event.type === 'click' || event.key === 'Enter' || event.key === ' ') {
      event?.preventDefault?.();
      setActiveTab(value);
    }
  };

  return (
    <div className="container mx-auto">
      <div className="mb-4">
        <h1 className="text-3xl font-bold tracking-tight">Dashboard Overview</h1>
        <p className="text-gray-600 mt-2">
          Monitor and analyze your performance metrics across different views
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="flex items-center overflow-auto bg-gray-100 p-1 rounded-lg shadow-sm mb-4">
        {tabItems.map((tab) => (
          <button
            key={tab.value}
            onClick={(e) => handleTabChange(e, tab.value)}
            onKeyDown={(e) => handleTabChange(e, tab.value)}
            className={`py-2 px-3 text-sm font-medium rounded-md transition
              ${
                activeTab === tab.value
                  ? 'bg-white text-gray-900 shadow-md'
                  : 'text-gray-600 hover:bg-gray-300 hover:text-gray-900'
              }
            `}>
            <span className="hidden sm:inline">{tab.label}</span>
            <span className="sm:hidden">{tab.shortLabel}</span>
          </button>
        ))}
      </div>

      {/* Dashboard Content */}
      <div className="relative">
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.3 }}>
            {dashboards[activeTab]}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default NewDashboard;
