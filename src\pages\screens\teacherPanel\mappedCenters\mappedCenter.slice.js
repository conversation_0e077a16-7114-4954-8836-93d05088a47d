import { teacherDashboardApi } from '../../../../redux/api/api';
import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  kotaTeachersData: null,
};

export const kotaTeachersSlice = teacherDashboardApi.injectEndpoints({
  endpoints: (builder) => ({
    getKotaTeachers: builder.query({
      query: () => '/teacher-dashboard',
      transformResponse: (response) => {
        console.log('Kota Teachers Data:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data,
      }),
      providesTags: ['TeacherDashboard'],
    }),
  }),
}); 

const KotaTeacherSlice = createSlice({
  name: 'kotaTeacher',
  initialState,
  reducers: {
    setKotaTeacherData(state, action) {
      state.kotaTeachersData = action.payload;
    },
  },
});

// Export both hooks
export const {  useLazyGetKotaTeachersQuery } = kotaTeachersSlice;

export const { setKotaTeacherData } = KotaTeacherSlice.actions;
export default KotaTeacherSlice.reducer;