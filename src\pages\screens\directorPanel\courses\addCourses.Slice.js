import { createSlice } from '@reduxjs/toolkit';
import { directorApi } from '../../../../redux/api/api';

const initialState = {
  courseData: null,
  courseListData: null,
};

export const createCoursesSlice = directorApi.injectEndpoints({
  endpoints: (builder) => ({
    createCoursesService: builder.mutation({
      query: (body) => ({
        url: '/add-course',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json(),
      }),
      transformResponse: (response) => {
        console.log('Create Courses Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => {
        console.log('Create Courses Error:', { originalStatus, status, data });
        return {
          status: originalStatus ?? status ?? 'UNKNOWN_ERROR',
          message: data?.message || data?.error || 'Failed to create course',
        };
      },
      invalidatesTags: ['Courses'],
    }),
    updateCoursesService: builder.mutation({
      query: ({ course_id, course_name, description }) => ({
        url: '/update-course',
        method: 'PUT',
        body: {
          course_id: course_id,
          course_name: course_name,
          description: description,
        },
        responseHandler: async (res) => res.json(),
      }),
      transformResponse: (response) => {
        console.log('Update Courses Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => {
        console.log('Update Courses Error:', { originalStatus, status, data });
        return {
          status: originalStatus ?? status ?? 'UNKNOWN_ERROR',
          message: data?.message || data?.error || 'Failed to update course',
        };
      },
      invalidatesTags: ['Courses'],
    }),
    deleteCoursesService: builder.mutation({
      query: ({ course_id }) => ({
        url: '/delete-course',
        method: 'DELETE',
        body: { course_id: course_id },
        responseHandler: async (res) => res.json(),
      }),
      transformResponse: (response) => {
        console.log('Delete Courses Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => {
        console.log('Delete Courses Error:', { originalStatus, status, data });
        return {
          status: originalStatus ?? status ?? 'UNKNOWN_ERROR',
          message: data?.message || data?.error || 'Failed to delete course',
        };
      },
      invalidatesTags: ['Courses'],
    }),
    listCoursesService: builder.query({
      query: () => ({
        url: '/list-courses',
        method: 'GET',
        responseHandler: async (res) => res.json(),
      }),
      transformResponse: (response) => {
        console.log('Raw List Courses Response:', response);
        const courses = Array.isArray(response.courses)
          ? response.courses
          : Array.isArray(response)
            ? response
            : [];
        const transformedCourses = courses.map((course) => ({
          course_id: course.id || course._id || course.courseId,
          course_name: course.name || course.course_name,
          description: course.desc || course.description || '',
        }));
        console.log('Transformed Courses:', transformedCourses);
        return transformedCourses;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => {
        console.log('List Courses Error:', { originalStatus, status, data });
        return {
          status: originalStatus ?? status ?? 'UNKNOWN_ERROR',
          message: data?.message || data?.error || 'Failed to fetch courses',
        };
      },
      providesTags: ['Courses'],
    }),
  }),
});

const coursesSlice = createSlice({
  name: 'courses',
  initialState,
  reducers: {
    setCourseData: (state, action) => {
      state.courseData = action.payload;
    },
    setCourseListData: (state, action) => {
      state.courseListData = action.payload;
    },
  },
});

export const { setCourseData, setCourseListData } = coursesSlice.actions;
export default coursesSlice.reducer;

export const {
  useCreateCoursesServiceMutation,
  useUpdateCoursesServiceMutation,
  useDeleteCoursesServiceMutation,
  useListCoursesServiceQuery,
} = createCoursesSlice;