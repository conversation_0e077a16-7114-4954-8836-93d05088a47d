import { useEffect, useState, useRef } from "react"
import { Room, RoomEvent } from "livekit-client"
import { motion, AnimatePresence } from "framer-motion"
import { toast, ToastContainer } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.css'
import {
  useLazyGetCenterLiveViewerQuery,
  useJoinLiveStreamMutation,
  useSendChatMessageMutation,
  useLazyGetChatHistoryQuery,
} from "./centerTraineeLive.slice"
import LiveQuiz from "./LiveQuiz"

const CenterTraineeLiveViewer = () => {
  const [trigger, { data, error, isLoading }] = useLazyGetCenterLiveViewerQuery()
  const [joinStream, { isLoading: isJoining }] = useJoinLiveStreamMutation()
  const [sendChatMessage, { isLoading: isSendingMessage }] = useSendChatMessageMutation()
  const [getChatHistory, { data: chatHistoryData, isLoading: isLoadingHistory }] = useLazyGetChatHistoryQuery()

  // Stream viewing states
  const [isViewingStream, setIsViewingStream] = useState(false)
  const [currentStream, setCurrentStream] = useState(null)
  const [livekitRoom, setLivekitRoom] = useState(null)
  const [livekitConnected, setLivekitConnected] = useState(false)
  const [participants, setParticipants] = useState([])
  const [remoteVideoTracks, setRemoteVideoTracks] = useState([])
  const [remoteAudioTracks, setRemoteAudioTracks] = useState([])
  const [connectionStatus, setConnectionStatus] = useState("")
  const [hasScreenShare, setHasScreenShare] = useState(false)
  const [hasCameraTrack, setHasCameraTrack] = useState(false)

  // Refs for video elements
  const mainVideoRef = useRef(null)
  const screenVideoRef = useRef(null)
  const cameraVideoRef = useRef(null)
  const cameraMainRef = useRef(null)

  // Translation states (Simplified - HTTP POST only)
  const [isTranslationEnabled, setIsTranslationEnabled] = useState(false)
  const [sourceLanguage, setSourceLanguage] = useState("en")
  const [targetLanguage, setTargetLanguage] = useState("ta")
  const [translationStatus, setTranslationStatus] = useState("")
  const [currentTranscription, setCurrentTranscription] = useState("")
  const [currentTranslation, setCurrentTranslation] = useState("")
  const [isPlayingTranslatedAudio, setIsPlayingTranslatedAudio] = useState(false)
  const [audioQueueLength, setAudioQueueLength] = useState(0)

  // LiveKit text stream states
  const [isReceivingTranscription, setIsReceivingTranscription] = useState(false)

  // Translation audio refs
  const audioContextRef = useRef(null)

  // Audio Queue System refs and state
  const audioQueueRef = useRef([])
  const currentAudioSourceRef = useRef(null)
  const currentHtmlAudioRef = useRef(null)
  const isProcessingQueueRef = useRef(false)

  // Chat states
  const [chatMessages, setChatMessages] = useState([])
  const [newMessage, setNewMessage] = useState("")
  const [isChatOpen, setIsChatOpen] = useState(false)
  const [unreadMessages, setUnreadMessages] = useState(0)
  const [activeSidebarTab, setActiveSidebarTab] = useState("info") // 'info', 'features', 'participants', 'connection', 'translation'

  // Quiz states
  const [receivedQuizId, setReceivedQuizId] = useState(null)
  const [showQuizNotification, setShowQuizNotification] = useState(false)
  const [isQuizOpen, setIsQuizOpen] = useState(false)

  // Available languages
  const availableLanguages = [
    { code: "en", name: "English", flag: "🇺🇸" },
    { code: "ta", name: "Tamil", flag: "🇮🇳" },
    { code: "hi", name: "Hindi", flag: "🇮🇳" },
    { code: "te", name: "Telugu", flag: "🇮🇳" },
    { code: "kn", name: "Kannada", flag: "🇮🇳" },
  ]

  // Mute/unmute original audio based on translation state
  useEffect(() => {
    remoteAudioTracks.forEach(({ audioElement }) => {
      if (audioElement) {
        audioElement.muted = isTranslationEnabled
      }
    })
  }, [isTranslationEnabled, remoteAudioTracks])

  useEffect(() => {
    trigger()
    const interval = setInterval(() => trigger(), 30000)
    return () => clearInterval(interval)
  }, [trigger])

  useEffect(() => {
    return () => {
      if (livekitRoom) {
        livekitRoom.disconnect()
        setLivekitRoom(null)
      }
      cleanupTranslation()
    }
  }, [livekitRoom])

  // HTTP-based chat system
  useEffect(() => {
    if (isViewingStream && currentStream) {
      console.log("💬 VIEWER: Starting HTTP-based chat for session:", currentStream.session_id)
      setChatMessages([])
      loadChatHistory()
      const pollInterval = setInterval(() => loadChatHistory(), 2000)
      return () => clearInterval(pollInterval)
    }
  }, [isViewingStream, currentStream])

  useEffect(() => {
    if (
      isViewingStream &&
      currentStream &&
      !isTranslationEnabled // Only start if not already enabled
    ) {
      console.log('🎯 [HTTP Translation] Auto-starting translation for new stream')
      startTranslation();
    }
  }, [isViewingStream, currentStream]);

  const loadChatHistory = async () => {
    if (!currentStream?.session_id) return
    try {
      const response = await getChatHistory(currentStream.session_id).unwrap()
      const newMessages = response.messages || []
      console.log("Chat history loaded:", newMessages.length, "messages")

      setChatMessages((prev) => {
        if (newMessages.length !== prev.length) {
          const newCount = newMessages.length - prev.length
          console.log("New messages detected:", newCount)

          if (newCount > 0 && !isChatOpen) {
            setUnreadMessages((prev) => prev + newCount)
          }

          // Check for new quiz messages
          const latestMessages = newMessages.slice(-newCount)
          console.log("Checking latest messages for quiz:", latestMessages)

          latestMessages.forEach(message => {
            console.log("Checking message:", message.message)
            // Check if message contains quiz start pattern
            if (message.message && message.message.includes('QUIZ_START:')) {
              console.log("Quiz message found:", message.message)
              const quizMatch = message.message.match(/QUIZ_START:([a-zA-Z0-9]+)/)
              if (quizMatch && quizMatch[1]) {
                const quizId = quizMatch[1]
                console.log("Quiz detected! Quiz ID:", quizId)
                setReceivedQuizId(quizId)
                setShowQuizNotification(true)
                toast.success('🎯 Quiz Started! Click "Open Quiz" to participate.', {
                  position: "top-right",
                  autoClose: 5000,
                  hideProgressBar: false,
                  closeOnClick: true,
                  pauseOnHover: true,
                  draggable: true,
                })
              }
            }
          })

          return newMessages
        }
        return prev
      })
    } catch (error) {
      console.log("❌ VIEWER: Failed to load chat history:", error.message)
    }
  }

  // Audio Queue Management
  const addToAudioQueue = (audioData) => {
    audioQueueRef.current.push(audioData)
    setAudioQueueLength(audioQueueRef.current.length)
    processAudioQueue()
  }

  const processAudioQueue = async () => {
    if (isProcessingQueueRef.current || audioQueueRef.current.length === 0) return
    isProcessingQueueRef.current = true
    while (audioQueueRef.current.length > 0) {
      const audioData = audioQueueRef.current.shift()
      setAudioQueueLength(audioQueueRef.current.length)
      await playAudioFromQueue(audioData)
    }
    isProcessingQueueRef.current = false
    setAudioQueueLength(0)
  }

  const stopCurrentAudio = () => {
    if (currentAudioSourceRef.current) {
      try {
        currentAudioSourceRef.current.stop()
        currentAudioSourceRef.current.disconnect()
      } catch (error) {
        console.warn("⚠️ Error stopping Web Audio source:", error)
      }
      currentAudioSourceRef.current = null
    }
    if (currentHtmlAudioRef.current) {
      try {
        currentHtmlAudioRef.current.pause()
        currentHtmlAudioRef.current.currentTime = 0
        if (currentHtmlAudioRef.current.src) {
          URL.revokeObjectURL(currentHtmlAudioRef.current.src)
        }
      } catch (error) {
        console.warn("⚠️ Error stopping HTML5 Audio:", error)
      }
      currentHtmlAudioRef.current = null
    }
    setIsPlayingTranslatedAudio(false)
  }

  const clearAudioQueue = () => {
    audioQueueRef.current = []
    setAudioQueueLength(0)
    stopCurrentAudio()
    isProcessingQueueRef.current = false
  }

  const cleanupTranslation = () => {
    if (audioContextRef.current && audioContextRef.current.state !== "closed") {
      audioContextRef.current.close()
      audioContextRef.current = null
    }
    clearAudioQueue()
    setIsTranslationEnabled(false)
    setTranslationStatus("")
    setCurrentTranscription("")
    setCurrentTranslation("")
  }

  useEffect(() => {
    if (remoteVideoTracks.length === 0) return
    remoteVideoTracks.forEach(({ track, publication }) => {
      if (publication.source === "screen_share" || publication.source === "screen_share_audio") {
        if (screenVideoRef.current && !track.attachedElements.includes(screenVideoRef.current)) {
          track.attach(screenVideoRef.current)
        }
      } else if (publication.source === "camera" || publication.trackName === "teacher_camera" || !publication.source) {
        if (hasScreenShare) {
          if (cameraVideoRef.current && !track.attachedElements.includes(cameraVideoRef.current)) {
            track.attach(cameraVideoRef.current)
          }
        } else {
          if (cameraMainRef.current && !track.attachedElements.includes(cameraMainRef.current)) {
            track.attach(cameraMainRef.current)
          }
        }
      }
    })
  }, [remoteVideoTracks, hasScreenShare, hasCameraTrack])

  const connectToLiveKitRoom = async (token, url) => {
    try {
      setConnectionStatus("Connecting...")
      const room = new Room()
      room.on(RoomEvent.Connected, () => {
        setLivekitConnected(true)
        setConnectionStatus("Connected")

        // Check for existing participants and their text streams
        console.log("🔗 Room connected, checking for existing participants...")
        room.remoteParticipants.forEach((participant) => {
          console.log(`📝 Checking participant ${participant.identity} for text streams...`)

          // Check for existing text streams
          if (participant.textStreams && participant.textStreams.size > 0) {
            participant.textStreams.forEach((stream) => {
              console.log(`📝 Found text stream: ${stream.info.id} with topic: ${stream.info.topic}`)
              if (stream.info.topic === 'transcription') {
                console.log("📝 ✅ Found existing transcription stream!")
                setIsReceivingTranscription(true)
                const processExistingStream = async () => {
                  try {
                    for await (const chunk of stream) {
                      console.log(`📝 ✅ Existing stream chunk: "${chunk}"`)
                      setCurrentTranscription(chunk)
                      if (isTranslationEnabled && chunk.trim()) {
                        await sendTextForTranslation(chunk.trim())
                      }
                    }
                  } catch (error) {
                    console.error("❌ Error processing existing stream:", error)
                  } finally {
                    setIsReceivingTranscription(false)
                  }
                }
                processExistingStream()
              }
            })
          }
        })
      })
      room.on(RoomEvent.Disconnected, () => {
        setLivekitConnected(false)
        setConnectionStatus("Disconnected")
        setIsViewingStream(false)
      })
      room.on(RoomEvent.ParticipantConnected, (participant) => {
        console.log("📥 Participant connected:", participant.identity)
        setParticipants((prev) => [...prev, participant])

        // Check if this participant has any text streams
        console.log("📝 Checking for existing text streams from", participant.identity)
        if (participant.textStreams && participant.textStreams.size > 0) {
          participant.textStreams.forEach((stream) => {
            console.log(`📝 Found existing text stream: ${stream.info.id} with topic: ${stream.info.topic}`)
            if (stream.info.topic === 'transcription') {
              console.log("📝 ✅ Found transcription stream, setting up reader...")
              setIsReceivingTranscription(true)
              const processExistingStream = async () => {
                try {
                  for await (const chunk of stream) {
                    console.log(`📝 ✅ Chunk from existing stream: "${chunk}"`)
                    setCurrentTranscription(chunk)
                    if (isTranslationEnabled && chunk.trim()) {
                      await sendTextForTranslation(chunk.trim())
                    }
                  }
                } catch (error) {
                  console.error("❌ Error processing existing stream:", error)
                } finally {
                  setIsReceivingTranscription(false)
                }
              }
              processExistingStream()
            }
          })
        }
      })
      room.on(RoomEvent.ParticipantDisconnected, (participant) => {
        console.log("📤 Participant disconnected:", participant.identity)
        setParticipants((prev) => prev.filter((p) => p.identity !== participant.identity))
        // Reset transcription state when teacher disconnects
        if (participant.identity.includes('teacher') || participant.identity.includes('Teacher')) {
          setCurrentTranscription("")
          setIsReceivingTranscription(false)
        }
      })
      room.on(RoomEvent.TrackSubscribed, (track, publication, participant) => {
        if (track.kind === "video") {
          setRemoteVideoTracks((prev) => {
            const newTracks = [...prev, { track, participant, publication }]
            setHasScreenShare(
              newTracks.some(
                (t) => t.publication.source === "screen_share" || t.publication.source === "screen_share_audio",
              ),
            )
            setHasCameraTrack(
              newTracks.some(
                (t) =>
                  t.publication.source === "camera" ||
                  t.publication.trackName === "teacher_camera" ||
                  !t.publication.source,
              ),
            )
            return newTracks
          })
          if (publication.source === "screen_share" || publication.source === "screen_share_audio") {
            if (screenVideoRef.current) track.attach(screenVideoRef.current)
          } else if (
            publication.source === "camera" ||
            publication.trackName === "teacher_camera" ||
            !publication.source
          ) {
            if (cameraVideoRef.current) track.attach(cameraVideoRef.current)
            if (
              !remoteVideoTracks.some(
                (t) => t.publication.source === "screen_share" || t.publication.source === "screen_share_audio",
              ) &&
              cameraMainRef.current
            ) {
              track.attach(cameraMainRef.current)
            }
          }
        } else if (track.kind === "audio") {
          const audioElement = track.attach()
          setRemoteAudioTracks((prev) => [...prev, { track, participant, publication, audioElement }])
        }
      })
      room.on(RoomEvent.TrackUnsubscribed, (track, publication, participant) => {
        if (track.kind === "video") {
          setRemoteVideoTracks((prev) => {
            const newTracks = prev.filter((t) => t.track !== track)
            setHasScreenShare(
              newTracks.some(
                (t) => t.publication.source === "screen_share" || t.publication.source === "screen_share_audio",
              ),
            )
            setHasCameraTrack(
              newTracks.some(
                (t) =>
                  t.publication.source === "camera" ||
                  t.publication.trackName === "teacher_camera" ||
                  !t.publication.source,
              ),
            )
            return newTracks
          })
          track.detach()
        } else if (track.kind === "audio") {
          setRemoteAudioTracks((prev) => {
            const trackInfo = prev.find((t) => t.track === track)
            if (trackInfo && trackInfo.audioElement) {
              track.detach(trackInfo.audioElement)
            }
            return prev.filter((t) => t.track !== track)
          })
        }
      })

      // CRITICAL: Register text stream handler BEFORE connecting
      console.log("🔧 Registering LiveKit text stream handler for transcriptions...")
      room.registerTextStreamHandler('transcription', async (reader, participantInfo) => {
        console.log(`📝 ✅ RECEIVED transcription stream from ${participantInfo.identity}`)
        setIsReceivingTranscription(true)
        try {
          console.log("📝 [Viewer] Starting to process transcription stream with async iterator...")
          for await (const chunk of reader) {
            console.log(`📝 [Viewer] CHUNK RECEIVED: \"${chunk}\"`)
            setCurrentTranscription(chunk)
            console.log('[DEBUG] Translation enabled:', isTranslationEnabled)

            // If translation is enabled, send immediately for translation
            if (isTranslationEnabled && chunk.trim()) {
              console.log(`📤 [Viewer] Sending for translation: \"${chunk.trim()}\"`)
              await sendTextForTranslation(chunk.trim())
            }
          }
        } catch (error) {
          console.error("❌ [Viewer] Error processing transcription stream:", error)
        } finally {
          setIsReceivingTranscription(false)
        }
      })

      // Listen for text stream publications with enhanced handling
      room.on(RoomEvent.TextStreamPublished, (stream, participant) => {
        console.log(`📝 ✅ Text stream published by ${participant.identity}:`, stream.info)
        if (stream.info.topic === 'transcription') {
          console.log("📝 ✅ Transcription stream detected, setting up reader...")
          setIsReceivingTranscription(true)

          // Multiple approaches for published streams too
          const processPublishedStream = async () => {
            try {
              console.log("📝 Processing published stream with async iterator...")
              for await (const chunk of stream) {
                console.log(`📝 ✅ PUBLISHED STREAM CHUNK: "${chunk}"`)
                setCurrentTranscription(chunk)
                if (isTranslationEnabled && chunk.trim()) {
                  await sendTextForTranslation(chunk.trim())
                }
              }
            } catch (error) {
              console.error("❌ Error processing published stream:", error)
            } finally {
              setIsReceivingTranscription(false)
            }
          }

          // Also try event-based approach for published streams
          if (stream.on) {
            stream.on('data', (chunk) => {
              console.log(`📝 ✅ PUBLISHED STREAM EVENT: "${chunk}"`)
              setCurrentTranscription(chunk)
              if (isTranslationEnabled && chunk.trim()) {
                sendTextForTranslation(chunk.trim())
              }
            })
          }

          processPublishedStream()
        }
      })

      // ENHANCED data channel handler as fallback
      room.on(RoomEvent.DataReceived, (payload, participant) => {
        console.log(`📡 ✅ Data received from ${participant?.identity}:`, payload)
        console.log(`📡 Payload type:`, typeof payload)
        console.log(`📡 Payload length:`, payload?.length || 'N/A')

        try {
          const data = JSON.parse(new TextDecoder().decode(payload))
          console.log(`📡 Decoded data:`, data)

          if (data.type === 'transcription') {
            console.log(`📝 ✅ FALLBACK TRANSCRIPTION RECEIVED: "${data.text}"`)
            setCurrentTranscription(data.text)
            setIsReceivingTranscription(true)

            if (isTranslationEnabled && data.text.trim()) {
              console.log(`📤 Sending fallback text for translation: "${data.text.trim()}"`)
              sendTextForTranslation(data.text.trim())
            }

            // Reset receiving status after a delay
            setTimeout(() => setIsReceivingTranscription(false), 2000)
          }
        } catch (e) {
          console.log("📡 Non-JSON data received:", e.message)
          // Try to decode as plain text
          try {
            const text = new TextDecoder().decode(payload)
            console.log(`📡 Plain text received: "${text}"`)
            if (text && text.length > 0) {
              setCurrentTranscription(text)
              setIsReceivingTranscription(true)
              setTimeout(() => setIsReceivingTranscription(false), 2000)
            }
          } catch (textError) {
            console.log("📡 Could not decode as text either")
          }
        }
      })

      console.log("🔗 Connecting to LiveKit room...")
      await room.connect(url, token)
      setLivekitRoom(room)
    } catch (err) {
      setConnectionStatus("Connection failed: " + err.message)
      alert("Failed to connect to stream: " + err.message)
    }
  }

  const handleJoinStream = async (stream) => {
    try {
      const userId = sessionStorage.getItem("userId")
      if (!userId) {
        alert("Please login first to join the stream.")
        return
      }
      if (!stream.session_id || !stream.teacher_id) {
        alert("Invalid stream data. Please try again.")
        return
      }
      const sessionData = { session_id: stream.session_id, teacher_id: stream.teacher_id }
      const response = await joinStream(sessionData).unwrap()
      if (response.token && response.livekit_url) {
        const correctSessionId = response.stream_info?.session_id || response.room_name || stream.session_id
        const updatedStream = { ...stream, session_id: correctSessionId }
        setCurrentStream(updatedStream)
        setIsViewingStream(true)
        await connectToLiveKitRoom(response.token, response.livekit_url)
      } else {
        alert("Invalid stream response. Missing connection credentials.")
      }
    } catch (error) {
      alert("Failed to join stream: " + (error.data?.message || "Unknown error"))
    }
  }

  const handleLeaveStream = () => {
    if (livekitRoom) {
      livekitRoom.disconnect()
      setLivekitRoom(null)
    }
    cleanupTranslation()
    setIsViewingStream(false)
    setCurrentStream(null)
    setLivekitConnected(false)
    setParticipants([])
    setRemoteVideoTracks([])
    setRemoteAudioTracks([])
    setConnectionStatus("")
    setHasScreenShare(false)
    setHasCameraTrack(false)
  }

  // HTTP POST Translation Function
  const sendTextForTranslation = async (text) => {
    try {
      console.log('📤 [HTTP Translation] Sending text for translation:', text)
      setTranslationStatus("Translating...")

      const response = await fetch('https://sasthra.in/api/translate/text', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          session_id: currentStream?.session_id || 'viewer-session',
          text,
          source_language: sourceLanguage,
          target_language: targetLanguage,
        }),
      });

      const data = await response.json();

      if (data.success && data.translated_text) {
        console.log('✅ [HTTP Translation] Translation received:', data.translated_text)
        setCurrentTranslation(data.translated_text);
        setTranslationStatus("Playing translated audio...")

        if (data.audio_data) {
          console.log('🔊 [HTTP Translation] Adding audio to queue')
          addToAudioQueue(data.audio_data);
        }
      } else {
        console.error('❌ [HTTP Translation] Translation failed:', data)
        setTranslationStatus("Translation failed")
      }

    } catch (error) {
      console.error('❌ [HTTP Translation] Error:', error);
      setTranslationStatus("Translation error")
    }
  }

  // Debug function to manually check for text streams
  const debugCheckTextStreams = () => {
    console.log("🔍 DEBUG: Manually checking for text streams...")
    if (!livekitRoom) {
      console.log("❌ No LiveKit room available")
      return
    }

    console.log("🔍 Remote participants:", livekitRoom.remoteParticipants.size)
    livekitRoom.remoteParticipants.forEach((participant) => {
      console.log(`🔍 Participant: ${participant.identity}`)
      console.log(`🔍 Text streams count:`, participant.textStreams?.size || 0)

      if (participant.textStreams && participant.textStreams.size > 0) {
        participant.textStreams.forEach((stream) => {
          console.log(`🔍 Text stream found:`, {
            id: stream.info.id,
            topic: stream.info.topic,
            state: stream.state
          })

          // Try to manually read from the stream
          console.log("🔍 Attempting manual read from stream...")
          try {
            const manualRead = async () => {
              for await (const chunk of stream) {
                console.log(`🔍 ✅ MANUAL READ SUCCESS: "${chunk}"`)
                setCurrentTranscription(chunk)
                break // Just read one chunk for testing
              }
            }
            manualRead()
          } catch (error) {
            console.error("🔍 Manual read failed:", error)
          }
        })
      } else {
        console.log("🔍 No text streams found for this participant")
      }
    })

    // Test setting transcription manually
    console.log("🔍 Testing manual transcription set...")
    setCurrentTranscription("TEST: Manual transcription set at " + new Date().toLocaleTimeString())

    // Test translation if enabled
    if (isTranslationEnabled) {
      console.log("🔍 Testing translation with manual text...")
      sendTextForTranslation("Hello, this is a test transcription")
    }
  }

  const startTranslation = async () => {
    try {
      console.log('🚀 [HTTP Translation] Starting translation...');
      setTranslationStatus("Starting translation...")

      // Enable translation immediately - no WebSocket needed
      setIsTranslationEnabled(true)
      setTranslationStatus("Translation active - ready to translate teacher's speech")
      console.log('✅ [HTTP Translation] Translation enabled successfully')

      // If we have current transcription, send it for translation
      if (currentTranscription && currentTranscription.trim()) {
        console.log(`📤 [HTTP Translation] Sending current transcription: "${currentTranscription}"`)
        await sendTextForTranslation(currentTranscription.trim())
      }

    } catch (error) {
      setTranslationStatus(`Error: ${error.message}`)
      console.error('❌ [HTTP Translation] startTranslation error:', error);
    }
  }

  const stopTranslation = async () => {
    try {
      console.log('🛑 [HTTP Translation] Stopping translation...')
      setTranslationStatus("Stopping translation...")
      cleanupTranslation()
      setTranslationStatus("Translation stopped")
      console.log('✅ [HTTP Translation] Translation stopped successfully')
    } catch (error) {
      setTranslationStatus(`Error: ${error.message}`)
      console.error('❌ [HTTP Translation] stopTranslation error:', error)
    }
  }







  const playAudioFromQueue = async (audioDataBase64) => {
    return new Promise(async (resolve) => {
      try {
        setTranslationStatus("Playing audio...")
        if (!audioDataBase64) {
          setIsPlayingTranslatedAudio(false)
          resolve()
          return
        }
        if (!audioContextRef.current) {
          audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)()
        }
        if (audioContextRef.current.state === "suspended") {
          await audioContextRef.current.resume()
        }
        try {
          const binaryString = atob(audioDataBase64)
          const audioData = new Uint8Array(binaryString.length)
          for (let i = 0; i < binaryString.length; i++) {
            audioData[i] = binaryString.charCodeAt(i)
          }
          const audioBuffer = await audioContextRef.current.decodeAudioData(audioData.buffer)
          const source = audioContextRef.current.createBufferSource()
          source.buffer = audioBuffer
          source.connect(audioContextRef.current.destination)
          currentAudioSourceRef.current = source
          setIsPlayingTranslatedAudio(true)
          source.onended = () => {
            setIsPlayingTranslatedAudio(false)
            currentAudioSourceRef.current = null
            setTranslationStatus("Translation active - waiting for teacher's speech")
            resolve()
          }
          source.start()
        } catch (webAudioError) {
          try {
            const audioBlob = new Blob(
              [
                new Uint8Array(
                  atob(audioDataBase64)
                    .split("")
                    .map((c) => c.charCodeAt(0)),
                ),
              ],
              { type: "audio/wav" },
            )
            const audioUrl = URL.createObjectURL(audioBlob)
            const audio = new Audio(audioUrl)
            currentHtmlAudioRef.current = audio
            setIsPlayingTranslatedAudio(true)
            audio.onended = () => {
              setIsPlayingTranslatedAudio(false)
              currentHtmlAudioRef.current = null
              URL.revokeObjectURL(audioUrl)
              setTranslationStatus("Translation active - waiting for teacher's speech")
              resolve()
            }
            audio.onerror = () => {
              setIsPlayingTranslatedAudio(false)
              currentHtmlAudioRef.current = null
              URL.revokeObjectURL(audioUrl)
              setTranslationStatus("Audio playback error")
              resolve()
            }
            await audio.play()
          } catch (htmlAudioError) {
            setIsPlayingTranslatedAudio(false)
            setTranslationStatus(`Audio playback error: ${htmlAudioError.message}`)
            resolve()
          }
        }
      } catch (error) {
        setIsPlayingTranslatedAudio(false)
        setTranslationStatus(`Audio playback error: ${error.message}`)
        resolve()
      }
    })
  }

  const handleSendChatMessage = async () => {
    if (!newMessage.trim() || !currentStream) return
    const messageData = {
      session_id: currentStream.session_id,
      message: newMessage.trim(),
      sender_id: sessionStorage.getItem("userId"),
      sender_name: sessionStorage.getItem("name") || "Viewer",
    }
    try {
      await sendChatMessage(messageData).unwrap()
      setNewMessage("")
      setTimeout(loadChatHistory, 500)
    } catch (error) {
      console.error("❌ VIEWER: Error sending message:", error.message)
    }
  }

  const handleKeyDown = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSendChatMessage()
    }
  }

  const toggleChat = () => {
    setIsChatOpen(!isChatOpen)
    if (!isChatOpen) {
      setUnreadMessages(0)
    }
  }

  const formatMessageTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const getRoleColor = (role) => {
    switch (role) {
      case "kota_teacher":
      case "faculty":
        return "text-emerald-500"
      case "student":
        return "text-blue-500"
      case "center_counselor":
        return "text-purple-500"
      default:
        return "text-gray-500"
    }
  }

  // Quiz functions
  const handleOpenQuiz = () => {
    if (receivedQuizId) {
      setIsQuizOpen(true)
      setShowQuizNotification(false)
    }
  }

  const handleCloseQuiz = () => {
    setIsQuizOpen(false)
  }

  const getRoleBadge = (role) => {
    switch (role) {
      case "kota_teacher":
      case "faculty":
        return "Teacher"
      case "student":
        return "Student"
      case "center_counselor":
        return "Counselor"
      default:
        return "User"
    }
  }

  const formatUptime = (uptime) => {
    const hours = Math.floor(uptime / 3600)
    const minutes = Math.floor((uptime % 3600) / 60)
    const seconds = Math.floor(uptime % 60)
    if (hours > 0) return `${hours}h ${minutes}m ${seconds}s`
    if (minutes > 0) return `${minutes}m ${seconds}s`
    return `${seconds}s`
  }

  const formatDateTime = (dateString) => new Date(dateString).toLocaleString()

  if (isLoading)
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="flex items-center justify-center min-h-screen bg-gradient-to-br from-gray-900 via-indigo-900 to-gray-900"
      >
        <div className="text-center">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ repeat: Infinity, duration: 2, ease: "linear" }}
            className="w-20 h-20 border-4 border-indigo-200 border-t-indigo-500 rounded-full mx-auto"
          ></motion.div>
          <motion.p
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="text-white text-lg font-semibold mt-6"
          >
            Loading Live Streams...
          </motion.p>
        </div>
      </motion.div>
    )

  if (error)
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="flex items-center justify-center min-h-screen bg-gradient-to-br from-red-50 to-red-100"
      >
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.4 }}
          className="bg-white rounded-2xl shadow-2xl p-8 max-w-md mx-4 border border-red-100"
        >
          <div className="flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mx-auto mb-4">
            <svg className="h-8 w-8 text-red-600" viewBox="0 0 20 20" fill="currentColor">
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <h3 className="text-xl font-bold text-red-800 text-center mb-2">Connection Error</h3>
          <p className="text-red-600 text-center mb-4">{error.message || "Failed to load active streams"}</p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => trigger()}
            className="w-full bg-red-500 hover:bg-red-600 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200"
          >
            Try Again
          </motion.button>
        </motion.div>
      </motion.div>
    )

  const activeStreams = data?.active_streams || []

  if (isViewingStream && currentStream)
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="min-h-screen bg-gradient-to-br from-gray-900 via-indigo-900 to-gray-900"
      >
        {/* Header */}
        <motion.div
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="bg-gradient-to-r from-gray-800 to-indigo-900 text-white p-4 shadow-lg border-b border-gray-700"
        >
          <div className="flex items-center justify-between max-w-7xl mx-auto">
            <div className="flex items-center space-x-4">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleLeaveStream}
                className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-6 py-3 rounded-lg font-semibold shadow-md flex items-center space-x-2"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                <span>Leave Stream</span>
              </motion.button>
              <div className="border-l border-gray-600 pl-4">
                <h1 className="text-2xl font-bold bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent">
                  Live Stream
                </h1>
                <p className="text-sm text-gray-300">Session: {currentStream.session_id}</p>
              </div>
            </div>
            <div className="flex items-center space-x-6">
              <div className="flex items-center bg-gray-800/50 rounded-lg px-4 py-2">
                <motion.div
                  animate={{ scale: livekitConnected ? [1, 1.2, 1] : 1 }}
                  transition={{ repeat: livekitConnected ? Infinity : 0, duration: 1.5 }}
                  className={`w-3 h-3 rounded-full mr-3 ${livekitConnected ? "bg-green-400" : "bg-red-400"}`}
                ></motion.div>
                <span className="text-sm font-medium">{connectionStatus || "Connecting..."}</span>
              </div>
              <div className="flex items-center bg-gray-800/50 rounded-lg px-4 py-2">
                <svg className="w-4 h-4 mr-2 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                  />
                </svg>
                <span className="text-sm font-medium">{participants.length + 1} Participants</span>
              </div>

              {/* Debug Quiz ID Display */}
              {process.env.NODE_ENV === 'development' && (
                <div className="bg-yellow-500 text-black px-2 py-1 rounded text-xs">
                  Quiz ID: {receivedQuizId || 'None'}
                </div>
              )}

              {/* Quiz Notification and Open Quiz Button */}
              {receivedQuizId && (
                <motion.button
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleOpenQuiz}
                  className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white px-6 py-3 rounded-lg font-semibold shadow-md flex items-center space-x-2 animate-pulse"
                >
                  <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <span>Open Quiz (ID: {receivedQuizId})</span>
                </motion.button>
              )}

              {/* Always show manual quiz button for testing */}
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setIsQuizOpen(true)}
                className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-4 py-2 rounded-lg font-semibold shadow-md flex items-center space-x-2"
              >
                <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                <span>Manual Quiz</span>
              </motion.button>
            </div>
          </div>
        </motion.div>

        <div className="flex h-[calc(100vh-80px)] max-w-7xl mx-auto">
          {/* Video Area */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="flex-1 relative bg-black rounded-lg overflow-hidden shadow-2xl"
          >
            <div className="w-full h-full flex items-center justify-center relative">
              <video
                ref={screenVideoRef}
                autoPlay
                playsInline
                className="w-full h-full object-contain"
              />
              {!hasScreenShare && hasCameraTrack && (
                <video
                  ref={cameraMainRef}
                  autoPlay
                  playsInline
                  className="w-full h-full object-contain absolute inset-0"
                />
              )}
              {remoteVideoTracks.length === 0 && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5 }}
                  className="absolute inset-0 flex items-center justify-center text-white bg-gradient-to-br from-gray-900/70 to-black/70"
                >
                  <div className="text-center">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ repeat: Infinity, duration: 2, ease: "linear" }}
                      className="w-20 h-20 border-4 border-indigo-200 border-t-indigo-500 rounded-full mx-auto mb-4"
                    ></motion.div>
                    <motion.p
                      initial={{ y: 20, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: 0.2 }}
                      className="text-xl font-semibold"
                    >
                      Waiting for stream...
                    </motion.p>
                    <p className="text-gray-300 flex items-center justify-center mt-2">
                      <motion.span
                        animate={{ scale: livekitConnected ? [1, 1.2, 1] : 1 }}
                        transition={{ repeat: livekitConnected ? Infinity : 0, duration: 1.5 }}
                        className={`w-2 h-2 rounded-full mr-2 ${livekitConnected ? "bg-green-400" : "bg-red-400"}`}
                      ></motion.span>
                      {livekitConnected ? "Connected to room" : "Connecting..."}
                    </p>
                  </div>
                </motion.div>
              )}
            </div>
            {hasScreenShare && hasCameraTrack && (
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.4 }}
                className="absolute bottom-6 right-6 w-72 h-52 bg-gray-900 rounded-lg overflow-hidden border border-gray-600 shadow-lg"
              >
                <video ref={cameraVideoRef} autoPlay playsInline muted className="w-full h-full object-cover" />
                <div className="absolute bottom-2 left-2 bg-black/60 text-white text-xs px-2 py-1 rounded">
                  Teacher Camera
                </div>
              </motion.div>
            )}
            {!isChatOpen && (
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={toggleChat}
                className="absolute bottom-6 left-6 bg-indigo-500 hover:bg-indigo-600 text-white p-3 rounded-full shadow-lg"
              >
                <svg
                  className="w-6 h-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.405L3 21l2.595-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"
                  />
                </svg>
                {unreadMessages > 0 && (
                  <motion.span
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="absolute -top-2 -right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full"
                  >
                    {unreadMessages}
                  </motion.span>
                )}
              </motion.button>
            )}
          </motion.div>

          {/* Chat Overlay */}
          <AnimatePresence>
            {isChatOpen && (
              <motion.div
                initial={{ x: '100%' }}
                animate={{ x: 0 }}
                exit={{ x: '100%' }}
                transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                className="absolute right-0 top-0 bottom-0 w-96 bg-gray-800 text-white shadow-lg border-l border-gray-700 z-10"
              >
                <div className="p-4 border-b border-gray-700 flex justify-between items-center">
                  <h3 className="text-lg font-semibold">Live Chat</h3>
                  <button onClick={toggleChat} className="text-gray-400 hover:text-white">
                    <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                <div className="h-[calc(100%-120px)] overflow-y-auto p-4">
                  {isLoadingHistory ? (
                    <div className="flex items-center justify-center h-full">
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ repeat: Infinity, duration: 1 }}
                        className="w-6 h-6 border-2 border-indigo-200 border-t-indigo-500 rounded-full"
                      ></motion.div>
                    </div>
                  ) : chatMessages.length === 0 ? (
                    <div className="flex items-center justify-center h-full text-gray-400">
                      No messages yet
                    </div>
                  ) : (
                    <AnimatePresence>
                      {chatMessages.map((message, index) => (
                        <motion.div
                          key={`${message.id || "msg"}-${index}-${message.timestamp || Date.now()}`}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -10 }}
                          className={`p-2 hover:bg-gray-700/30 rounded mb-2 ${
                            message.message && message.message.includes('QUIZ_START:')
                              ? 'bg-purple-900/50 border border-purple-500'
                              : ''
                          }`}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-1">
                                <span className={`text-sm font-medium ${getRoleColor(message.sender_role)}`}>
                                  {message.sender_name}
                                </span>
                                <span className="text-xs bg-gray-700 px-2 py-1 rounded">
                                  {getRoleBadge(message.sender_role)}
                                </span>
                                {message.message && message.message.includes('QUIZ_START:') && (
                                  <span className="text-xs bg-purple-500 text-white px-2 py-1 rounded">
                                    QUIZ
                                  </span>
                                )}
                              </div>
                              <p className="text-sm text-gray-200">{message.message}</p>
                            </div>
                            <span className="text-xs text-gray-400">{formatMessageTime(message.timestamp)}</span>
                          </div>
                        </motion.div>
                      ))}
                    </AnimatePresence>
                  )}
                </div>
                <div className="absolute bottom-0 left-0 right-0 p-4 bg-gray-800 border-t border-gray-700">
                  <div className="flex space-x-2">
                    <input
                      type="text"
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyDown={handleKeyDown}
                      placeholder="Type a message..."
                      className="flex-1 bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-sm text-white"
                    />
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={handleSendChatMessage}
                      disabled={!newMessage.trim() || isSendingMessage}
                      className="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-lg"
                    >
                      {isSendingMessage ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ repeat: Infinity, duration: 1 }}
                          className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                        ></motion.div>
                      ) : (
                        "Send"
                      )}
                    </motion.button>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Sidebar */}
          <motion.div
            initial={{ x: 100, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="w-20 bg-gray-800 text-white overflow-y-auto shadow-lg border-l border-gray-700"
          >
            <div className="flex flex-col items-center py-4 space-y-4">
              {/* Stream Information */}
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => setActiveSidebarTab('info')}
                className={`p-3 rounded-lg ${activeSidebarTab === 'info' ? 'bg-indigo-600' : 'bg-gray-700 hover:bg-gray-600'}`}
              >
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </motion.button>

              {/* Features */}
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => setActiveSidebarTab('features')}
                className={`p-3 rounded-lg ${activeSidebarTab === 'features' ? 'bg-indigo-600' : 'bg-gray-700 hover:bg-gray-600'}`}
              >
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                </svg>
              </motion.button>

              {/* Participants */}
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => setActiveSidebarTab('participants')}
                className={`p-3 rounded-lg ${activeSidebarTab === 'participants' ? 'bg-indigo-600' : 'bg-gray-700 hover:bg-gray-600'}`}
              >
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
              </motion.button>

              {/* Connection Status */}
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => setActiveSidebarTab('connection')}
                className={`p-3 rounded-lg ${activeSidebarTab === 'connection' ? 'bg-indigo-600' : 'bg-gray-700 hover:bg-gray-600'}`}
              >
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
                </svg>
              </motion.button>

              {/* Translation */}
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => setActiveSidebarTab('translation')}
                className={`p-3 rounded-lg ${activeSidebarTab === 'translation' ? 'bg-indigo-600' : 'bg-gray-700 hover:bg-gray-600'}`}
              >
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                </svg>
              </motion.button>
            </div>
          </motion.div>

          {/* Sidebar Content */}
          <motion.div
            initial={{ x: 100, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="w-80 bg-gray-800 text-white overflow-y-auto shadow-lg border-l border-gray-700"
          >
            <div className="p-6 space-y-6">
              {/* Stream Information */}
              {activeSidebarTab === 'info' && (
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.1 }}
                  className="bg-gray-900 rounded-lg p-4 shadow"
                >
                  <h3 className="text-lg font-semibold mb-3">Stream Information</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between bg-gray-700/50 p-2 rounded">
                      <span className="text-gray-300">Teacher ID:</span>
                      <span className="font-medium text-indigo-400">{currentStream.teacher_id}</span>
                    </div>
                    <div className="flex justify-between bg-gray-700/50 p-2 rounded">
                      <span className="text-gray-300">Quality:</span>
                      <span className="font-medium text-indigo-400">{currentStream.quality || "Standard"}</span>
                    </div>
                    <div className="flex justify-between bg-gray-700/50 p-2 rounded">
                      <span className="text-gray-300">Viewers:</span>
                      <span className="font-medium text-indigo-400">{currentStream.viewer_count || 0}</span>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Features */}
              {activeSidebarTab === 'features' && (
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.2 }}
                  className="bg-gray-900 rounded-lg p-4 shadow"
                >
                  <h3 className="text-lg font-semibold mb-3">Features</h3>
                  <div className="space-y-2">
                    {currentStream.features?.screen_sharing && (
                      <div className="flex items-center bg-indigo-500/10 p-2 rounded">
                        <span className="text-indigo-400">Screen Sharing Active</span>
                      </div>
                    )}
                    {currentStream.features?.chat_enabled && (
                      <div className="flex items-center bg-indigo-500/10 p-2 rounded">
                        <span className="text-indigo-400">Chat Available</span>
                      </div>
                    )}
                    {currentStream.features?.recording_enabled && (
                      <div className="flex items-center bg-indigo-500/10 p-2 rounded">
                        <span className="text-indigo-400">Recording Active</span>
                      </div>
                    )}
                  </div>
                </motion.div>
              )}

              {/* Participants */}
              {activeSidebarTab === 'participants' && (
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.3 }}
                  className="bg-gray-900 rounded-lg p-4 shadow"
                >
                  <h3 className="text-lg font-semibold mb-3">Participants ({participants.length + 1})</h3>
                  <div className="space-y-2 max-h-60 overflow-y-auto">
                    <div className="flex items-center bg-indigo-500/10 p-2 rounded">
                      <div className="w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center text-sm font-bold mr-2">
                        T
                      </div>
                      <div className="flex-1">
                        <div className="font-medium text-indigo-400">Teacher</div>
                        <div className="text-xs text-gray-400">Host</div>
                      </div>
                    </div>
                    <AnimatePresence>
                      {participants.map((participant, index) => (
                        <motion.div
                          key={participant.identity || index}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -10 }}
                          className="flex items-center bg-indigo-500/10 p-2 rounded"
                        >
                          <div className="w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center text-sm font-bold mr-2">
                            {participant.identity?.charAt(0)?.toUpperCase() || "U"}
                          </div>
                          <div className="flex-1">
                            <div className="font-medium text-indigo-400">{participant.identity || "Unknown"}</div>
                            <div className="text-xs text-gray-400">Viewer</div>
                          </div>
                        </motion.div>
                      ))}
                    </AnimatePresence>
                  </div>
                </motion.div>
              )}

              {/* Connection Status */}
              {activeSidebarTab === 'connection' && (
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.4 }}
                  className="bg-gray-900 rounded-lg p-4 shadow"
                >
                  <h3 className="text-lg font-semibold mb-3">Connection Status</h3>
                  <div className="space-y-2">
                    <div className="flex items-center bg-indigo-500/10 p-2 rounded">
                      <motion.div
                        animate={{ scale: livekitConnected ? [1, 1.2, 1] : 1 }}
                        transition={{ repeat: livekitConnected ? Infinity : 0, duration: 1.5 }}
                        className={`w-3 h-3 rounded-full mr-2 ${livekitConnected ? "bg-green-400" : "bg-red-400"}`}
                      ></motion.div>
                      <span className="font-medium">{livekitConnected ? "Connected" : "Disconnected"}</span>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="bg-gray-700/50 p-2 rounded text-center">
                        <div className="text-gray-400">Video Tracks</div>
                        <div className="font-bold text-indigo-400">{remoteVideoTracks.length}</div>
                      </div>
                      <div className="bg-gray-700/50 p-2 rounded text-center">
                        <div className="text-gray-400">Audio Tracks</div>
                        <div className="font-bold text-indigo-400">{remoteAudioTracks.length}</div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Translation */}
              {activeSidebarTab === 'translation' && (
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.5 }}
                  className="bg-gray-900 rounded-lg p-4 shadow"
                >
                  <h3 className="text-lg font-semibold mb-3">Live Translation</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-300 font-medium">Enable Translation</span>
                      <div className="flex gap-2">
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={debugCheckTextStreams}
                          className="px-2 py-1 text-xs bg-yellow-500 hover:bg-yellow-600 text-black rounded"
                        >
                          Debug
                        </motion.button>
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={isTranslationEnabled ? stopTranslation : startTranslation}
                          className={`px-4 py-2 rounded-lg font-semibold ${
                            isTranslationEnabled
                              ? "bg-red-500 hover:bg-red-600 text-white"
                              : "bg-indigo-500 hover:bg-indigo-600 text-white"
                          }`}
                        >
                          {isTranslationEnabled ? "Stop" : "Start"}
                        </motion.button>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-xs text-gray-400 mb-1">From</label>
                        <select
                          value={sourceLanguage}
                          onChange={(e) => setSourceLanguage(e.target.value)}
                          disabled={isTranslationEnabled}
                          className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-sm text-white disabled:opacity-50"
                        >
                          {availableLanguages.map((lang) => (
                            <option key={lang.code} value={lang.code}>
                              {lang.flag} {lang.name}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <label className="block text-xs text-gray-400 mb-1">To</label>
                        <select
                          value={targetLanguage}
                          onChange={(e) => setTargetLanguage(e.target.value)}
                          disabled={isTranslationEnabled}
                          className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-sm text-white disabled:opacity-50"
                        >
                          {availableLanguages.map((lang) => (
                            <option key={lang.code} value={lang.code}>
                              {lang.flag} {lang.name}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center bg-indigo-500/10 p-2 rounded">
                        <motion.div
                          animate={{ scale: isTranslationEnabled ? [1, 1.2, 1] : 1 }}
                          transition={{ repeat: isTranslationEnabled ? Infinity : 0, duration: 1.5 }}
                          className={`w-3 h-3 rounded-full mr-2 ${isTranslationEnabled ? "bg-green-400" : "bg-gray-400"}`}
                        ></motion.div>
                        <span className="text-sm">
                          {translationStatus || (isTranslationEnabled ? "Translation Active" : "Translation Inactive")}
                        </span>
                      </div>
                      {isPlayingTranslatedAudio && (
                        <div className="flex items-center bg-indigo-500/10 p-2 rounded">
                          <motion.div
                            animate={{ scale: [1, 1.2, 1] }}
                            transition={{ repeat: Infinity, duration: 1 }}
                            className="w-3 h-3 bg-indigo-400 rounded-full mr-2"
                          ></motion.div>
                          <span className="text-sm">Playing Translated Audio</span>
                        </div>
                      )}
                      {audioQueueLength > 0 && (
                        <div className="flex items-center bg-yellow-500/10 p-2 rounded">
                          <motion.div
                            animate={{ scale: [1, 1.2, 1] }}
                            transition={{ repeat: Infinity, duration: 1 }}
                            className="w-3 h-3 bg-yellow-400 rounded-full mr-2"
                          ></motion.div>
                          <span className="text-sm">Audio Queue: {audioQueueLength} pending</span>
                        </div>
                      )}
                    </div>
                    {isTranslationEnabled && (
                      <div className="bg-gray-700/50 rounded-lg p-2 text-xs">
                        <div className="flex justify-between">
                          <span className="text-gray-400">WebSocket:</span>
                          <span
                            className={
                              translationWebSocket
                                ? translationWebSocket.readyState === 1
                                  ? "text-green-400"
                                  : translationWebSocket.readyState === 0
                                    ? "text-yellow-400"
                                    : "text-red-400"
                                : "text-red-400"
                            }
                          >
                            {translationWebSocket
                              ? translationWebSocket.readyState === 1
                                ? "Connected"
                                : translationWebSocket.readyState === 0
                                  ? "Connecting"
                                  : "Closed"
                              : "Not Created"}
                          </span>
                        </div>
                      </div>
                    )}
                    {currentTranscription && (
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="bg-gray-700/50 rounded-lg p-3"
                      >
                        <div className="text-xs text-indigo-400 mb-1">Original ({sourceLanguage.toUpperCase()}):</div>
                        <div className="text-sm">{currentTranscription}</div>
                      </motion.div>
                    )}
                    {currentTranslation && (
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="bg-indigo-900/30 rounded-lg p-3"
                      >
                        <div className="text-xs text-indigo-400 mb-1">Translation ({targetLanguage.toUpperCase()}):</div>
                        <div className="text-sm">{currentTranslation}</div>
                      </motion.div>
                    )}
                    <div className="flex items-center bg-blue-500/10 p-2 rounded text-xs">
                      <span>Translation receives transcribed text from teacher via LiveKit</span>
                    </div>
                    {isReceivingTranscription && (
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="bg-green-500/10 rounded-lg p-2"
                      >
                        <div className="flex items-center">
                          <motion.div
                            animate={{ scale: [1, 1.2, 1] }}
                            transition={{ repeat: Infinity, duration: 1 }}
                            className="w-2 h-2 bg-green-400 rounded-full mr-2"
                          />
                          <span className="text-xs text-green-400">Receiving live transcription from teacher</span>
                        </div>
                      </motion.div>
                    )}

                    {/* Debug info */}
                    <div className="bg-gray-800/50 rounded-lg p-2 text-xs">
                      <div className="text-gray-400 mb-1">Debug Info:</div>
                      <div className="space-y-1">
                        <div>Room Connected: {livekitConnected ? "✅" : "❌"}</div>
                        <div>Participants: {participants.length}</div>
                        <div>Receiving Text: {isReceivingTranscription ? "✅" : "❌"}</div>
                        <div>Current Text: {currentTranscription ? "✅" : "❌"}</div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}
            </div>
          </motion.div>
        </div>

        {/* Quiz Overlay */}
        <AnimatePresence>
          {isQuizOpen && receivedQuizId && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-50"
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                className="w-full h-full max-w-7xl mx-4"
              >
                <LiveQuiz quizId={receivedQuizId} onClose={handleCloseQuiz} />
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    )

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="min-h-screen bg-gradient-to-br from-gray-50 to-indigo-100"
    >
      <div className="max-w-7xl mx-auto px-4 py-8">
        <motion.div
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
            Live Streams
          </h1>
          <p className="text-lg text-gray-600 mt-2">Join active streaming sessions</p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => trigger()}
            className="mt-4 bg-indigo-500 hover:bg-indigo-600 text-white px-6 py-3 rounded-lg font-semibold"
          >
            Refresh Streams
          </motion.button>
        </motion.div>

        {activeStreams.length === 0 ? (
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.4 }}
            className="text-center py-20"
          >
            <div className="bg-white rounded-lg shadow-lg p-8 max-w-md mx-auto">
              <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1}
                  d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                />
              </svg>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No Active Streams</h3>
              <p className="text-gray-500">There are currently no live streaming sessions available.</p>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => trigger()}
                className="mt-4 bg-indigo-500 hover:bg-indigo-600 text-white px-6 py-3 rounded-lg font-semibold"
              >
                Check Again
              </motion.button>
            </div>
          </motion.div>
        ) : (
          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ staggerChildren: 0.2 }}
          >
            {activeStreams.map((stream, index) => (
              <motion.div
                key={stream.session_id || index}
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow"
              >
                <div className="flex items-center justify-between mb-4">
                  <span className="text-sm font-semibold text-indigo-600">Session: {stream.session_id}</span>
                  <span className="bg-indigo-100 text-indigo-600 px-2 py-1 rounded text-xs">
                    {stream.quality || "Standard"}
                  </span>
                </div>
                <p className="text-gray-600 text-sm mb-4">Teacher ID: {stream.teacher_id}</p>
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="text-center bg-indigo-50 p-3 rounded">
                    <div className="text-lg font-bold text-indigo-600">{stream.viewer_count || 0}</div>
                    <div className="text-xs text-gray-500">Viewers</div>
                  </div>
                  <div className="text-center bg-indigo-50 p-3 rounded">
                    <div className="text-lg font-bold text-indigo-600">{formatUptime(stream.uptime || 0)}</div>
                    <div className="text-xs text-gray-500">Uptime</div>
                  </div>
                </div>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => handleJoinStream(stream)}
                  disabled={isJoining}
                  className="w-full bg-indigo-500 hover:bg-indigo-600 text-white py-3 rounded-lg font-semibold"
                >
                  {isJoining ? "Joining..." : "Join Stream"}
                </motion.button>
              </motion.div>
            ))}
          </motion.div>
        )}
      </div>

      {/* Toast Notifications */}
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="dark"
      />
    </motion.div>
  )
}

export default CenterTraineeLiveViewer



//===================================================
// import { useEffect, useState, useRef } from "react"
// import { Room, RoomEvent } from "livekit-client"
// import { motion, AnimatePresence } from "framer-motion"
// import { toast, ToastContainer } from 'react-toastify'
// import 'react-toastify/dist/ReactToastify.css'
// import {
//   useLazyGetCenterLiveViewerQuery,
//   useJoinLiveStreamMutation,
//   useStartTranslationSessionMutation,
//   useStopTranslationSessionMutation,
//   useSendChatMessageMutation,
//   useLazyGetChatHistoryQuery,
// } from "./centerTraineeLive.slice"
// import LiveQuiz from "./LiveQuiz"

// const CenterTraineeLiveViewer = () => {
//   const [trigger, { data, error, isLoading }] = useLazyGetCenterLiveViewerQuery()
//   const [joinStream, { isLoading: isJoining }] = useJoinLiveStreamMutation()
//   const [startTranslationSession] = useStartTranslationSessionMutation()
//   const [stopTranslationSession] = useStopTranslationSessionMutation()
//   const [sendChatMessage, { isLoading: isSendingMessage }] = useSendChatMessageMutation()
//   const [getChatHistory, { data: chatHistoryData, isLoading: isLoadingHistory }] = useLazyGetChatHistoryQuery()

//   // Stream viewing states
//   const [isViewingStream, setIsViewingStream] = useState(false)
//   const [currentStream, setCurrentStream] = useState(null)
//   const [livekitRoom, setLivekitRoom] = useState(null)
//   const [livekitConnected, setLivekitConnected] = useState(false)
//   const [participants, setParticipants] = useState([])
//   const [remoteVideoTracks, setRemoteVideoTracks] = useState([])
//   const [remoteAudioTracks, setRemoteAudioTracks] = useState([])
//   const [connectionStatus, setConnectionStatus] = useState("")
//   const [hasScreenShare, setHasScreenShare] = useState(false)
//   const [hasCameraTrack, setHasCameraTrack] = useState(false)

//   // Refs for video elements
//   const mainVideoRef = useRef(null)
//   const screenVideoRef = useRef(null)
//   const cameraVideoRef = useRef(null)
//   const cameraMainRef = useRef(null)

//   // Translation states
//   const [isTranslationEnabled, setIsTranslationEnabled] = useState(false)
//   const [translationSession, setTranslationSession] = useState(null)
//   const [translationWebSocket, setTranslationWebSocket] = useState(null)
//   const [sourceLanguage, setSourceLanguage] = useState("en")
//   const [targetLanguage, setTargetLanguage] = useState("ta")
//   const [translationStatus, setTranslationStatus] = useState("")
//   const [currentTranscription, setCurrentTranscription] = useState("")
//   const [currentTranslation, setCurrentTranslation] = useState("")
//   const [isPlayingTranslatedAudio, setIsPlayingTranslatedAudio] = useState(false)
//   const [audioQueueLength, setAudioQueueLength] = useState(0)

//   // LiveKit text stream states
//   const [receivedTranscriptions, setReceivedTranscriptions] = useState([])
//   const [isReceivingTranscription, setIsReceivingTranscription] = useState(false)
//   const [pendingTranscriptions, setPendingTranscriptions] = useState([]) // Queue for text received before translation starts

//   // Translation audio refs
//   const audioContextRef = useRef(null)
//   const translationWebSocketRef = useRef(null)

//   // Audio Queue System refs and state
//   const audioQueueRef = useRef([])
//   const currentAudioSourceRef = useRef(null)
//   const currentHtmlAudioRef = useRef(null)
//   const isProcessingQueueRef = useRef(false)

//   // Chat states
//   const [chatMessages, setChatMessages] = useState([])
//   const [newMessage, setNewMessage] = useState("")
//   const [isChatOpen, setIsChatOpen] = useState(false)
//   const [unreadMessages, setUnreadMessages] = useState(0)
//   const [activeSidebarTab, setActiveSidebarTab] = useState("info") // 'info', 'features', 'participants', 'connection', 'translation'

//   // Quiz states
//   const [receivedQuizId, setReceivedQuizId] = useState(null)
//   const [showQuizNotification, setShowQuizNotification] = useState(false)
//   const [isQuizOpen, setIsQuizOpen] = useState(false)

//   // Available languages
//   const availableLanguages = [
//     { code: "en", name: "English", flag: "🇺🇸" },
//     { code: "ta", name: "Tamil", flag: "🇮🇳" },
//     { code: "hi", name: "Hindi", flag: "🇮🇳" },
//     { code: "te", name: "Telugu", flag: "🇮🇳" },
//     { code: "kn", name: "Kannada", flag: "🇮🇳" },
//   ]

//   // Mute/unmute original audio based on translation state
//   useEffect(() => {
//     remoteAudioTracks.forEach(({ audioElement }) => {
//       if (audioElement) {
//         audioElement.muted = isTranslationEnabled
//       }
//     })
//   }, [isTranslationEnabled, remoteAudioTracks])

//   useEffect(() => {
//     trigger()
//     const interval = setInterval(() => trigger(), 30000)
//     return () => clearInterval(interval)
//   }, [trigger])

//   useEffect(() => {
//     return () => {
//       if (livekitRoom) {
//         livekitRoom.disconnect()
//         setLivekitRoom(null)
//       }
//       cleanupTranslation()
//     }
//   }, [livekitRoom])

//   // HTTP-based chat system
//   useEffect(() => {
//     if (isViewingStream && currentStream) {
//       console.log("💬 VIEWER: Starting HTTP-based chat for session:", currentStream.session_id)
//       setChatMessages([])
//       loadChatHistory()
//       const pollInterval = setInterval(() => loadChatHistory(), 2000)
//       return () => clearInterval(pollInterval)
//     }
//   }, [isViewingStream, currentStream])

//   useEffect(() => {
//     if (
//       isViewingStream &&
//       currentStream &&
//       !isTranslationEnabled &&
//       !translationSession // Only start if not already started
//     ) {
//       startTranslation();
//     }
//   }, [isViewingStream, currentStream]);

//   const loadChatHistory = async () => {
//     if (!currentStream?.session_id) return
//     try {
//       const response = await getChatHistory(currentStream.session_id).unwrap()
//       const newMessages = response.messages || []
//       console.log("Chat history loaded:", newMessages.length, "messages")

//       setChatMessages((prev) => {
//         if (newMessages.length !== prev.length) {
//           const newCount = newMessages.length - prev.length
//           console.log("New messages detected:", newCount)

//           if (newCount > 0 && !isChatOpen) {
//             setUnreadMessages((prev) => prev + newCount)
//           }

//           // Check for new quiz messages
//           const latestMessages = newMessages.slice(-newCount)
//           console.log("Checking latest messages for quiz:", latestMessages)

//           latestMessages.forEach(message => {
//             console.log("Checking message:", message.message)
//             // Check if message contains quiz start pattern
//             if (message.message && message.message.includes('QUIZ_START:')) {
//               console.log("Quiz message found:", message.message)
//               const quizMatch = message.message.match(/QUIZ_START:([a-zA-Z0-9]+)/)
//               if (quizMatch && quizMatch[1]) {
//                 const quizId = quizMatch[1]
//                 console.log("Quiz detected! Quiz ID:", quizId)
//                 setReceivedQuizId(quizId)
//                 setShowQuizNotification(true)
//                 toast.success('🎯 Quiz Started! Click "Open Quiz" to participate.', {
//                   position: "top-right",
//                   autoClose: 5000,
//                   hideProgressBar: false,
//                   closeOnClick: true,
//                   pauseOnHover: true,
//                   draggable: true,
//                 })
//               }
//             }
//           })

//           return newMessages
//         }
//         return prev
//       })
//     } catch (error) {
//       console.log("❌ VIEWER: Failed to load chat history:", error.message)
//     }
//   }

//   // Audio Queue Management
//   const addToAudioQueue = (audioData) => {
//     audioQueueRef.current.push(audioData)
//     setAudioQueueLength(audioQueueRef.current.length)
//     processAudioQueue()
//   }

//   const processAudioQueue = async () => {
//     if (isProcessingQueueRef.current || audioQueueRef.current.length === 0) return
//     isProcessingQueueRef.current = true
//     while (audioQueueRef.current.length > 0) {
//       const audioData = audioQueueRef.current.shift()
//       setAudioQueueLength(audioQueueRef.current.length)
//       await playAudioFromQueue(audioData)
//     }
//     isProcessingQueueRef.current = false
//     setAudioQueueLength(0)
//   }

//   const stopCurrentAudio = () => {
//     if (currentAudioSourceRef.current) {
//       try {
//         currentAudioSourceRef.current.stop()
//         currentAudioSourceRef.current.disconnect()
//       } catch (error) {
//         console.warn("⚠️ Error stopping Web Audio source:", error)
//       }
//       currentAudioSourceRef.current = null
//     }
//     if (currentHtmlAudioRef.current) {
//       try {
//         currentHtmlAudioRef.current.pause()
//         currentHtmlAudioRef.current.currentTime = 0
//         if (currentHtmlAudioRef.current.src) {
//           URL.revokeObjectURL(currentHtmlAudioRef.current.src)
//         }
//       } catch (error) {
//         console.warn("⚠️ Error stopping HTML5 Audio:", error)
//       }
//       currentHtmlAudioRef.current = null
//     }
//     setIsPlayingTranslatedAudio(false)
//   }

//   const clearAudioQueue = () => {
//     audioQueueRef.current = []
//     setAudioQueueLength(0)
//     stopCurrentAudio()
//     isProcessingQueueRef.current = false
//   }

//   const cleanupTranslation = () => {
//     if (translationWebSocket) {
//       translationWebSocket.close()
//       setTranslationWebSocket(null)
//     }
//     if (translationWebSocketRef.current) {
//       translationWebSocketRef.current.close()
//       translationWebSocketRef.current = null
//     }
//     if (audioContextRef.current && audioContextRef.current.state !== "closed") {
//       audioContextRef.current.close()
//       audioContextRef.current = null
//     }
//     clearAudioQueue()
//     setTranslationSession(null)
//     setIsTranslationEnabled(false)
//     setTranslationStatus("")
//     setCurrentTranscription("")
//     setCurrentTranslation("")
//   }

//   useEffect(() => {
//     if (remoteVideoTracks.length === 0) return
//     remoteVideoTracks.forEach(({ track, publication }) => {
//       if (publication.source === "screen_share" || publication.source === "screen_share_audio") {
//         if (screenVideoRef.current && !track.attachedElements.includes(screenVideoRef.current)) {
//           track.attach(screenVideoRef.current)
//         }
//       } else if (publication.source === "camera" || publication.trackName === "teacher_camera" || !publication.source) {
//         if (hasScreenShare) {
//           if (cameraVideoRef.current && !track.attachedElements.includes(cameraVideoRef.current)) {
//             track.attach(cameraVideoRef.current)
//           }
//         } else {
//           if (cameraMainRef.current && !track.attachedElements.includes(cameraMainRef.current)) {
//             track.attach(cameraMainRef.current)
//           }
//         }
//       }
//     })
//   }, [remoteVideoTracks, hasScreenShare, hasCameraTrack])

//   const connectToLiveKitRoom = async (token, url) => {
//     try {
//       setConnectionStatus("Connecting...")
//       const room = new Room()
//       room.on(RoomEvent.Connected, () => {
//         setLivekitConnected(true)
//         setConnectionStatus("Connected")

//         // Check for existing participants and their text streams
//         console.log("🔗 Room connected, checking for existing participants...")
//         room.remoteParticipants.forEach((participant) => {
//           console.log(`📝 Checking participant ${participant.identity} for text streams...`)

//           // Check for existing text streams
//           if (participant.textStreams && participant.textStreams.size > 0) {
//             participant.textStreams.forEach((stream) => {
//               console.log(`📝 Found text stream: ${stream.info.id} with topic: ${stream.info.topic}`)
//               if (stream.info.topic === 'transcription') {
//                 console.log("📝 ✅ Found existing transcription stream!")
//                 setIsReceivingTranscription(true)
//                 const processExistingStream = async () => {
//                   try {
//                     for await (const chunk of stream) {
//                       console.log(`📝 ✅ Existing stream chunk: "${chunk}"`)
//                       setCurrentTranscription(chunk)
//                       if (isTranslationEnabled && chunk.trim()) {
//                         await sendTextForTranslation(chunk.trim())
//                       }
//                     }
//                   } catch (error) {
//                     console.error("❌ Error processing existing stream:", error)
//                   } finally {
//                     setIsReceivingTranscription(false)
//                   }
//                 }
//                 processExistingStream()
//               }
//             })
//           }
//         })
//       })
//       room.on(RoomEvent.Disconnected, () => {
//         setLivekitConnected(false)
//         setConnectionStatus("Disconnected")
//         setIsViewingStream(false)
//       })
//       room.on(RoomEvent.ParticipantConnected, (participant) => {
//         console.log("📥 Participant connected:", participant.identity)
//         setParticipants((prev) => [...prev, participant])

//         // Check if this participant has any text streams
//         console.log("📝 Checking for existing text streams from", participant.identity)
//         if (participant.textStreams && participant.textStreams.size > 0) {
//           participant.textStreams.forEach((stream) => {
//             console.log(`📝 Found existing text stream: ${stream.info.id} with topic: ${stream.info.topic}`)
//             if (stream.info.topic === 'transcription') {
//               console.log("📝 ✅ Found transcription stream, setting up reader...")
//               setIsReceivingTranscription(true)
//               const processExistingStream = async () => {
//                 try {
//                   for await (const chunk of stream) {
//                     console.log(`📝 ✅ Chunk from existing stream: "${chunk}"`)
//                     setCurrentTranscription(chunk)
//                     if (isTranslationEnabled && chunk.trim()) {
//                       await sendTextForTranslation(chunk.trim())
//                     }
//                   }
//                 } catch (error) {
//                   console.error("❌ Error processing existing stream:", error)
//                 } finally {
//                   setIsReceivingTranscription(false)
//                 }
//               }
//               processExistingStream()
//             }
//           })
//         }
//       })
//       room.on(RoomEvent.ParticipantDisconnected, (participant) => {
//         console.log("📤 Participant disconnected:", participant.identity)
//         setParticipants((prev) => prev.filter((p) => p.identity !== participant.identity))
//         // Reset transcription state when teacher disconnects
//         if (participant.identity.includes('teacher') || participant.identity.includes('Teacher')) {
//           setCurrentTranscription("")
//           setIsReceivingTranscription(false)
//         }
//       })
//       room.on(RoomEvent.TrackSubscribed, (track, publication, participant) => {
//         if (track.kind === "video") {
//           setRemoteVideoTracks((prev) => {
//             const newTracks = [...prev, { track, participant, publication }]
//             setHasScreenShare(
//               newTracks.some(
//                 (t) => t.publication.source === "screen_share" || t.publication.source === "screen_share_audio",
//               ),
//             )
//             setHasCameraTrack(
//               newTracks.some(
//                 (t) =>
//                   t.publication.source === "camera" ||
//                   t.publication.trackName === "teacher_camera" ||
//                   !t.publication.source,
//               ),
//             )
//             return newTracks
//           })
//           if (publication.source === "screen_share" || publication.source === "screen_share_audio") {
//             if (screenVideoRef.current) track.attach(screenVideoRef.current)
//           } else if (
//             publication.source === "camera" ||
//             publication.trackName === "teacher_camera" ||
//             !publication.source
//           ) {
//             if (cameraVideoRef.current) track.attach(cameraVideoRef.current)
//             if (
//               !remoteVideoTracks.some(
//                 (t) => t.publication.source === "screen_share" || t.publication.source === "screen_share_audio",
//               ) &&
//               cameraMainRef.current
//             ) {
//               track.attach(cameraMainRef.current)
//             }
//           }
//         } else if (track.kind === "audio") {
//           const audioElement = track.attach()
//           setRemoteAudioTracks((prev) => [...prev, { track, participant, publication, audioElement }])
//         }
//       })
//       room.on(RoomEvent.TrackUnsubscribed, (track, publication, participant) => {
//         if (track.kind === "video") {
//           setRemoteVideoTracks((prev) => {
//             const newTracks = prev.filter((t) => t.track !== track)
//             setHasScreenShare(
//               newTracks.some(
//                 (t) => t.publication.source === "screen_share" || t.publication.source === "screen_share_audio",
//               ),
//             )
//             setHasCameraTrack(
//               newTracks.some(
//                 (t) =>
//                   t.publication.source === "camera" ||
//                   t.publication.trackName === "teacher_camera" ||
//                   !t.publication.source,
//               ),
//             )
//             return newTracks
//           })
//           track.detach()
//         } else if (track.kind === "audio") {
//           setRemoteAudioTracks((prev) => {
//             const trackInfo = prev.find((t) => t.track === track)
//             if (trackInfo && trackInfo.audioElement) {
//               track.detach(trackInfo.audioElement)
//             }
//             return prev.filter((t) => t.track !== track)
//           })
//         }
//       })

//       // CRITICAL: Register text stream handler BEFORE connecting
//       console.log("🔧 Registering LiveKit text stream handler for transcriptions...")
//       room.registerTextStreamHandler('transcription', async (reader, participantInfo) => {
//         console.log(`📝 ✅ RECEIVED transcription stream from ${participantInfo.identity}`)
//         setIsReceivingTranscription(true)
//         try {
//           console.log("📝 [Viewer] Starting to process transcription stream with async iterator...")
//           for await (const chunk of reader) {
//             console.log(`📝 [Viewer] CHUNK RECEIVED: \"${chunk}\"`)
//             setCurrentTranscription(chunk)
//             setReceivedTranscriptions(prev => [...prev.slice(-4), chunk])
//             console.log('[DEBUG] Handler State:', {
//               isTranslationEnabled,
//               translationSession,
//               translationWebSocket,
//               pendingTranscriptions
//             })
//             // If translation is enabled, send immediately; otherwise, queue it
//             if (isTranslationEnabled && chunk.trim()) {
//               console.log(`📤 [Viewer] Sending for translation: \"${chunk.trim()}\"`)
//               await sendTextForTranslation(chunk.trim())
//             } else if (chunk.trim()) {
//               setPendingTranscriptions(prev => [...prev, chunk.trim()])
//               console.log('[DEBUG] Queued chunk for later translation:', chunk.trim())
//             }
//           }
//         } catch (error) {
//           console.error("❌ [Viewer] Error processing transcription stream:", error)
//         } finally {
//           setIsReceivingTranscription(false)
//         }
//       })

//       // Listen for text stream publications with enhanced handling
//       room.on(RoomEvent.TextStreamPublished, (stream, participant) => {
//         console.log(`📝 ✅ Text stream published by ${participant.identity}:`, stream.info)
//         if (stream.info.topic === 'transcription') {
//           console.log("📝 ✅ Transcription stream detected, setting up reader...")
//           setIsReceivingTranscription(true)

//           // Multiple approaches for published streams too
//           const processPublishedStream = async () => {
//             try {
//               console.log("📝 Processing published stream with async iterator...")
//               for await (const chunk of stream) {
//                 console.log(`📝 ✅ PUBLISHED STREAM CHUNK: "${chunk}"`)
//                 setCurrentTranscription(chunk)
//                 if (isTranslationEnabled && chunk.trim()) {
//                   await sendTextForTranslation(chunk.trim())
//                 }
//               }
//             } catch (error) {
//               console.error("❌ Error processing published stream:", error)
//             } finally {
//               setIsReceivingTranscription(false)
//             }
//           }

//           // Also try event-based approach for published streams
//           if (stream.on) {
//             stream.on('data', (chunk) => {
//               console.log(`📝 ✅ PUBLISHED STREAM EVENT: "${chunk}"`)
//               setCurrentTranscription(chunk)
//               if (isTranslationEnabled && chunk.trim()) {
//                 sendTextForTranslation(chunk.trim())
//               }
//             })
//           }

//           processPublishedStream()
//         }
//       })

//       // ENHANCED data channel handler as fallback
//       room.on(RoomEvent.DataReceived, (payload, participant) => {
//         console.log(`📡 ✅ Data received from ${participant?.identity}:`, payload)
//         console.log(`📡 Payload type:`, typeof payload)
//         console.log(`📡 Payload length:`, payload?.length || 'N/A')

//         try {
//           const data = JSON.parse(new TextDecoder().decode(payload))
//           console.log(`📡 Decoded data:`, data)

//           if (data.type === 'transcription') {
//             console.log(`📝 ✅ FALLBACK TRANSCRIPTION RECEIVED: "${data.text}"`)
//             setCurrentTranscription(data.text)
//             setIsReceivingTranscription(true)

//             if (isTranslationEnabled && data.text.trim()) {
//               console.log(`📤 Sending fallback text for translation: "${data.text.trim()}"`)
//               sendTextForTranslation(data.text.trim())
//             }

//             // Reset receiving status after a delay
//             setTimeout(() => setIsReceivingTranscription(false), 2000)
//           }
//         } catch (e) {
//           console.log("📡 Non-JSON data received:", e.message)
//           // Try to decode as plain text
//           try {
//             const text = new TextDecoder().decode(payload)
//             console.log(`📡 Plain text received: "${text}"`)
//             if (text && text.length > 0) {
//               setCurrentTranscription(text)
//               setIsReceivingTranscription(true)
//               setTimeout(() => setIsReceivingTranscription(false), 2000)
//             }
//           } catch (textError) {
//             console.log("📡 Could not decode as text either")
//           }
//         }
//       })

//       console.log("🔗 Connecting to LiveKit room...")
//       await room.connect(url, token)
//       setLivekitRoom(room)
//     } catch (err) {
//       setConnectionStatus("Connection failed: " + err.message)
//       alert("Failed to connect to stream: " + err.message)
//     }
//   }

//   const handleJoinStream = async (stream) => {
//     try {
//       const userId = sessionStorage.getItem("userId")
//       if (!userId) {
//         alert("Please login first to join the stream.")
//         return
//       }
//       if (!stream.session_id || !stream.teacher_id) {
//         alert("Invalid stream data. Please try again.")
//         return
//       }
//       const sessionData = { session_id: stream.session_id, teacher_id: stream.teacher_id }
//       const response = await joinStream(sessionData).unwrap()
//       if (response.token && response.livekit_url) {
//         const correctSessionId = response.stream_info?.session_id || response.room_name || stream.session_id
//         const updatedStream = { ...stream, session_id: correctSessionId }
//         setCurrentStream(updatedStream)
//         setIsViewingStream(true)
//         await connectToLiveKitRoom(response.token, response.livekit_url)
//       } else {
//         alert("Invalid stream response. Missing connection credentials.")
//       }
//     } catch (error) {
//       alert("Failed to join stream: " + (error.data?.message || "Unknown error"))
//     }
//   }

//   const handleLeaveStream = () => {
//     if (livekitRoom) {
//       livekitRoom.disconnect()
//       setLivekitRoom(null)
//     }
//     cleanupTranslation()
//     setIsViewingStream(false)
//     setCurrentStream(null)
//     setLivekitConnected(false)
//     setParticipants([])
//     setRemoteVideoTracks([])
//     setRemoteAudioTracks([])
//     setConnectionStatus("")
//     setHasScreenShare(false)
//     setHasCameraTrack(false)
//   }

//   // Replace sendTextForTranslation to use HTTP POST
//   const sendTextForTranslation = async (text) => {
//     try {
//       console.log('[DEBUG] sendTextForTranslation (HTTP POST) called with:', text)
//       const response = await fetch('https://sasthra.in/api/translate/text', {
//         method: 'POST',
//         headers: { 'Content-Type': 'application/json' },
//         body: JSON.stringify({
//           session_id: 'dummy-session', // You can use a real session if needed
//           text,
//           source_language: sourceLanguage,
//           target_language: targetLanguage,
//         }),
//       });
//       const data = await response.json();
//       if (data.translated_text) setCurrentTranslation(data.translated_text);
//       if (data.audio_data) addToAudioQueue(data.audio_data);
//       console.log('[DEBUG] Translation response:', data);
//     } catch (error) {
//       console.error('Error sending text for translation (HTTP POST):', error);
//     }
//   }

//   // Debug function to manually check for text streams
//   const debugCheckTextStreams = () => {
//     console.log("🔍 DEBUG: Manually checking for text streams...")
//     if (!livekitRoom) {
//       console.log("❌ No LiveKit room available")
//       return
//     }

//     console.log("🔍 Remote participants:", livekitRoom.remoteParticipants.size)
//     livekitRoom.remoteParticipants.forEach((participant) => {
//       console.log(`🔍 Participant: ${participant.identity}`)
//       console.log(`🔍 Text streams count:`, participant.textStreams?.size || 0)

//       if (participant.textStreams && participant.textStreams.size > 0) {
//         participant.textStreams.forEach((stream) => {
//           console.log(`🔍 Text stream found:`, {
//             id: stream.info.id,
//             topic: stream.info.topic,
//             state: stream.state
//           })

//           // Try to manually read from the stream
//           console.log("🔍 Attempting manual read from stream...")
//           try {
//             const manualRead = async () => {
//               for await (const chunk of stream) {
//                 console.log(`🔍 ✅ MANUAL READ SUCCESS: "${chunk}"`)
//                 setCurrentTranscription(chunk)
//                 break // Just read one chunk for testing
//               }
//             }
//             manualRead()
//           } catch (error) {
//             console.error("🔍 Manual read failed:", error)
//           }
//         })
//       } else {
//         console.log("🔍 No text streams found for this participant")
//       }
//     })

//     // Test setting transcription manually
//     console.log("🔍 Testing manual transcription set...")
//     setCurrentTranscription("TEST: Manual transcription set at " + new Date().toLocaleTimeString())

//     // Test translation if enabled
//     if (isTranslationEnabled) {
//       console.log("🔍 Testing translation with manual text...")
//       sendTextForTranslation("Hello, this is a test transcription")
//     }
//   }

//   const startTranslation = async () => {
//     try {
//       console.log('[DEBUG] startTranslation called');
//       setTranslationStatus("Starting translation...")
//       const userId = sessionStorage.getItem("userId")
//       if (!userId) {
//         console.error('[DEBUG] No userId in sessionStorage');
//         throw new Error("User not authenticated")
//       }
//       console.log('[DEBUG] Calling startTranslationSession with:', {
//         user_id: userId,
//         stream_session_id: currentStream?.session_id,
//         source_language: sourceLanguage,
//         target_language: targetLanguage,
//       });
//       const data = await startTranslationSession({
//         user_id: userId,
//         stream_session_id: currentStream.session_id,
//         source_language: sourceLanguage,
//         target_language: targetLanguage,
//       }).unwrap()
//       console.log('[DEBUG] startTranslationSession response:', data);
//       await new Promise((resolve, reject) => {
//         connectTranslationWebSocket(data.websocket_url)
//           .then(resolve)
//           .catch(reject);
//       });
//       const wsInstance = translationWebSocketRef.current || translationWebSocket;
//       console.log('[DEBUG] WebSocket after connect:', wsInstance, wsInstance?.readyState);
//       if (!wsInstance || wsInstance.readyState !== WebSocket.OPEN) throw new Error("WebSocket not ready")

//       setIsTranslationEnabled(true)
//       setTranslationStatus("Translation active - receiving transcriptions from teacher")
//       console.log('[DEBUG] startTranslation: Now enabled. Pending:', pendingTranscriptions)

//       // Process any pending transcriptions that were received before translation started
//       if (pendingTranscriptions.length > 0) {
//         console.log(`📤 Processing ${pendingTranscriptions.length} pending transcriptions...`)
//         for (const text of pendingTranscriptions) {
//           if (text.trim()) {
//             console.log(`📤 Sending pending text: \"${text}\"`)
//             await sendTextForTranslation(text.trim())
//           }
//         }
//         setPendingTranscriptions([]) // Clear the queue
//       }

//       // If we have current transcription, send it too
//       if (currentTranscription && currentTranscription.trim()) {
//         console.log(`📤 Sending current transcription: \"${currentTranscription}\"`)
//         await sendTextForTranslation(currentTranscription.trim())
//       }

//     } catch (error) {
//       setTranslationStatus(`Error: ${error.message}`)
//       console.error('[DEBUG] startTranslation error:', error);
//     }
//   }

//   const stopTranslation = async () => {
//     try {
//       setTranslationStatus("Stopping translation...")
//       if (translationSession) {
//         await stopTranslationSession({ session_id: translationSession }).unwrap()
//       }
//       cleanupTranslation()
//       setTranslationStatus("Translation stopped")
//     } catch (error) {
//       setTranslationStatus(`Error: ${error.message}`)
//     }
//   }

//   const connectTranslationWebSocket = async (websocketUrl) => {
//     return new Promise((resolve, reject) => {
//       // Fix WebSocket URL - remove port 8012 and use correct domain
//       let correctedUrl = websocketUrl
//       if (websocketUrl.includes(':8012')) {
//         correctedUrl = websocketUrl.replace(':8012', '')
//       }
//       if (correctedUrl.includes('sasthra.in:8012')) {
//         correctedUrl = correctedUrl.replace('sasthra.in:8012', 'sasthra.in')
//       }

//       console.log('Original WebSocket URL:', websocketUrl)
//       console.log('Corrected WebSocket URL:', correctedUrl)

//       const ws = new WebSocket(correctedUrl)
//       ws.onopen = () => {
//         setTranslationWebSocket(ws)
//         translationWebSocketRef.current = ws
//         setTranslationStatus("WebSocket connected - ready for audio")
//         console.log('[DEBUG] WebSocket onopen fired');
//         resolve()
//       }
//       ws.onmessage = (event) => {
//         const data = JSON.parse(event.data)
//         handleTranslationMessage(data)
//       }
//       ws.onerror = (error) => {
//         setTranslationStatus(`WebSocket error: ${error.message || "Connection failed"}`)
//         console.error('[DEBUG] WebSocket onerror:', error);
//         reject(error)
//       }
//       ws.onclose = () => {
//         setTranslationWebSocket(null)
//         setTranslationStatus("WebSocket disconnected")
//         console.log('[DEBUG] WebSocket onclose fired');
//       }
//     })
//   }

//   const handleTranslationMessage = (data) => {
//     switch (data.type) {
//       case "connection_established":
//         setTranslationStatus("Connected - waiting for teacher's transcription")
//         break
//       case "transcription":
//         setCurrentTranscription(data.text)
//         setTranslationStatus("Processing transcription for translation...")
//         break
//       case "translation":
//         setCurrentTranslation(data.translated_text)
//         setTranslationStatus("Translation received...")
//         break
//       case "translated_audio":
//         setTranslationStatus("Playing translated audio...")
//         addToAudioQueue(data.audio_data)
//         break
//       case "error":
//         setTranslationStatus(`Error: ${data.message}`)
//         break
//       default:
//         break
//     }
//   }





//   const playAudioFromQueue = async (audioDataBase64) => {
//     return new Promise(async (resolve) => {
//       try {
//         setTranslationStatus("Playing audio...")
//         if (!audioDataBase64) {
//           setIsPlayingTranslatedAudio(false)
//           resolve()
//           return
//         }
//         if (!audioContextRef.current) {
//           audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)()
//         }
//         if (audioContextRef.current.state === "suspended") {
//           await audioContextRef.current.resume()
//         }
//         try {
//           const binaryString = atob(audioDataBase64)
//           const audioData = new Uint8Array(binaryString.length)
//           for (let i = 0; i < binaryString.length; i++) {
//             audioData[i] = binaryString.charCodeAt(i)
//           }
//           const audioBuffer = await audioContextRef.current.decodeAudioData(audioData.buffer)
//           const source = audioContextRef.current.createBufferSource()
//           source.buffer = audioBuffer
//           source.connect(audioContextRef.current.destination)
//           currentAudioSourceRef.current = source
//           setIsPlayingTranslatedAudio(true)
//           source.onended = () => {
//             setIsPlayingTranslatedAudio(false)
//             currentAudioSourceRef.current = null
//             setTranslationStatus("Translation active - waiting for teacher's speech")
//             resolve()
//           }
//           source.start()
//         } catch (webAudioError) {
//           try {
//             const audioBlob = new Blob(
//               [
//                 new Uint8Array(
//                   atob(audioDataBase64)
//                     .split("")
//                     .map((c) => c.charCodeAt(0)),
//                 ),
//               ],
//               { type: "audio/wav" },
//             )
//             const audioUrl = URL.createObjectURL(audioBlob)
//             const audio = new Audio(audioUrl)
//             currentHtmlAudioRef.current = audio
//             setIsPlayingTranslatedAudio(true)
//             audio.onended = () => {
//               setIsPlayingTranslatedAudio(false)
//               currentHtmlAudioRef.current = null
//               URL.revokeObjectURL(audioUrl)
//               setTranslationStatus("Translation active - waiting for teacher's speech")
//               resolve()
//             }
//             audio.onerror = () => {
//               setIsPlayingTranslatedAudio(false)
//               currentHtmlAudioRef.current = null
//               URL.revokeObjectURL(audioUrl)
//               setTranslationStatus("Audio playback error")
//               resolve()
//             }
//             await audio.play()
//           } catch (htmlAudioError) {
//             setIsPlayingTranslatedAudio(false)
//             setTranslationStatus(`Audio playback error: ${htmlAudioError.message}`)
//             resolve()
//           }
//         }
//       } catch (error) {
//         setIsPlayingTranslatedAudio(false)
//         setTranslationStatus(`Audio playback error: ${error.message}`)
//         resolve()
//       }
//     })
//   }

//   const handleSendChatMessage = async () => {
//     if (!newMessage.trim() || !currentStream) return
//     const messageData = {
//       session_id: currentStream.session_id,
//       message: newMessage.trim(),
//       sender_id: sessionStorage.getItem("userId"),
//       sender_name: sessionStorage.getItem("name") || "Viewer",
//     }
//     try {
//       await sendChatMessage(messageData).unwrap()
//       setNewMessage("")
//       setTimeout(loadChatHistory, 500)
//     } catch (error) {
//       console.error("❌ VIEWER: Error sending message:", error.message)
//     }
//   }

//   const handleKeyDown = (e) => {
//     if (e.key === "Enter" && !e.shiftKey) {
//       e.preventDefault()
//       handleSendChatMessage()
//     }
//   }

//   const toggleChat = () => {
//     setIsChatOpen(!isChatOpen)
//     if (!isChatOpen) {
//       setUnreadMessages(0)
//     }
//   }

//   const formatMessageTime = (timestamp) => {
//     return new Date(timestamp).toLocaleTimeString([], {
//       hour: "2-digit",
//       minute: "2-digit",
//     })
//   }

//   const getRoleColor = (role) => {
//     switch (role) {
//       case "kota_teacher":
//       case "faculty":
//         return "text-emerald-500"
//       case "student":
//         return "text-blue-500"
//       case "center_counselor":
//         return "text-purple-500"
//       default:
//         return "text-gray-500"
//     }
//   }

//   // Quiz functions
//   const handleOpenQuiz = () => {
//     if (receivedQuizId) {
//       setIsQuizOpen(true)
//       setShowQuizNotification(false)
//     }
//   }

//   const handleCloseQuiz = () => {
//     setIsQuizOpen(false)
//   }

//   const getRoleBadge = (role) => {
//     switch (role) {
//       case "kota_teacher":
//       case "faculty":
//         return "Teacher"
//       case "student":
//         return "Student"
//       case "center_counselor":
//         return "Counselor"
//       default:
//         return "User"
//     }
//   }

//   const formatUptime = (uptime) => {
//     const hours = Math.floor(uptime / 3600)
//     const minutes = Math.floor((uptime % 3600) / 60)
//     const seconds = Math.floor(uptime % 60)
//     if (hours > 0) return `${hours}h ${minutes}m ${seconds}s`
//     if (minutes > 0) return `${minutes}m ${seconds}s`
//     return `${seconds}s`
//   }

//   const formatDateTime = (dateString) => new Date(dateString).toLocaleString()

//   if (isLoading)
//     return (
//       <motion.div
//         initial={{ opacity: 0 }}
//         animate={{ opacity: 1 }}
//         transition={{ duration: 0.5 }}
//         className="flex items-center justify-center min-h-screen bg-gradient-to-br from-gray-900 via-indigo-900 to-gray-900"
//       >
//         <div className="text-center">
//           <motion.div
//             animate={{ rotate: 360 }}
//             transition={{ repeat: Infinity, duration: 2, ease: "linear" }}
//             className="w-20 h-20 border-4 border-indigo-200 border-t-indigo-500 rounded-full mx-auto"
//           ></motion.div>
//           <motion.p
//             initial={{ y: 20, opacity: 0 }}
//             animate={{ y: 0, opacity: 1 }}
//             transition={{ delay: 0.2 }}
//             className="text-white text-lg font-semibold mt-6"
//           >
//             Loading Live Streams...
//           </motion.p>
//         </div>
//       </motion.div>
//     )

//   if (error)
//     return (
//       <motion.div
//         initial={{ opacity: 0 }}
//         animate={{ opacity: 1 }}
//         transition={{ duration: 0.5 }}
//         className="flex items-center justify-center min-h-screen bg-gradient-to-br from-red-50 to-red-100"
//       >
//         <motion.div
//           initial={{ scale: 0.8, opacity: 0 }}
//           animate={{ scale: 1, opacity: 1 }}
//           transition={{ duration: 0.4 }}
//           className="bg-white rounded-2xl shadow-2xl p-8 max-w-md mx-4 border border-red-100"
//         >
//           <div className="flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mx-auto mb-4">
//             <svg className="h-8 w-8 text-red-600" viewBox="0 0 20 20" fill="currentColor">
//               <path
//                 fillRule="evenodd"
//                 d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
//                 clipRule="evenodd"
//               />
//             </svg>
//           </div>
//           <h3 className="text-xl font-bold text-red-800 text-center mb-2">Connection Error</h3>
//           <p className="text-red-600 text-center mb-4">{error.message || "Failed to load active streams"}</p>
//           <motion.button
//             whileHover={{ scale: 1.05 }}
//             whileTap={{ scale: 0.95 }}
//             onClick={() => trigger()}
//             className="w-full bg-red-500 hover:bg-red-600 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200"
//           >
//             Try Again
//           </motion.button>
//         </motion.div>
//       </motion.div>
//     )

//   const activeStreams = data?.active_streams || []

//   if (isViewingStream && currentStream)
//     return (
//       <motion.div
//         initial={{ opacity: 0 }}
//         animate={{ opacity: 1 }}
//         transition={{ duration: 0.5 }}
//         className="min-h-screen bg-gradient-to-br from-gray-900 via-indigo-900 to-gray-900"
//       >
//         {/* Header */}
//         <motion.div
//           initial={{ y: -50, opacity: 0 }}
//           animate={{ y: 0, opacity: 1 }}
//           transition={{ duration: 0.5 }}
//           className="bg-gradient-to-r from-gray-800 to-indigo-900 text-white p-4 shadow-lg border-b border-gray-700"
//         >
//           <div className="flex items-center justify-between max-w-7xl mx-auto">
//             <div className="flex items-center space-x-4">
//               <motion.button
//                 whileHover={{ scale: 1.05 }}
//                 whileTap={{ scale: 0.95 }}
//                 onClick={handleLeaveStream}
//                 className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-6 py-3 rounded-lg font-semibold shadow-md flex items-center space-x-2"
//               >
//                 <svg
//                   className="w-5 h-5"
//                   fill="none"
//                   viewBox="0 0 24 24"
//                   stroke="currentColor"
//                 >
//                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
//                 </svg>
//                 <span>Leave Stream</span>
//               </motion.button>
//               <div className="border-l border-gray-600 pl-4">
//                 <h1 className="text-2xl font-bold bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent">
//                   Live Stream
//                 </h1>
//                 <p className="text-sm text-gray-300">Session: {currentStream.session_id}</p>
//               </div>
//             </div>
//             <div className="flex items-center space-x-6">
//               <div className="flex items-center bg-gray-800/50 rounded-lg px-4 py-2">
//                 <motion.div
//                   animate={{ scale: livekitConnected ? [1, 1.2, 1] : 1 }}
//                   transition={{ repeat: livekitConnected ? Infinity : 0, duration: 1.5 }}
//                   className={`w-3 h-3 rounded-full mr-3 ${livekitConnected ? "bg-green-400" : "bg-red-400"}`}
//                 ></motion.div>
//                 <span className="text-sm font-medium">{connectionStatus || "Connecting..."}</span>
//               </div>
//               <div className="flex items-center bg-gray-800/50 rounded-lg px-4 py-2">
//                 <svg className="w-4 h-4 mr-2 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                   <path
//                     strokeLinecap="round"
//                     strokeLinejoin="round"
//                     strokeWidth={2}
//                     d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
//                   />
//                 </svg>
//                 <span className="text-sm font-medium">{participants.length + 1} Participants</span>
//               </div>

//               {/* Debug Quiz ID Display */}
//               {process.env.NODE_ENV === 'development' && (
//                 <div className="bg-yellow-500 text-black px-2 py-1 rounded text-xs">
//                   Quiz ID: {receivedQuizId || 'None'}
//                 </div>
//               )}

//               {/* Quiz Notification and Open Quiz Button */}
//               {receivedQuizId && (
//                 <motion.button
//                   initial={{ scale: 0 }}
//                   animate={{ scale: 1 }}
//                   whileHover={{ scale: 1.05 }}
//                   whileTap={{ scale: 0.95 }}
//                   onClick={handleOpenQuiz}
//                   className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white px-6 py-3 rounded-lg font-semibold shadow-md flex items-center space-x-2 animate-pulse"
//                 >
//                   <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                     <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
//                   </svg>
//                   <span>Open Quiz (ID: {receivedQuizId})</span>
//                 </motion.button>
//               )}

//               {/* Always show manual quiz button for testing */}
//               <motion.button
//                 whileHover={{ scale: 1.05 }}
//                 whileTap={{ scale: 0.95 }}
//                 onClick={() => setIsQuizOpen(true)}
//                 className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-4 py-2 rounded-lg font-semibold shadow-md flex items-center space-x-2"
//               >
//                 <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
//                 </svg>
//                 <span>Manual Quiz</span>
//               </motion.button>
//             </div>
//           </div>
//         </motion.div>

//         <div className="flex h-[calc(100vh-80px)] max-w-7xl mx-auto">
//           {/* Video Area */}
//           <motion.div
//             initial={{ opacity: 0 }}
//             animate={{ opacity: 1 }}
//             transition={{ duration: 0.5 }}
//             className="flex-1 relative bg-black rounded-lg overflow-hidden shadow-2xl"
//           >
//             <div className="w-full h-full flex items-center justify-center relative">
//               <video
//                 ref={screenVideoRef}
//                 autoPlay
//                 playsInline
//                 className="w-full h-full object-contain"
//               />
//               {!hasScreenShare && hasCameraTrack && (
//                 <video
//                   ref={cameraMainRef}
//                   autoPlay
//                   playsInline
//                   className="w-full h-full object-contain absolute inset-0"
//                 />
//               )}
//               {remoteVideoTracks.length === 0 && (
//                 <motion.div
//                   initial={{ opacity: 0 }}
//                   animate={{ opacity: 1 }}
//                   transition={{ duration: 0.5 }}
//                   className="absolute inset-0 flex items-center justify-center text-white bg-gradient-to-br from-gray-900/70 to-black/70"
//                 >
//                   <div className="text-center">
//                     <motion.div
//                       animate={{ rotate: 360 }}
//                       transition={{ repeat: Infinity, duration: 2, ease: "linear" }}
//                       className="w-20 h-20 border-4 border-indigo-200 border-t-indigo-500 rounded-full mx-auto mb-4"
//                     ></motion.div>
//                     <motion.p
//                       initial={{ y: 20, opacity: 0 }}
//                       animate={{ y: 0, opacity: 1 }}
//                       transition={{ delay: 0.2 }}
//                       className="text-xl font-semibold"
//                     >
//                       Waiting for stream...
//                     </motion.p>
//                     <p className="text-gray-300 flex items-center justify-center mt-2">
//                       <motion.span
//                         animate={{ scale: livekitConnected ? [1, 1.2, 1] : 1 }}
//                         transition={{ repeat: livekitConnected ? Infinity : 0, duration: 1.5 }}
//                         className={`w-2 h-2 rounded-full mr-2 ${livekitConnected ? "bg-green-400" : "bg-red-400"}`}
//                       ></motion.span>
//                       {livekitConnected ? "Connected to room" : "Connecting..."}
//                     </p>
//                   </div>
//                 </motion.div>
//               )}
//             </div>
//             {hasScreenShare && hasCameraTrack && (
//               <motion.div
//                 initial={{ scale: 0.8, opacity: 0 }}
//                 animate={{ scale: 1, opacity: 1 }}
//                 transition={{ duration: 0.4 }}
//                 className="absolute bottom-6 right-6 w-72 h-52 bg-gray-900 rounded-lg overflow-hidden border border-gray-600 shadow-lg"
//               >
//                 <video ref={cameraVideoRef} autoPlay playsInline muted className="w-full h-full object-cover" />
//                 <div className="absolute bottom-2 left-2 bg-black/60 text-white text-xs px-2 py-1 rounded">
//                   Teacher Camera
//                 </div>
//               </motion.div>
//             )}
//             {!isChatOpen && (
//               <motion.button
//                 whileHover={{ scale: 1.1 }}
//                 whileTap={{ scale: 0.9 }}
//                 onClick={toggleChat}
//                 className="absolute bottom-6 left-6 bg-indigo-500 hover:bg-indigo-600 text-white p-3 rounded-full shadow-lg"
//               >
//                 <svg
//                   className="w-6 h-6"
//                   fill="none"
//                   viewBox="0 0 24 24"
//                   stroke="currentColor"
//                 >
//                   <path
//                     strokeLinecap="round"
//                     strokeLinejoin="round"
//                     strokeWidth={2}
//                     d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.405L3 21l2.595-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"
//                   />
//                 </svg>
//                 {unreadMessages > 0 && (
//                   <motion.span
//                     initial={{ scale: 0 }}
//                     animate={{ scale: 1 }}
//                     className="absolute -top-2 -right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full"
//                   >
//                     {unreadMessages}
//                   </motion.span>
//                 )}
//               </motion.button>
//             )}
//           </motion.div>

//           {/* Chat Overlay */}
//           <AnimatePresence>
//             {isChatOpen && (
//               <motion.div
//                 initial={{ x: '100%' }}
//                 animate={{ x: 0 }}
//                 exit={{ x: '100%' }}
//                 transition={{ type: 'spring', stiffness: 300, damping: 30 }}
//                 className="absolute right-0 top-0 bottom-0 w-96 bg-gray-800 text-white shadow-lg border-l border-gray-700 z-10"
//               >
//                 <div className="p-4 border-b border-gray-700 flex justify-between items-center">
//                   <h3 className="text-lg font-semibold">Live Chat</h3>
//                   <button onClick={toggleChat} className="text-gray-400 hover:text-white">
//                     <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                       <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
//                     </svg>
//                   </button>
//                 </div>
//                 <div className="h-[calc(100%-120px)] overflow-y-auto p-4">
//                   {isLoadingHistory ? (
//                     <div className="flex items-center justify-center h-full">
//                       <motion.div
//                         animate={{ rotate: 360 }}
//                         transition={{ repeat: Infinity, duration: 1 }}
//                         className="w-6 h-6 border-2 border-indigo-200 border-t-indigo-500 rounded-full"
//                       ></motion.div>
//                     </div>
//                   ) : chatMessages.length === 0 ? (
//                     <div className="flex items-center justify-center h-full text-gray-400">
//                       No messages yet
//                     </div>
//                   ) : (
//                     <AnimatePresence>
//                       {chatMessages.map((message, index) => (
//                         <motion.div
//                           key={`${message.id || "msg"}-${index}-${message.timestamp || Date.now()}`}
//                           initial={{ opacity: 0, y: 10 }}
//                           animate={{ opacity: 1, y: 0 }}
//                           exit={{ opacity: 0, y: -10 }}
//                           className={`p-2 hover:bg-gray-700/30 rounded mb-2 ${
//                             message.message && message.message.includes('QUIZ_START:')
//                               ? 'bg-purple-900/50 border border-purple-500'
//                               : ''
//                           }`}
//                         >
//                           <div className="flex items-start justify-between">
//                             <div className="flex-1">
//                               <div className="flex items-center space-x-2 mb-1">
//                                 <span className={`text-sm font-medium ${getRoleColor(message.sender_role)}`}>
//                                   {message.sender_name}
//                                 </span>
//                                 <span className="text-xs bg-gray-700 px-2 py-1 rounded">
//                                   {getRoleBadge(message.sender_role)}
//                                 </span>
//                                 {message.message && message.message.includes('QUIZ_START:') && (
//                                   <span className="text-xs bg-purple-500 text-white px-2 py-1 rounded">
//                                     QUIZ
//                                   </span>
//                                 )}
//                               </div>
//                               <p className="text-sm text-gray-200">{message.message}</p>
//                             </div>
//                             <span className="text-xs text-gray-400">{formatMessageTime(message.timestamp)}</span>
//                           </div>
//                         </motion.div>
//                       ))}
//                     </AnimatePresence>
//                   )}
//                 </div>
//                 <div className="absolute bottom-0 left-0 right-0 p-4 bg-gray-800 border-t border-gray-700">
//                   <div className="flex space-x-2">
//                     <input
//                       type="text"
//                       value={newMessage}
//                       onChange={(e) => setNewMessage(e.target.value)}
//                       onKeyDown={handleKeyDown}
//                       placeholder="Type a message..."
//                       className="flex-1 bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-sm text-white"
//                     />
//                     <motion.button
//                       whileHover={{ scale: 1.05 }}
//                       whileTap={{ scale: 0.95 }}
//                       onClick={handleSendChatMessage}
//                       disabled={!newMessage.trim() || isSendingMessage}
//                       className="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-lg"
//                     >
//                       {isSendingMessage ? (
//                         <motion.div
//                           animate={{ rotate: 360 }}
//                           transition={{ repeat: Infinity, duration: 1 }}
//                           className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
//                         ></motion.div>
//                       ) : (
//                         "Send"
//                       )}
//                     </motion.button>
//                   </div>
//                 </div>
//               </motion.div>
//             )}
//           </AnimatePresence>

//           {/* Sidebar */}
//           <motion.div
//             initial={{ x: 100, opacity: 0 }}
//             animate={{ x: 0, opacity: 1 }}
//             transition={{ duration: 0.5 }}
//             className="w-20 bg-gray-800 text-white overflow-y-auto shadow-lg border-l border-gray-700"
//           >
//             <div className="flex flex-col items-center py-4 space-y-4">
//               {/* Stream Information */}
//               <motion.button
//                 whileHover={{ scale: 1.1 }}
//                 whileTap={{ scale: 0.9 }}
//                 onClick={() => setActiveSidebarTab('info')}
//                 className={`p-3 rounded-lg ${activeSidebarTab === 'info' ? 'bg-indigo-600' : 'bg-gray-700 hover:bg-gray-600'}`}
//               >
//                 <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
//                 </svg>
//               </motion.button>

//               {/* Features */}
//               <motion.button
//                 whileHover={{ scale: 1.1 }}
//                 whileTap={{ scale: 0.9 }}
//                 onClick={() => setActiveSidebarTab('features')}
//                 className={`p-3 rounded-lg ${activeSidebarTab === 'features' ? 'bg-indigo-600' : 'bg-gray-700 hover:bg-gray-600'}`}
//               >
//                 <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
//                 </svg>
//               </motion.button>

//               {/* Participants */}
//               <motion.button
//                 whileHover={{ scale: 1.1 }}
//                 whileTap={{ scale: 0.9 }}
//                 onClick={() => setActiveSidebarTab('participants')}
//                 className={`p-3 rounded-lg ${activeSidebarTab === 'participants' ? 'bg-indigo-600' : 'bg-gray-700 hover:bg-gray-600'}`}
//               >
//                 <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
//                 </svg>
//               </motion.button>

//               {/* Connection Status */}
//               <motion.button
//                 whileHover={{ scale: 1.1 }}
//                 whileTap={{ scale: 0.9 }}
//                 onClick={() => setActiveSidebarTab('connection')}
//                 className={`p-3 rounded-lg ${activeSidebarTab === 'connection' ? 'bg-indigo-600' : 'bg-gray-700 hover:bg-gray-600'}`}
//               >
//                 <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
//                 </svg>
//               </motion.button>

//               {/* Translation */}
//               <motion.button
//                 whileHover={{ scale: 1.1 }}
//                 whileTap={{ scale: 0.9 }}
//                 onClick={() => setActiveSidebarTab('translation')}
//                 className={`p-3 rounded-lg ${activeSidebarTab === 'translation' ? 'bg-indigo-600' : 'bg-gray-700 hover:bg-gray-600'}`}
//               >
//                 <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
//                 </svg>
//               </motion.button>
//             </div>
//           </motion.div>

//           {/* Sidebar Content */}
//           <motion.div
//             initial={{ x: 100, opacity: 0 }}
//             animate={{ x: 0, opacity: 1 }}
//             transition={{ duration: 0.5 }}
//             className="w-80 bg-gray-800 text-white overflow-y-auto shadow-lg border-l border-gray-700"
//           >
//             <div className="p-6 space-y-6">
//               {/* Stream Information */}
//               {activeSidebarTab === 'info' && (
//                 <motion.div
//                   initial={{ y: 20, opacity: 0 }}
//                   animate={{ y: 0, opacity: 1 }}
//                   transition={{ delay: 0.1 }}
//                   className="bg-gray-900 rounded-lg p-4 shadow"
//                 >
//                   <h3 className="text-lg font-semibold mb-3">Stream Information</h3>
//                   <div className="space-y-2 text-sm">
//                     <div className="flex justify-between bg-gray-700/50 p-2 rounded">
//                       <span className="text-gray-300">Teacher ID:</span>
//                       <span className="font-medium text-indigo-400">{currentStream.teacher_id}</span>
//                     </div>
//                     <div className="flex justify-between bg-gray-700/50 p-2 rounded">
//                       <span className="text-gray-300">Quality:</span>
//                       <span className="font-medium text-indigo-400">{currentStream.quality || "Standard"}</span>
//                     </div>
//                     <div className="flex justify-between bg-gray-700/50 p-2 rounded">
//                       <span className="text-gray-300">Viewers:</span>
//                       <span className="font-medium text-indigo-400">{currentStream.viewer_count || 0}</span>
//                     </div>
//                   </div>
//                 </motion.div>
//               )}

//               {/* Features */}
//               {activeSidebarTab === 'features' && (
//                 <motion.div
//                   initial={{ y: 20, opacity: 0 }}
//                   animate={{ y: 0, opacity: 1 }}
//                   transition={{ delay: 0.2 }}
//                   className="bg-gray-900 rounded-lg p-4 shadow"
//                 >
//                   <h3 className="text-lg font-semibold mb-3">Features</h3>
//                   <div className="space-y-2">
//                     {currentStream.features?.screen_sharing && (
//                       <div className="flex items-center bg-indigo-500/10 p-2 rounded">
//                         <span className="text-indigo-400">Screen Sharing Active</span>
//                       </div>
//                     )}
//                     {currentStream.features?.chat_enabled && (
//                       <div className="flex items-center bg-indigo-500/10 p-2 rounded">
//                         <span className="text-indigo-400">Chat Available</span>
//                       </div>
//                     )}
//                     {currentStream.features?.recording_enabled && (
//                       <div className="flex items-center bg-indigo-500/10 p-2 rounded">
//                         <span className="text-indigo-400">Recording Active</span>
//                       </div>
//                     )}
//                   </div>
//                 </motion.div>
//               )}

//               {/* Participants */}
//               {activeSidebarTab === 'participants' && (
//                 <motion.div
//                   initial={{ y: 20, opacity: 0 }}
//                   animate={{ y: 0, opacity: 1 }}
//                   transition={{ delay: 0.3 }}
//                   className="bg-gray-900 rounded-lg p-4 shadow"
//                 >
//                   <h3 className="text-lg font-semibold mb-3">Participants ({participants.length + 1})</h3>
//                   <div className="space-y-2 max-h-60 overflow-y-auto">
//                     <div className="flex items-center bg-indigo-500/10 p-2 rounded">
//                       <div className="w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center text-sm font-bold mr-2">
//                         T
//                       </div>
//                       <div className="flex-1">
//                         <div className="font-medium text-indigo-400">Teacher</div>
//                         <div className="text-xs text-gray-400">Host</div>
//                       </div>
//                     </div>
//                     <AnimatePresence>
//                       {participants.map((participant, index) => (
//                         <motion.div
//                           key={participant.identity || index}
//                           initial={{ opacity: 0, y: 10 }}
//                           animate={{ opacity: 1, y: 0 }}
//                           exit={{ opacity: 0, y: -10 }}
//                           className="flex items-center bg-indigo-500/10 p-2 rounded"
//                         >
//                           <div className="w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center text-sm font-bold mr-2">
//                             {participant.identity?.charAt(0)?.toUpperCase() || "U"}
//                           </div>
//                           <div className="flex-1">
//                             <div className="font-medium text-indigo-400">{participant.identity || "Unknown"}</div>
//                             <div className="text-xs text-gray-400">Viewer</div>
//                           </div>
//                         </motion.div>
//                       ))}
//                     </AnimatePresence>
//                   </div>
//                 </motion.div>
//               )}

//               {/* Connection Status */}
//               {activeSidebarTab === 'connection' && (
//                 <motion.div
//                   initial={{ y: 20, opacity: 0 }}
//                   animate={{ y: 0, opacity: 1 }}
//                   transition={{ delay: 0.4 }}
//                   className="bg-gray-900 rounded-lg p-4 shadow"
//                 >
//                   <h3 className="text-lg font-semibold mb-3">Connection Status</h3>
//                   <div className="space-y-2">
//                     <div className="flex items-center bg-indigo-500/10 p-2 rounded">
//                       <motion.div
//                         animate={{ scale: livekitConnected ? [1, 1.2, 1] : 1 }}
//                         transition={{ repeat: livekitConnected ? Infinity : 0, duration: 1.5 }}
//                         className={`w-3 h-3 rounded-full mr-2 ${livekitConnected ? "bg-green-400" : "bg-red-400"}`}
//                       ></motion.div>
//                       <span className="font-medium">{livekitConnected ? "Connected" : "Disconnected"}</span>
//                     </div>
//                     <div className="grid grid-cols-2 gap-2 text-sm">
//                       <div className="bg-gray-700/50 p-2 rounded text-center">
//                         <div className="text-gray-400">Video Tracks</div>
//                         <div className="font-bold text-indigo-400">{remoteVideoTracks.length}</div>
//                       </div>
//                       <div className="bg-gray-700/50 p-2 rounded text-center">
//                         <div className="text-gray-400">Audio Tracks</div>
//                         <div className="font-bold text-indigo-400">{remoteAudioTracks.length}</div>
//                       </div>
//                     </div>
//                   </div>
//                 </motion.div>
//               )}

//               {/* Translation */}
//               {activeSidebarTab === 'translation' && (
//                 <motion.div
//                   initial={{ y: 20, opacity: 0 }}
//                   animate={{ y: 0, opacity: 1 }}
//                   transition={{ delay: 0.5 }}
//                   className="bg-gray-900 rounded-lg p-4 shadow"
//                 >
//                   <h3 className="text-lg font-semibold mb-3">Live Translation</h3>
//                   <div className="space-y-4">
//                     <div className="flex items-center justify-between">
//                       <span className="text-gray-300 font-medium">Enable Translation</span>
//                       <div className="flex gap-2">
//                         <motion.button
//                           whileHover={{ scale: 1.05 }}
//                           whileTap={{ scale: 0.95 }}
//                           onClick={debugCheckTextStreams}
//                           className="px-2 py-1 text-xs bg-yellow-500 hover:bg-yellow-600 text-black rounded"
//                         >
//                           Debug
//                         </motion.button>
//                         <motion.button
//                           whileHover={{ scale: 1.05 }}
//                           whileTap={{ scale: 0.95 }}
//                           onClick={isTranslationEnabled ? stopTranslation : startTranslation}
//                           className={`px-4 py-2 rounded-lg font-semibold ${
//                             isTranslationEnabled
//                               ? "bg-red-500 hover:bg-red-600 text-white"
//                               : "bg-indigo-500 hover:bg-indigo-600 text-white"
//                           }`}
//                         >
//                           {isTranslationEnabled ? "Stop" : "Start"}
//                         </motion.button>
//                       </div>
//                     </div>
//                     <div className="grid grid-cols-2 gap-3">
//                       <div>
//                         <label className="block text-xs text-gray-400 mb-1">From</label>
//                         <select
//                           value={sourceLanguage}
//                           onChange={(e) => setSourceLanguage(e.target.value)}
//                           disabled={isTranslationEnabled}
//                           className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-sm text-white disabled:opacity-50"
//                         >
//                           {availableLanguages.map((lang) => (
//                             <option key={lang.code} value={lang.code}>
//                               {lang.flag} {lang.name}
//                             </option>
//                           ))}
//                         </select>
//                       </div>
//                       <div>
//                         <label className="block text-xs text-gray-400 mb-1">To</label>
//                         <select
//                           value={targetLanguage}
//                           onChange={(e) => setTargetLanguage(e.target.value)}
//                           disabled={isTranslationEnabled}
//                           className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-sm text-white disabled:opacity-50"
//                         >
//                           {availableLanguages.map((lang) => (
//                             <option key={lang.code} value={lang.code}>
//                               {lang.flag} {lang.name}
//                             </option>
//                           ))}
//                         </select>
//                       </div>
//                     </div>
//                     <div className="space-y-2">
//                       <div className="flex items-center bg-indigo-500/10 p-2 rounded">
//                         <motion.div
//                           animate={{ scale: isTranslationEnabled ? [1, 1.2, 1] : 1 }}
//                           transition={{ repeat: isTranslationEnabled ? Infinity : 0, duration: 1.5 }}
//                           className={`w-3 h-3 rounded-full mr-2 ${isTranslationEnabled ? "bg-green-400" : "bg-gray-400"}`}
//                         ></motion.div>
//                         <span className="text-sm">
//                           {translationStatus || (isTranslationEnabled ? "Translation Active" : "Translation Inactive")}
//                         </span>
//                       </div>
//                       {isPlayingTranslatedAudio && (
//                         <div className="flex items-center bg-indigo-500/10 p-2 rounded">
//                           <motion.div
//                             animate={{ scale: [1, 1.2, 1] }}
//                             transition={{ repeat: Infinity, duration: 1 }}
//                             className="w-3 h-3 bg-indigo-400 rounded-full mr-2"
//                           ></motion.div>
//                           <span className="text-sm">Playing Translated Audio</span>
//                         </div>
//                       )}
//                       {audioQueueLength > 0 && (
//                         <div className="flex items-center bg-yellow-500/10 p-2 rounded">
//                           <motion.div
//                             animate={{ scale: [1, 1.2, 1] }}
//                             transition={{ repeat: Infinity, duration: 1 }}
//                             className="w-3 h-3 bg-yellow-400 rounded-full mr-2"
//                           ></motion.div>
//                           <span className="text-sm">Audio Queue: {audioQueueLength} pending</span>
//                         </div>
//                       )}
//                     </div>
//                     {isTranslationEnabled && (
//                       <div className="bg-gray-700/50 rounded-lg p-2 text-xs">
//                         <div className="flex justify-between">
//                           <span className="text-gray-400">WebSocket:</span>
//                           <span
//                             className={
//                               translationWebSocket
//                                 ? translationWebSocket.readyState === 1
//                                   ? "text-green-400"
//                                   : translationWebSocket.readyState === 0
//                                     ? "text-yellow-400"
//                                     : "text-red-400"
//                                 : "text-red-400"
//                             }
//                           >
//                             {translationWebSocket
//                               ? translationWebSocket.readyState === 1
//                                 ? "Connected"
//                                 : translationWebSocket.readyState === 0
//                                   ? "Connecting"
//                                   : "Closed"
//                               : "Not Created"}
//                           </span>
//                         </div>
//                       </div>
//                     )}
//                     {currentTranscription && (
//                       <motion.div
//                         initial={{ opacity: 0 }}
//                         animate={{ opacity: 1 }}
//                         className="bg-gray-700/50 rounded-lg p-3"
//                       >
//                         <div className="text-xs text-indigo-400 mb-1">Original ({sourceLanguage.toUpperCase()}):</div>
//                         <div className="text-sm">{currentTranscription}</div>
//                       </motion.div>
//                     )}
//                     {currentTranslation && (
//                       <motion.div
//                         initial={{ opacity: 0 }}
//                         animate={{ opacity: 1 }}
//                         className="bg-indigo-900/30 rounded-lg p-3"
//                       >
//                         <div className="text-xs text-indigo-400 mb-1">Translation ({targetLanguage.toUpperCase()}):</div>
//                         <div className="text-sm">{currentTranslation}</div>
//                       </motion.div>
//                     )}
//                     <div className="flex items-center bg-blue-500/10 p-2 rounded text-xs">
//                       <span>Translation receives transcribed text from teacher via LiveKit</span>
//                     </div>
//                     {isReceivingTranscription && (
//                       <motion.div
//                         initial={{ opacity: 0 }}
//                         animate={{ opacity: 1 }}
//                         className="bg-green-500/10 rounded-lg p-2"
//                       >
//                         <div className="flex items-center">
//                           <motion.div
//                             animate={{ scale: [1, 1.2, 1] }}
//                             transition={{ repeat: Infinity, duration: 1 }}
//                             className="w-2 h-2 bg-green-400 rounded-full mr-2"
//                           />
//                           <span className="text-xs text-green-400">Receiving live transcription from teacher</span>
//                         </div>
//                       </motion.div>
//                     )}

//                     {/* Debug info */}
//                     <div className="bg-gray-800/50 rounded-lg p-2 text-xs">
//                       <div className="text-gray-400 mb-1">Debug Info:</div>
//                       <div className="space-y-1">
//                         <div>Room Connected: {livekitConnected ? "✅" : "❌"}</div>
//                         <div>Participants: {participants.length}</div>
//                         <div>Receiving Text: {isReceivingTranscription ? "✅" : "❌"}</div>
//                         <div>Current Text: {currentTranscription ? "✅" : "❌"}</div>
//                       </div>
//                     </div>
//                   </div>
//                 </motion.div>
//               )}
//             </div>
//           </motion.div>
//         </div>

//         {/* Quiz Overlay */}
//         <AnimatePresence>
//           {isQuizOpen && receivedQuizId && (
//             <motion.div
//               initial={{ opacity: 0 }}
//               animate={{ opacity: 1 }}
//               exit={{ opacity: 0 }}
//               className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-50"
//             >
//               <motion.div
//                 initial={{ scale: 0.9, opacity: 0 }}
//                 animate={{ scale: 1, opacity: 1 }}
//                 exit={{ scale: 0.9, opacity: 0 }}
//                 className="w-full h-full max-w-7xl mx-4"
//               >
//                 <LiveQuiz quizId={receivedQuizId} onClose={handleCloseQuiz} />
//               </motion.div>
//             </motion.div>
//           )}
//         </AnimatePresence>
//       </motion.div>
//     )

//   return (
//     <motion.div
//       initial={{ opacity: 0 }}
//       animate={{ opacity: 1 }}
//       transition={{ duration: 0.5 }}
//       className="min-h-screen bg-gradient-to-br from-gray-50 to-indigo-100"
//     >
//       <div className="max-w-7xl mx-auto px-4 py-8">
//         <motion.div
//           initial={{ y: -50, opacity: 0 }}
//           animate={{ y: 0, opacity: 1 }}
//           transition={{ duration: 0.5 }}
//           className="text-center mb-12"
//         >
//           <h1 className="text-4xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
//             Live Streams
//           </h1>
//           <p className="text-lg text-gray-600 mt-2">Join active streaming sessions</p>
//           <motion.button
//             whileHover={{ scale: 1.05 }}
//             whileTap={{ scale: 0.95 }}
//             onClick={() => trigger()}
//             className="mt-4 bg-indigo-500 hover:bg-indigo-600 text-white px-6 py-3 rounded-lg font-semibold"
//           >
//             Refresh Streams
//           </motion.button>
//         </motion.div>

//         {activeStreams.length === 0 ? (
//           <motion.div
//             initial={{ scale: 0.8, opacity: 0 }}
//             animate={{ scale: 1, opacity: 1 }}
//             transition={{ duration: 0.4 }}
//             className="text-center py-20"
//           >
//             <div className="bg-white rounded-lg shadow-lg p-8 max-w-md mx-auto">
//               <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                 <path
//                   strokeLinecap="round"
//                   strokeLinejoin="round"
//                   strokeWidth={1}
//                   d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
//                 />
//               </svg>
//               <h3 className="text-xl font-semibold text-gray-900 mb-2">No Active Streams</h3>
//               <p className="text-gray-500">There are currently no live streaming sessions available.</p>
//               <motion.button
//                 whileHover={{ scale: 1.05 }}
//                 whileTap={{ scale: 0.95 }}
//                 onClick={() => trigger()}
//                 className="mt-4 bg-indigo-500 hover:bg-indigo-600 text-white px-6 py-3 rounded-lg font-semibold"
//               >
//                 Check Again
//               </motion.button>
//             </div>
//           </motion.div>
//         ) : (
//           <motion.div
//             className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
//             initial={{ opacity: 0 }}
//             animate={{ opacity: 1 }}
//             transition={{ staggerChildren: 0.2 }}
//           >
//             {activeStreams.map((stream, index) => (
//               <motion.div
//                 key={stream.session_id || index}
//                 initial={{ y: 20, opacity: 0 }}
//                 animate={{ y: 0, opacity: 1 }}
//                 transition={{ delay: index * 0.1 }}
//                 className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow"
//               >
//                 <div className="flex items-center justify-between mb-4">
//                   <span className="text-sm font-semibold text-indigo-600">Session: {stream.session_id}</span>
//                   <span className="bg-indigo-100 text-indigo-600 px-2 py-1 rounded text-xs">
//                     {stream.quality || "Standard"}
//                   </span>
//                 </div>
//                 <p className="text-gray-600 text-sm mb-4">Teacher ID: {stream.teacher_id}</p>
//                 <div className="grid grid-cols-2 gap-4 mb-4">
//                   <div className="text-center bg-indigo-50 p-3 rounded">
//                     <div className="text-lg font-bold text-indigo-600">{stream.viewer_count || 0}</div>
//                     <div className="text-xs text-gray-500">Viewers</div>
//                   </div>
//                   <div className="text-center bg-indigo-50 p-3 rounded">
//                     <div className="text-lg font-bold text-indigo-600">{formatUptime(stream.uptime || 0)}</div>
//                     <div className="text-xs text-gray-500">Uptime</div>
//                   </div>
//                 </div>
//                 <motion.button
//                   whileHover={{ scale: 1.05 }}
//                   whileTap={{ scale: 0.95 }}
//                   onClick={() => handleJoinStream(stream)}
//                   disabled={isJoining}
//                   className="w-full bg-indigo-500 hover:bg-indigo-600 text-white py-3 rounded-lg font-semibold"
//                 >
//                   {isJoining ? "Joining..." : "Join Stream"}
//                 </motion.button>
//               </motion.div>
//             ))}
//           </motion.div>
//         )}
//       </div>

//       {/* Toast Notifications */}
//       <ToastContainer
//         position="top-right"
//         autoClose={5000}
//         hideProgressBar={false}
//         newestOnTop={false}
//         closeOnClick
//         rtl={false}
//         pauseOnFocusLoss
//         draggable
//         pauseOnHover
//         theme="dark"
//       />
//     </motion.div>
//   )
// }

// export default CenterTraineeLiveViewer
