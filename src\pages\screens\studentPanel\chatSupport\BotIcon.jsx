
"use client";

import { useState, useEffect } from "react";
import botIcon from "../../../../assets/botOne.gif";

const EnhancedBotIcon = ({
  onClick,
  hasNotification = false,
  isActive = false,
  messageCount = 0,
  isTyping = false,
  isVisible = true,
}) => {
  const [isClicked, setIsClicked] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [isClosing, setIsClosing] = useState(false);

  // Debug isVisible prop
  useEffect(() => {
    console.log("EnhancedBotIcon: isVisible =", isVisible);
  }, [isVisible]);

  const handleClick = () => {
    setIsClicked(true);
    setIsClosing(true);
    setTimeout(() => {
      setIsClicked(false);
      if (onClick) onClick();
    }, 300);
  };

  if (!isVisible && !isClosing) return null;

  return (
    <div className="fixed bottom-12 right-6 z-50">
      {showTooltip && !isClosing && (
        <div className="absolute bottom-full right-0 mb-3 px-4 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white text-sm rounded-xl shadow-2xl whitespace-nowrap transform transition-all duration-200 animate-bounce-in">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span className="font-medium">{isTyping ? "Bot is typing..." : "Need help? Click to chat!"}</span>
          </div>
          <div className="absolute top-full right-6 w-0 h-0 border-l-4 border-r-4 border-t-6 border-transparent border-t-blue-600"></div>
        </div>
      )}
      <div
        className={`
          relative cursor-pointer rounded-full transition-all duration-500 ease-out
          ${
            isActive
              ? "bg-gradient-to-br from-blue-500 to-purple-600 shadow-2xl shadow-blue-500/40 p-3"
              : "bg-white shadow-xl hover:shadow-2xl p-3"
          }
          ${isClosing ? "animate-close-bot" : isClicked ? "scale-90" : isHovered ? "scale-110" : "scale-100"}
          ${!isActive && !isClosing ? "animate-gentle-bounce" : ""}
        `}
        onClick={handleClick}
        onMouseEnter={() => {
          if (!isClosing) {
            setShowTooltip(true);
            setIsHovered(true);
          }
        }}
        onMouseLeave={() => {
          setShowTooltip(false);
          setIsHovered(false);
        }}
      >
        {(hasNotification || messageCount > 0) && !isClosing && (
          <div className="absolute -top-2 -right-2 min-w-6 h-6 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center animate-pulse shadow-lg">
            <span className="text-white text-xs font-bold px-1">{messageCount > 99 ? "99+" : messageCount || "!"}</span>
          </div>
        )}
        {isTyping && !isClosing && (
          <div className="absolute -top-1 -left-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
            <div className="flex space-x-0.5">
              <div className="w-1 h-1 bg-white rounded-full animate-bounce" style={{ animationDelay: "0ms" }}></div>
              <div className="w-1 h-1 bg-white rounded-full animate-bounce" style={{ animationDelay: "150ms" }}></div>
              <div className="w-1 h-1 bg-white rounded-full animate-bounce" style={{ animationDelay: "300ms" }}></div>
            </div>
          </div>
        )}
        {isClicked && !isClosing && (
          <div className="absolute inset-0 rounded-full bg-blue-400 opacity-30 animate-ping"></div>
        )}
        {isClosing && (
          <>
            <div className="absolute inset-0 rounded-full bg-blue-400 opacity-40 animate-ping-fast"></div>
            <div
              className="absolute inset-0 rounded-full bg-purple-400 opacity-30 animate-ping-fast"
              style={{ animationDelay: "0.1s" }}
            ></div>
          </>
        )}
        <div
          className={`relative ${isActive && !isClosing ? "animate-spin-slow" : ""} ${isClosing ? "animate-spin-fast" : ""}`}
        >
          <img
            src={botIcon || "/placeholder.svg"}
            alt="AI Assistant"
            className={`
              h-16 w-16 transition-all duration-300 rounded-full
              ${isActive ? "brightness-110 contrast-110" : "brightness-100"}
              ${isHovered && !isClosing ? "drop-shadow-lg" : ""}
              ${isClosing ? "brightness-125 contrast-125" : ""}
            `}
          />
          {isActive && !isClosing && (
            <div className="absolute inset-0 rounded-full border-2 border-white/50 animate-pulse"></div>
          )}
          {isClosing && (
            <div className="absolute inset-0 rounded-full border-2 border-yellow-400 animate-pulse-fast"></div>
          )}
        </div>
        {isHovered && !isActive && !isClosing && (
          <div className="absolute inset-0 rounded-full bg-gradient-to-br from-blue-400/20 to-purple-400/20 animate-pulse"></div>
        )}
        {isClosing && (
          <div className="absolute inset-0 rounded-full bg-gradient-to-br from-yellow-400/40 to-orange-400/40 animate-pulse-fast"></div>
        )}
      </div>
      {!isClosing && (
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          <div className="absolute top-0 right-0 w-2 h-2 bg-blue-400/60 rounded-full animate-float-up-1"></div>
          <div className="absolute top-2 right-8 w-1.5 h-1.5 bg-purple-400/60 rounded-full animate-float-up-2"></div>
          <div className="absolute top-6 right-2 w-1 h-1 bg-pink-400/60 rounded-full animate-float-up-3"></div>
          <div className="absolute top-4 right-12 w-1 h-1 bg-cyan-400/60 rounded-full animate-float-up-4"></div>
        </div>
      )}
      {isClosing && (
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          <div className="absolute top-0 right-0 w-3 h-3 bg-yellow-400 rounded-full animate-explode-1"></div>
          <div className="absolute top-2 right-8 w-2 h-2 bg-orange-400 rounded-full animate-explode-2"></div>
          <div className="absolute top-6 right-2 w-2 h-2 bg-red-400 rounded-full animate-explode-3"></div>
          <div className="absolute top-4 right-12 w-1.5 h-1.5 bg-pink-400 rounded-full animate-explode-4"></div>
          <div className="absolute bottom-2 left-2 w-2 h-2 bg-purple-400 rounded-full animate-explode-5"></div>
          <div className="absolute bottom-4 left-8 w-1.5 h-1.5 bg-blue-400 rounded-full animate-explode-6"></div>
        </div>
      )}
      {isActive && !isClosing && (
        <div className="absolute inset-0 rounded-full border-4 border-blue-300/30 animate-ping"></div>
      )}
      <style jsx>{`
        @keyframes gentle-bounce {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-4px); }
        }
        @keyframes spin-slow {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
        @keyframes spin-fast {
          from { transform: rotate(0deg); }
          to { transform: rotate(720deg); }
        }
        @keyframes close-bot {
          0% { transform: scale(1) rotate(0deg); opacity: 1; }
          25% { transform: scale(1.2) rotate(90deg); opacity: 0.9; }
          50% { transform: scale(0.8) rotate(180deg); opacity: 0.7; }
          75% { transform: scale(1.1) rotate(270deg); opacity: 0.4; }
          100% { transform: scale(0) rotate(360deg); opacity: 0; }
        }
        @keyframes ping-fast {
          0% { transform: scale(1); opacity: 1; }
          50% { transform: scale(1.5); opacity: 0.5; }
          100% { transform: scale(2); opacity: 0; }
        }
        @keyframes pulse-fast {
          0%, 100% { opacity: 1; transform: scale(1); }
          50% { opacity: 0.5; transform: scale(1.1); }
        }
        @keyframes explode-1 {
          0% { transform: translate(0px, 0px) scale(1); opacity: 1; }
          100% { transform: translate(-30px, -30px) scale(0); opacity: 0; }
        }
        @keyframes explode-2 {
          0% { transform: translate(0px, 0px) scale(1); opacity: 1; }
          100% { transform: translate(40px, -25px) scale(0); opacity: 0; }
        }
        @keyframes explode-3 {
          0% { transform: translate(0px, 0px) scale(1); opacity: 1; }
          100% { transform: translate(-25px, 35px) scale(0); opacity: 0; }
        }
        @keyframes explode-4 {
          0% { transform: translate(0px, 0px) scale(1); opacity: 1; }
          100% { transform: translate(35px, 20px) scale(0); opacity: 0; }
        }
        @keyframes explode-5 {
          0% { transform: translate(0px, 0px) scale(1); opacity: 1; }
          100% { transform: translate(-40px, 15px) scale(0); opacity: 0; }
        }
        @keyframes explode-6 {
          0% { transform: translate(0px, 0px) scale(1); opacity: 1; }
          100% { transform: translate(30px, -40px) scale(0); opacity: 0; }
        }
        @keyframes float-up-1 {
          0% { transform: translateY(0px) translateX(0px); opacity: 0.7; }
          50% { transform: translateY(-20px) translateX(5px); opacity: 1; }
          100% { transform: translateY(-40px) translateX(-5px); opacity: 0; }
        }
        @keyframes float-up-2 {
          0% { transform: translateY(0px) translateX(0px); opacity: 0.6; }
          50% { transform: translateY(-25px) translateX(-8px); opacity: 1; }
          100% { transform: translateY(-50px) translateX(8px); opacity: 0; }
        }
        @keyframes float-up-3 {
          0% { transform: translateY(0px) translateX(0px); opacity: 0.8; }
          50% { transform: translateY(-15px) translateX(3px); opacity: 1; }
          100% { transform: translateY(-30px) translateX(-3px); opacity: 0; }
        }
        @keyframes float-up-4 {
          0% { transform: translateY(0px) translateX(0px); opacity: 0.5; }
          50% { transform: translateY(-35px) translateX(-10px); opacity: 1; }
          100% { transform: translateY(-70px) translateX(10px); opacity: 0; }
        }
        @keyframes bounce-in {
          0% { transform: scale(0.3) translateY(10px); opacity: 0; }
          50% { transform: scale(1.05) translateY(-5px); }
          70% { transform: scale(0.9) translateY(0px); }
          100% { transform: scale(1) translateY(0px); opacity: 1; }
        }
        .animate-gentle-bounce { animation: gentle-bounce 3s ease-in-out infinite; }
        .animate-spin-slow { animation: spin-slow 8s linear infinite; }
        .animate-spin-fast { animation: spin-fast 0.5s ease-in-out; }
        .animate-close-bot { animation: close-bot 0.5s ease-in-out forwards; }
        .animate-ping-fast { animation: ping-fast 0.3s ease-out infinite; }
        .animate-pulse-fast { animation: pulse-fast 0.2s ease-in-out infinite; }
        .animate-explode-1 { animation: explode-1 0.5s ease-out forwards; }
        .animate-explode-2 { animation: explode-2 0.5s ease-out forwards 0.05s; }
        .animate-explode-3 { animation: explode-3 0.5s ease-out forwards 0.1s; }
        .animate-explode-4 { animation: explode-4 0.5s ease-out forwards 0.15s; }
        .animate-explode-5 { animation: explode-5 0.5s ease-out forwards 0.2s; }
        .animate-explode-6 { animation: explode-6 0.5s ease-out forwards 0.25s; }
        .animate-float-up-1 { animation: float-up-1 4s ease-out infinite; }
        .animate-float-up-2 { animation: float-up-2 5s ease-out infinite 1s; }
        .animate-float-up-3 { animation: float-up-3 3.5s ease-out infinite 2s; }
        .animate-float-up-4 { animation: float-up-4 4.5s ease-out infinite 0.5s; }
        .animate-bounce-in { animation: bounce-in 0.6s ease-out; }
      `}</style>
    </div>
  );
};

export default EnhancedBotIcon;
