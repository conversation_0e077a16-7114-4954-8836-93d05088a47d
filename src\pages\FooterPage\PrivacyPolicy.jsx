import React, { lazy, Suspense, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Logo from '../../assets/sasthra_logo.png';
import {
  Mail,
  FileText,
  ChevronDown,
  Lock,
  Shield,
  Eye,
  Users,
  <PERSON>ie,
  Link,
  RefreshCw
} from 'lucide-react';
import { useNavigate } from 'react-router';

// Lazy load components
const InteractiveBackground = lazy(() => import('./MandalaBackground'));
const SectionCard = lazy(() => import('./SectionContent'));

const PrivacyPolicy = () => {
  const [activeSection, setActiveSection] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const navigate = useNavigate();
  const sections = [
    {
      id: 1,
      title: 'Introduction',
      icon: <Lock className="w-5 h-5" />,
      content: `At Sasthra, an AI-driven educational platform by DataSpark AI Solutions, we are committed to safeguarding your privacy...`
    },
    {
      id: 2,
      title: 'Information We Collect',
      icon: <Eye className="w-5 h-5" />,
      content: `We collect the following information to enhance your learning experience...`
    },
    {
      id: 3,
      title: 'How We Use Your Information',
      icon: <Shield className="w-5 h-5" />,
      content: `Your information is used to deliver personalized AI-driven educational content...`
    },
    {
      id: 4,
      title: 'Sharing Your Information',
      icon: <Users className="w-5 h-5" />,
      content: `We do not sell your data. DataSpark AI Solutions may share your information with trusted partners...`
    },
    {
      id: 5,
      title: 'Data Security',
      icon: <Shield className="w-5 h-5" />,
      content: `We employ robust security measures, including 256-bit SSL encryption...`
    },
    {
      id: 6,
      title: 'Your Rights',
      icon: <Eye className="w-5 h-5" />,
      content: `You have the right to access, correct, or delete your personal information...`
    },
    {
      id: 7,
      title: 'Cookies and Tracking',
      icon: <Cookie className="w-5 h-5" />,
      content: `We use cookies and similar technologies to enhance your experience...`
    },

    {
      id: 8,
      title: 'Policy Updates',
      icon: <RefreshCw className="w-5 h-5" />,
      content: `We may update this Privacy Policy as our services evolve...`
    },
    {
      id: 9,
      title: 'Contact Us',
      icon: <Mail className="w-5 h-5" />,
      content: `For questions or concerns about this Privacy Policy, please contact us...`
    }
  ];

  const filteredSections = sections.filter(
    (section) =>
      section.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      section.content.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 to-saffron-50 font-sans relative overflow-hidden">
      <style jsx>{`
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&family=Noto+Serif+Devanagari:wght@400;700&display=swap');
        .font-sans {
          font-family: 'Poppins', sans-serif;
        }
        .font-devanagari {
          font-family: 'Noto Serif Devanagari', serif;
        }
        .glass-card {
          background: rgba(255, 255, 255, 0.85);
          backdrop-filter: blur(12px);
          -webkit-backdrop-filter: blur(12px);
        }
        .gradient-text {
          background: linear-gradient(90deg, #f97316, #7c3aed);
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>

      <Suspense fallback={<div className="absolute inset-0 bg-indigo-50/50" />}>
        <InteractiveBackground />
      </Suspense>

      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="glass-card rounded-3xl shadow-2xl overflow-hidden">
          {/* Header */}
          <div className="relative overflow-hidden bg-[var(--color-student)]">
            {/* Abstract floating shapes */}
            <div className="absolute inset-0 overflow-hidden">
              {/* Floating encrypted lock icons */}
              {[...Array(8)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute text-cyan-300/20"
                  style={{
                    fontSize: `${Math.random() * 2 + 1}rem`,
                    left: `${Math.random() * 100}%`,
                    top: `${Math.random() * 100}%`
                  }}
                  animate={{
                    y: [0, Math.random() * 100 - 50],
                    x: [0, Math.random() * 100 - 50],
                    rotate: [0, 360]
                  }}
                  transition={{
                    duration: Math.random() * 20 + 10,
                    repeat: Infinity,
                    repeatType: 'reverse',
                    ease: 'linear'
                  }}>
                  🔒
                </motion.div>
              ))}

              {/* Particle network */}
              <div className="absolute inset-0">
                {[...Array(30)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute rounded-full bg-cyan-400/10"
                    style={{
                      width: `${Math.random() * 10 + 2}px`,
                      height: `${Math.random() * 10 + 2}px`,
                      left: `${Math.random() * 100}%`,
                      top: `${Math.random() * 100}%`
                    }}
                    animate={{
                      scale: [1, 1.5, 1],
                      opacity: [0.1, 0.4, 0.1]
                    }}
                    transition={{
                      duration: Math.random() * 5 + 3,
                      repeat: Infinity,
                      ease: 'easeInOut'
                    }}
                  />
                ))}
              </div>

              {/* Animated circuit board lines */}
              <svg
                className="absolute inset-0 w-full h-full opacity-10"
                xmlns="http://www.w3.org/2000/svg">
                <motion.path
                  d="M0,20 Q100,50 200,20 T400,20 T600,50 T800,20"
                  stroke="cyan"
                  strokeWidth="1"
                  fill="none"
                  initial={{ pathLength: 0 }}
                  animate={{ pathLength: 1 }}
                  transition={{ duration: 8, repeat: Infinity, ease: 'linear' }}
                />
                <motion.path
                  d="M0,60 Q150,30 300,60 T600,60 T900,30"
                  stroke="cyan"
                  strokeWidth="1"
                  fill="none"
                  initial={{ pathLength: 0 }}
                  animate={{ pathLength: 1 }}
                  transition={{ duration: 10, repeat: Infinity, ease: 'linear', delay: 1 }}
                />
              </svg>

              {/* Holographic grid */}
              <motion.div
                className="absolute inset-0 bg-[length:40px_40px]"
                style={{
                  backgroundImage: `
          linear-gradient(to right, cyan 1px, transparent 1px),
          linear-gradient(to bottom, cyan 1px, transparent 1px)
        `,
                  opacity: 0.05
                }}
                animate={{
                  backgroundPosition: ['0% 0%', '100% 100%']
                }}
                transition={{
                  duration: 30,
                  repeat: Infinity,
                  ease: 'linear'
                }}
              />

              {/* Radar sweep effect */}
              <motion.div
                className="absolute top-1/2 left-1/2 w-0 h-0"
                style={{
                  boxShadow: '0 0 100px 50px rgba(0, 255, 255, 0.05)',
                  transformOrigin: 'center'
                }}
                animate={{ rotate: 360 }}
                transition={{
                  duration: 8,
                  repeat: Infinity,
                  ease: 'linear'
                }}
              />
            </div>

            {/* Your content */}
            <div className="relative z-10 p-8 text-center">
              {/* Logo container - positioned absolutely on left */}
 <button className="text-white hover:cursor-pointer text-bold " onClick={() => navigate('/')}>
                Back
              </button>
              <motion.div
                className="absolute  left-8 top-1/2 -translate-y-1/2"
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}>
                {/* Replace with your actual logo image */}
                
                <img src={Logo} alt="Company Logo" className="h-24 w-auto md:h-32 " />
              </motion.div>
             
              <motion.h1
                className="text-5xl md:text-7xl font-extrabold mb-4 text-transparent bg-clip-text bg-gradient-to-r from-white to-cyan-200 font-sans tracking-tighter"
                initial={{ opacity: 0, letterSpacing: '1em' }}
                animate={{ opacity: 1, letterSpacing: '0.05em' }}
                transition={{ duration: 1, delay: 0.3 }}>
                Privacy Policy
                <motion.span
                  className="absolute ml-2 w-2 h-10 bg-cyan-400"
                  animate={{
                    opacity: [0, 1, 0],
                    x: [-10, 0, 10]
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity
                  }}
                />
              </motion.h1>
              <motion.p
                className="text-indigo-100 text-lg"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.4 }}>
                Your data security is our priority
              </motion.p>
            </div>
          </div>

          {/* Search and Navigation */}
          <div className="p-6 border-b border-gray-200">
            <div className="relative max-w-xl mx-auto">
              <input
                type="text"
                placeholder="Search privacy policy..."
                className="w-full px-4 py-3 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 pl-12"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <div className="absolute left-4 top-3.5 text-gray-400">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
            </div>

            <div className="mt-6 flex overflow-x-auto scrollbar-hide space-x-2 pb-2">
              {sections.map((section) => (
                <motion.button
                  key={section.id}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className={`flex items-center px-4 py-2 rounded-full text-sm whitespace-nowrap ${
                    activeSection === section.id
                      ? 'bg-indigo-500 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                  onClick={() =>
                    setActiveSection(activeSection === section.id ? null : section.id)
                  }>
                  {section.icon}
                  <span className="ml-2">{section.title}</span>
                </motion.button>
              ))}
            </div>
          </div>

          {/* Main Content */}
          <div className="grid grid-cols-1 lg:grid-cols-3">
            {/* Sidebar */}
            <div className="lg:col-span-1 p-6 border-r border-gray-200 hidden lg:block">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Policy Sections</h3>
              <ul className="space-y-2">
                {sections.map((section) => (
                  <motion.li
                    key={section.id}
                    whileHover={{ x: 5 }}
                    className={`cursor-pointer px-4 py-3 rounded-lg transition-colors ${
                      activeSection === section.id
                        ? 'bg-indigo-50 text-indigo-600 font-medium'
                        : 'text-gray-600 hover:bg-gray-50'
                    }`}
                    onClick={() => setActiveSection(section.id)}>
                    <div className="flex items-center">
                      <span className="mr-3">{section.icon}</span>
                      <span>{section.title}</span>
                    </div>
                  </motion.li>
                ))}
              </ul>
            </div>

            {/* Content Area */}
            <div className="lg:col-span-2 p-6 md:p-8">
              <AnimatePresence mode="wait">
                {activeSection ? (
                  <Suspense
                    fallback={
                      <div className="h-64 flex items-center justify-center">Loading...</div>
                    }>
                    <SectionCard
                      section={sections.find((s) => s.id === activeSection)}
                      onClose={() => setActiveSection(null)}
                    />
                  </Suspense>
                ) : (
                  <motion.div
                    key="overview"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="space-y-6">
                    <div className="text-center py-8">
                      <Shield className="w-12 h-12 mx-auto text-indigo-500 mb-4" />
                      <h2 className="text-2xl font-bold text-gray-800 mb-2">
                        Our Privacy Commitment
                      </h2>
                      <p className="text-gray-600 max-w-2xl mx-auto">
                        At Sasthra, we believe transparency about data collection and usage is
                        fundamental to trust. This policy explains how we handle your information in
                        our AI-driven educational platform.
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {filteredSections.slice(0, 4).map((section) => (
                        <motion.div
                          key={section.id}
                          whileHover={{ y: -5 }}
                          className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 cursor-pointer"
                          onClick={() => setActiveSection(section.id)}>
                          <div className="flex items-center mb-3">
                            <div className="p-2 rounded-lg bg-indigo-50 text-indigo-500 mr-3">
                              {section.icon}
                            </div>
                            <h3 className="text-lg font-semibold">{section.title}</h3>
                          </div>
                          <p className="text-gray-600 line-clamp-2">
                            {section.content.substring(0, 120)}...
                          </p>
                          <div className="mt-4 text-indigo-500 flex items-center text-sm font-medium">
                            Read more <ChevronDown className="w-4 h-4 ml-1" />
                          </div>
                        </motion.div>
                      ))}
                    </div>

                    <div className="bg-saffron-50 rounded-xl p-6 border border-saffron-100">
                      <div className="flex items-start">
                        <div className="p-2 rounded-lg bg-saffron-100 text-saffron-600 mr-4">
                          <RefreshCw className="w-5 h-5" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-800 mb-2">
                            Policy Updates
                          </h3>
                          <p className="text-gray-700 mb-4">
                            This policy was last updated on{' '}
                            <span className="font-medium">July 22, 2025</span>. We may update this
                            policy as our services evolve.
                          </p>
                         
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>

          {/* Footer */}
          <div className="bg-gray-50 p-6 border-t border-gray-200">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="flex items-center mb-4 md:mb-0">
                <FileText className="w-5 h-5 text-gray-500 mr-2" />
                <span className="text-gray-600 text-sm">2025 Sasthra, DataSpark AI Solutions</span>
              </div>
              <div className="flex space-x-6">
                <a href="#" className="text-gray-600 hover:text-indigo-600 text-sm">
                  Terms of Service
                </a>
                <a href="#" className="text-gray-600 hover:text-indigo-600 text-sm">
                  Cookie Policy
                </a>
                <a href="#" className="text-gray-600 hover:text-indigo-600 text-sm">
                  Data Processing
                </a>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default PrivacyPolicy;
