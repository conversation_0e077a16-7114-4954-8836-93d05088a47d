import React, { useEffect, useRef, useMemo } from 'react';
import { useGLTF, useFBX } from '@react-three/drei';
import { useFrame } from '@react-three/fiber';
import { SkeletonUtils } from 'three-stdlib';
import * as THREE from 'three';

const visemeMap = {
  AA: { Jaw_Open: 0.15, Mouth_open_3: 0.4, V_Tight: 0.4 },
  AE: { Jaw_Open: 0.13, Mouth_open2: 0.35, V_Tight: 0.4 },
  AH: { Jaw_Open: 0.12, Mouth_open_3: 0.3, V_Tight: 0.3 },
  AO: {
    Jaw_Open: 0.1,
    Mouth_Pucker_Up_L: 0.25,
    Mouth_Pucker_Up_R: 0.25,
    Mouth_open2: 0.3,
    V_Tight: 0.4
  },
  B: { Mouth_Press_L: 0.4, Mouth_Press_R: 0.4, V_Tight: 0.6 },
  P: { Mouth_Press_L: 0.4, Mouth_Press_R: 0.4, V_Tight: 0.6 },
  M: { Mouth_Press_L: 0.4, Mouth_Press_R: 0.4, V_Tight: 0.5 },
  CH: { Jaw_Open: 0.12, Mouth_Press_L: 0.3, Mouth_Press_R: 0.3, Mouth_open_3: 0.35, V_Tight: 0.5 },
  JH: { Jaw_Open: 0.11, Mouth_Press_L: 0.3, Mouth_Press_R: 0.3, Mouth_open2: 0.3, V_Tight: 0.4 },
  SH: { Mouth_Shrug_Upper: 0.35, Mouth_open_3: 0.25, V_Tight: 0.4 },
  D: { Jaw_Open: 0.1, Tongue_Tip_Up: 0.4, Mouth_open2: 0.25, V_Tight: 0.3 },
  T: { Jaw_Open: 0.1, Tongue_Tip_Up: 0.4, Mouth_open_3: 0.25, V_Tight: 0.3 },
  EH: { Jaw_Open: 0.12, Mouth_open2: 0.3, V_Tight: 0.4 },
  ER: { Jaw_Open: 0.1, V_Tongue_Raise: 0.5, Mouth_open_3: 0.25, V_Tight: 0.5 },
  EY: { Mouth_open2: 0.2, V_Tight: 0.4 },
  F: { V_Dental_Lip: 0.5, Mouth_open_3: 0.25, V_Tight: 0.5 },
  V: { V_Dental_Lip: 0.5, Mouth_open2: 0.25, V_Tight: 0.5 },
  G: { Jaw_Open: 0.1, Tongue_Tip_Up: 0.3, Mouth_open2: 0.3, V_Tight: 0.3 },
  K: { Jaw_Open: 0.1, Tongue_Tip_Up: 0.3, Mouth_open_3: 0.3, V_Tight: 0.3 },
  NG: { Jaw_Open: 0.1, Tongue_Tip_Up: 0.3, Mouth_open2: 0.25, V_Tight: 0.3 },
  HH: { Mouth_open_3: 0.3, V_Tight: 0.4 },
  IY: { Mouth_open2: 0.2, V_Tight: 0.4 },
  L: { Tongue_Tip_Up: 0.5, Mouth_open2: 0.3, V_Tight: 0.5 },
  N: { Tongue_Tip_Up: 0.5, Mouth_open_3: 0.3, V_Tight: 0.5 },
  OW: { Mouth_Pucker_Up_L: 0.35, Mouth_Pucker_Up_R: 0.35, Mouth_open2: 0.25, V_Tight: 0.4 },
  OY: { Mouth_Pucker_Up_L: 0.3, Mouth_open_3: 0.3, V_Tight: 0.4 },
  R: { Tongue_Tip_Up: 0.4, Mouth_open2: 0.25, V_Tight: 0.5 },
  S: { V_Tight: 0.5, Mouth_Shrug_Upper: 0.35, Mouth_open_3: 0.25 },
  Z: { V_Tight: 0.5, Mouth_Shrug_Upper: 0.35, Mouth_open2: 0.25 },
  TH: { V_Tongue_Out: 0.5, Mouth_open_3: 0.25, V_Tight: 0.5 },
  DH: { V_Tongue_Out: 0.5, Mouth_open2: 0.25, V_Tight: 0.5 },
  UW: { Mouth_Pucker_Up_L: 0.4, Mouth_Pucker_Up_R: 0.4, Mouth_open_3: 0.25, V_Tight: 0.5 },
  W: { Mouth_Pucker_Up_L: 0.4, Mouth_Pucker_Up_R: 0.4, Mouth_open2: 0.25, V_Tight: 0.5 },
  Y: { Mouth_open_3: 0.3, V_Tight: 0.5 },

  // Punctuation – no facial animation
  ',': {},
  '.': {},
  '-': {},
  ' ': {}
};

const expressionMap = {
  neutral: {},
  smile: {
    Mouth_Smile_L: 0.3,
    Mouth_Smile_R: 0.3,
    Cheek_Raise_L: 0.2,
    Cheek_Raise_R: 0.2
  },
  sad: {
    Mouth_Frown_L: 0.3,
    Mouth_Frown_R: 0.3,
    Cheek_Suck_L: 0.2,
    Cheek_Suck_R: 0.2,
    Brow_Raise_Inner_L: 0.15,
    Brow_Raise_Inner_R: 0.15
  },
  angry: {
    Brow_Compress_L: 0.3,
    Brow_Compress_R: 0.3,
    Brow_Drop_L: 0.2,
    Brow_Drop_R: 0.2,
    Eye_Squint_L: 0.2,
    Eye_Squint_R: 0.2,
    Mouth_Tighten_L: 0.25,
    Mouth_Tighten_R: 0.25
  },
  surprised: {
    Brow_Raise_Outer_L: 0.25,
    Brow_Raise_Outer_R: 0.25,
    Eye_Wide_L: 0.25,
    Eye_Wide_R: 0.25,
    Jaw_Open: 0.3,
    Mouth_Open: 0.5
  },
  blink: { Eye_Blink_L: 1.0, Eye_Blink_R: 1.0 },
  confused: { Brow_Raise_Inner_L: 0.2, Brow_Drop_R: 0.2, Mouth_Shrug_Lower: 0.15 },
  excited: {
    Mouth_Smile_L: 0.4,
    Mouth_Smile_R: 0.4,
    Cheek_Raise_L: 0.3,
    Cheek_Raise_R: 0.3,
    Eye_Wide_L: 0.15,
    Eye_Wide_R: 0.15
  },
  disgust: { Nose_Sneer_L: 0.2, Nose_Sneer_R: 0.2, Mouth_Press_L: 0.2, Mouth_Press_R: 0.2 },
  laugh: {
    Mouth_Smile_L: 0.5,
    Mouth_Smile_R: 0.5,
    Cheek_Raise_L: 0.3,
    Cheek_Raise_R: 0.3,
    Jaw_Open: 0.3
  },
  fear: {
    Eye_Wide_L: 0.4,
    Eye_Wide_R: 0.4,
    Brow_Raise_Outer_L: 0.3,
    Brow_Raise_Outer_R: 0.3,
    Mouth_Open: 0.4,
    Jaw_Open: 0.3
  }
};

export function Avatars({ currentViseme, expression = 'smile', ...props }) {
  const { scene } = useGLTF('/avatar_final.glb');
  const { animations: animClips } = useFBX('/idle_sasthra_female.fbx');
  const clone = useMemo(() => SkeletonUtils.clone(scene), [scene]);
  const mixer = useRef();
  const nodes = {};

  clone.traverse((o) => {
    if (o.isMesh || o.isSkinnedMesh) nodes[o.name] = o;
  });

  const currentInfluences = useRef({});

  useEffect(() => {
    const scalp = nodes['outfit_outfit_0085_16'];
    if (scalp) scalp.visible = false;
  }, [nodes]);

  useEffect(() => {
    if (animClips.length > 0) {
      mixer.current = new THREE.AnimationMixer(clone);
      const action = mixer.current.clipAction(animClips[0]);
      action.play();
    }
  }, [animClips, clone]);

  useFrame((_, delta) => {
    mixer.current?.update(delta);
    const headMesh = nodes['outfit_outfit_0085_12'];
    if (!headMesh?.morphTargetInfluences || !headMesh.morphTargetDictionary) return;

    const dict = headMesh.morphTargetDictionary;
    const influences = headMesh.morphTargetInfluences;

    const viseme = visemeMap[currentViseme] || {};
    const exp = expressionMap[expression] || {};
    const activeKeys = new Set([...Object.keys(viseme), ...Object.keys(exp)]);

    for (let key in dict) {
      const visemeVal = viseme[key] || 0;
      const expVal = exp[key] || 0;
      const target = Math.max(visemeVal, expVal);
      const current = currentInfluences.current[key] || 0;
      const blendSpeed = activeKeys.has(key) ? 15 : 5;
      const newVal = THREE.MathUtils.lerp(current, target, blendSpeed * delta);
      currentInfluences.current[key] = newVal;
      influences[dict[key]] = newVal;
    }
  });

  return <primitive object={clone} {...props} />;
}

useGLTF.preload('/avatar_final.glb');
