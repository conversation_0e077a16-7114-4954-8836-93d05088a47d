'use client';

import { useState } from 'react';
// eslint-disable-next-line no-unused-vars
import { motion, AnimatePresence } from 'framer-motion';
import {
  Clock,
  CheckCircle2,
  Atom,
  FlaskConical,
  Calculator,
  Target,
  TrendingUp,
  Zap,
  Timer,
  Award,
  BarChart3,
  Info,
  AlertCircle,
  CheckCircle,
  Activity
} from 'lucide-react';

const TimeAnalysisDashboard = () => {
  const [selectedSubject, setSelectedSubject] = useState(null);
  const [hoveredMetric, setHoveredMetric] = useState(null);
  const [showTooltip, setShowTooltip] = useState(null);
  const [sortBy, setSortBy] = useState('time');

  // Animation variants
  const containerVariants = {
    initial: { opacity: 0 },
    animate: {
      opacity: 1,
      transition: {
        duration: 0.8,
        staggerChildren: 0.12,
        delayChildren: 0.3
      }
    }
  };

  const rowVariants = {
    initial: { opacity: 0, x: -60, scale: 0.95 },
    animate: {
      opacity: 1,
      x: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    },
    hover: {
      scale: 1.02,
      y: -4,
      transition: {
        duration: 0.3,
        ease: 'easeOut'
      }
    }
  };

  const metricVariants = {
    initial: { opacity: 0, scale: 0.8 },
    animate: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.5,
        ease: 'easeOut'
      }
    },
    hover: {
      scale: 1.1,
      transition: {
        duration: 0.2
      }
    }
  };

  const progressVariants = {
    initial: { width: 0 },
    animate: {
      width: '100%',
      transition: {
        duration: 2,
        delay: 1,
        ease: 'easeInOut'
      }
    }
  };

  // Time analysis data
  const timeData = [
    {
      id: 1,
      subject: 'Overall',
      icon: CheckCircle2,
      color: 'blue',
      iconColor: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      gradientFrom: 'from-blue-500',
      gradientTo: 'to-indigo-600',
      timeSpent: 112,
      questionsAttempted: 59,
      totalQuestions: 75,
      accuracy: 96.61,
      efficiency: 85.2, // Custom metric: accuracy vs time
      avgTimePerQuestion: 1.9
    },
    {
      id: 2,
      subject: 'Physics',
      icon: Atom,
      color: 'green',
      iconColor: 'text-green-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      gradientFrom: 'from-green-500',
      gradientTo: 'to-emerald-600',
      timeSpent: 28,
      questionsAttempted: 24,
      totalQuestions: 25,
      accuracy: 95.83,
      efficiency: 92.1,
      avgTimePerQuestion: 1.17
    },
    {
      id: 3,
      subject: 'Chemistry',
      icon: FlaskConical,
      color: 'orange',
      iconColor: 'text-orange-600',
      bgColor: 'bg-orange-50',
      borderColor: 'border-orange-200',
      gradientFrom: 'from-orange-500',
      gradientTo: 'to-red-500',
      timeSpent: 20,
      questionsAttempted: 20,
      totalQuestions: 25,
      accuracy: 100.0,
      efficiency: 95.5,
      avgTimePerQuestion: 1.0
    },
    {
      id: 4,
      subject: 'Mathematics',
      icon: Calculator,
      color: 'purple',
      iconColor: 'text-purple-600',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200',
      gradientFrom: 'from-purple-500',
      gradientTo: 'to-indigo-600',
      timeSpent: 63,
      questionsAttempted: 15,
      totalQuestions: 25,
      accuracy: 93.33,
      efficiency: 71.8,
      avgTimePerQuestion: 4.2
    }
  ];

  const metricConfig = {
    timeSpent: {
      title: 'TIME SPENT',
      icon: Timer,
      color: 'blue',
      bgColor: 'bg-blue-100',
      textColor: 'text-blue-700',
      description: 'Total time spent on this subject',
      unit: 'min'
    },
    questionsAttempted: {
      title: 'QS ATTEMPTED',
      icon: Target,
      color: 'green',
      bgColor: 'bg-green-100',
      textColor: 'text-green-700',
      description: 'Number of questions attempted',
      unit: ''
    },
    accuracy: {
      title: 'ACCURACY',
      icon: Award,
      color: 'purple',
      bgColor: 'bg-purple-100',
      textColor: 'text-purple-700',
      description: 'Percentage of correct answers',
      unit: '%'
    }
  };

  const getEfficiencyLevel = (efficiency) => {
    if (efficiency >= 90)
      return { level: 'Excellent', color: 'text-green-600', bgColor: 'bg-green-100' };
    if (efficiency >= 80) return { level: 'Good', color: 'text-blue-600', bgColor: 'bg-blue-100' };
    if (efficiency >= 70)
      return { level: 'Average', color: 'text-yellow-600', bgColor: 'bg-yellow-100' };
    return { level: 'Needs Improvement', color: 'text-red-600', bgColor: 'bg-red-100' };
  };

  const getTimeEfficiencyIcon = (avgTime) => {
    if (avgTime <= 1.5) return { icon: Zap, color: 'text-green-500' };
    if (avgTime <= 2.5) return { icon: CheckCircle, color: 'text-blue-500' };
    return { icon: AlertCircle, color: 'text-orange-500' };
  };

  const handleSubjectClick = (subjectId) => {
    setSelectedSubject(selectedSubject === subjectId ? null : subjectId);
  };

  const sortedData = [...timeData].sort((a, b) => {
    switch (sortBy) {
      case 'time':
        return b.timeSpent - a.timeSpent;
      case 'accuracy':
        return b.accuracy - a.accuracy;
      case 'efficiency':
        return b.efficiency - a.efficiency;
      default:
        return 0;
    }
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-4 md:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto">
        {/* Enhanced Header */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: 'easeOut' }}>
          <div className="flex items-center justify-center gap-4 mb-6">
            <div className="p-4 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-2xl shadow-lg">
              <Clock className="w-10 h-10 text-white" />
            </div>
            <div className="text-left">
              <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-800 via-gray-700 to-gray-600 bg-clip-text text-transparent">
                Time Analysis
              </h1>
              <p className="text-gray-700 text-lg leading-relaxed">
                Time is the most important resource in any competitive exam.
              </p>
            </div>
          </div>

          {/* <div className="max-w-4xl mx-auto bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
            <p className="text-gray-700 text-lg leading-relaxed">
              Time is the most important resource in any competitive exam. And one major element of
              any test analysis is to check the time spent on an individual subject. This section
              will not only give you insight on the time spent but also the percentage attempt and
              accuracy at the subject level. Make sure to maintain a good balance between accuracy
              and time spent on a subject.
            </p>
          </div> */}

          {/* Summary Metrics */}
          <div className="flex flex-wrap justify-center gap-4 mt-8">
            <div className="flex items-center gap-2 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full shadow-md">
              <Timer className="w-5 h-5 text-blue-500" />
              <span className="text-sm font-medium text-gray-700">Total Time: 112 min</span>
            </div>
            <div className="flex items-center gap-2 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full shadow-md">
              <Activity className="w-5 h-5 text-green-500" />
              <span className="text-sm font-medium text-gray-700">Avg Efficiency: 86.2%</span>
            </div>
            <div className="flex items-center gap-2 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full shadow-md">
              <Target className="w-5 h-5 text-purple-500" />
              <span className="text-sm font-medium text-gray-700">Questions: 59/75</span>
            </div>
          </div>
        </motion.div>

        {/* Sort Controls */}
        <motion.div
          className="flex justify-center mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}>
          <div className="flex bg-white/80 backdrop-blur-sm rounded-2xl p-2 shadow-lg border border-white/20">
            {[
              { key: 'time', label: 'Sort by Time', icon: Timer },
              { key: 'accuracy', label: 'Sort by Accuracy', icon: Award },
              { key: 'efficiency', label: 'Sort by Efficiency', icon: TrendingUp }
            ].map(({ key, label, icon: Icon }) => (
              <button
                key={key}
                onClick={() => setSortBy(key)}
                className={`flex items-center gap-2 px-4 py-2 rounded-xl transition-all duration-300 ${
                  sortBy === key
                    ? 'bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-md'
                    : 'text-gray-600 hover:bg-gray-100'
                }`}>
                <Icon className="w-4 h-4" />
                <span className="text-sm font-medium">{label}</span>
              </button>
            ))}
          </div>
        </motion.div>

        {/* Enhanced Table */}
        <motion.div
          className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-2xl overflow-hidden border border-white/20"
          variants={containerVariants}
          initial="initial"
          animate="animate">
          {/* Table Header */}
          <div className="bg-gradient-to-r from-gray-50 to-gray-100 p-6 border-b border-gray-200">
            <div className="grid grid-cols-4 gap-6">
              <div className="font-semibold text-gray-700">SUBJECT</div>
              {Object.entries(metricConfig).map(([key, config]) => {
                const IconComponent = config.icon;
                return (
                  <div key={key} className="text-center">
                    <div className="flex items-center justify-center gap-2 mb-2">
                      <IconComponent className={`w-4 h-4 ${config.textColor}`} />
                      <span className="font-semibold text-gray-700 text-sm">{config.title}</span>
                      <div
                        className="relative"
                        onMouseEnter={() => setShowTooltip(key)}
                        onMouseLeave={() => setShowTooltip(null)}>
                        <Info className="w-3 h-3 text-gray-400 cursor-help" />
                        {showTooltip === key && (
                          <div className="absolute top-6 left-1/2 transform -translate-x-1/2 z-50 w-48 p-2 bg-gray-900 text-white text-xs rounded-lg shadow-xl">
                            <p>{config.description}</p>
                            <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-gray-900 rotate-45"></div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Table Body */}
          <div className="p-6">
            {sortedData.map((subject) => {
              const IconComponent = subject.icon;
              const isSelected = selectedSubject === subject.id;
              const efficiency = getEfficiencyLevel(subject.efficiency);
              const timeIcon = getTimeEfficiencyIcon(subject.avgTimePerQuestion);
              const TimeIconComponent = timeIcon.icon;

              return (
                <motion.div
                  key={subject.id}
                  variants={rowVariants}
                  whileHover="hover"
                  className={`grid grid-cols-4 gap-6 p-6 rounded-2xl mb-4 cursor-pointer transition-all duration-300 ${
                    isSelected
                      ? `${subject.bgColor} ${subject.borderColor} border-2 shadow-lg`
                      : 'hover:bg-gray-50 hover:shadow-md'
                  }`}
                  onClick={() => handleSubjectClick(subject.id)}>
                  {/* Subject Column */}
                  <div className="flex items-center gap-4">
                    <div className={`p-4 ${subject.bgColor} rounded-xl shadow-md`}>
                      <IconComponent className={`w-7 h-7 ${subject.iconColor}`} />
                    </div>
                    <div>
                      <h3 className="font-bold text-gray-800 text-lg">{subject.subject}</h3>
                      <div className="flex items-center gap-2 mt-1">
                        <span
                          className={`text-xs px-2 py-1 rounded-full ${efficiency.bgColor} ${efficiency.color}`}>
                          {efficiency.level}
                        </span>
                        <TimeIconComponent className={`w-3 h-3 ${timeIcon.color}`} />
                      </div>
                    </div>
                  </div>

                  {/* Time Spent */}
                  <motion.div
                    variants={metricVariants}
                    className="text-center p-4 rounded-xl hover:bg-blue-50 transition-colors duration-300"
                    onMouseEnter={() => setHoveredMetric(`${subject.id}-time`)}
                    onMouseLeave={() => setHoveredMetric(null)}>
                    <div className="mb-3">
                      <span className="text-3xl font-bold text-blue-600">{subject.timeSpent}</span>
                      <span className="text-gray-500 text-sm ml-1">min</span>
                    </div>
                    <div className="text-xs text-gray-600">
                      {subject.avgTimePerQuestion.toFixed(1)} min/question
                    </div>
                    {hoveredMetric === `${subject.id}-time` && (
                      <div className="mt-2 text-xs text-blue-600 font-medium">
                        {((subject.timeSpent / 112) * 100).toFixed(1)}% of total time
                      </div>
                    )}
                  </motion.div>

                  {/* Questions Attempted */}
                  <motion.div
                    variants={metricVariants}
                    className="text-center p-4 rounded-xl hover:bg-green-50 transition-colors duration-300"
                    onMouseEnter={() => setHoveredMetric(`${subject.id}-questions`)}
                    onMouseLeave={() => setHoveredMetric(null)}>
                    <div className="mb-3">
                      <span className="text-3xl font-bold text-green-600">
                        {subject.questionsAttempted}
                      </span>
                      <span className="text-gray-500 text-sm ml-1">/ {subject.totalQuestions}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                      <motion.div
                        className="h-2 bg-green-500 rounded-full"
                        variants={progressVariants}
                        initial="initial"
                        animate="animate"
                        style={{
                          width: `${(subject.questionsAttempted / subject.totalQuestions) * 100}%`
                        }}
                      />
                    </div>
                    <div className="text-xs text-gray-600">
                      {((subject.questionsAttempted / subject.totalQuestions) * 100).toFixed(1)}%
                      attempted
                    </div>
                  </motion.div>

                  {/* Accuracy */}
                  <motion.div
                    variants={metricVariants}
                    className="text-center p-4 rounded-xl hover:bg-purple-50 transition-colors duration-300"
                    onMouseEnter={() => setHoveredMetric(`${subject.id}-accuracy`)}
                    onMouseLeave={() => setHoveredMetric(null)}>
                    <div className="mb-3">
                      <span className="text-3xl font-bold text-purple-600">
                        {subject.accuracy.toFixed(2)}
                      </span>
                      <span className="text-gray-500 text-sm ml-1">%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                      <motion.div
                        className="h-2 bg-purple-500 rounded-full"
                        variants={progressVariants}
                        initial="initial"
                        animate="animate"
                        style={{ width: `${subject.accuracy}%` }}
                      />
                    </div>
                    <div className="text-xs text-gray-600">
                      Efficiency: {subject.efficiency.toFixed(1)}%
                    </div>
                  </motion.div>

                  {/* Expandable Details */}
                  <AnimatePresence>
                    {isSelected && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.4 }}
                        className="col-span-4 border-t border-gray-200 pt-6 mt-4">
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                          <div className="bg-blue-50 p-4 rounded-xl">
                            <div className="text-sm text-gray-600 mb-1">Time per Question</div>
                            <div className="text-xl font-bold text-blue-600">
                              {subject.avgTimePerQuestion.toFixed(1)} min
                            </div>
                          </div>
                          <div className="bg-green-50 p-4 rounded-xl">
                            <div className="text-sm text-gray-600 mb-1">Completion Rate</div>
                            <div className="text-xl font-bold text-green-600">
                              {(
                                (subject.questionsAttempted / subject.totalQuestions) *
                                100
                              ).toFixed(1)}
                              %
                            </div>
                          </div>
                          <div className="bg-purple-50 p-4 rounded-xl">
                            <div className="text-sm text-gray-600 mb-1">Efficiency Score</div>
                            <div className="text-xl font-bold text-purple-600">
                              {subject.efficiency.toFixed(1)}%
                            </div>
                          </div>
                          <div className="bg-orange-50 p-4 rounded-xl">
                            <div className="text-sm text-gray-600 mb-1">Time Distribution</div>
                            <div className="text-xl font-bold text-orange-600">
                              {((subject.timeSpent / 112) * 100).toFixed(1)}%
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              );
            })}
          </div>
        </motion.div>

        {/* Action Buttons */}
      </div>
    </div>
  );
};

export default TimeAnalysisDashboard;
