import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUserPlus, faSpinner } from '@fortawesome/free-solid-svg-icons';
import Input from '../../../../components/Field/Input';
import Button from '../../../../components/Field/Button';
import Select from '../../../../components/Field/Dropdown';
import {
  useAddStudentsMutation,
  useLazyCenterListBatchesQuery,
  useLazyCenterListCoursesQuery,
  useLazyCheckDocumentUploadQuery,
  useUploadApplicationFormMutation
} from './addStudents.slice';
import Toastify from '../../../../components/PopUp/Toastify';
import { fields } from './FormFields';
import FaceCaptureModal from './FaceCaptureModal';
import { useNavigate } from 'react-router';

const AddStudents = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [formExtractLoading, setFormExtractLoading] = useState(false);
  const [studentForm, setStudentForm] = useState({
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    dob: '',
    course: '',
    course_id: '',
    batch_name: '',
    batch_id: '',
    marks_10th: '',
    marks_12th: '',
    religion: '',
    gender: '',
    age: '',
    aadhar_number: '',
    nationality: '',
    full_address: '',
    parent_first_name: '',
    parent_last_name: '',
    parent_email: '',
    parent_phone: '',
    parent_relationship: '',
    parent_occupation: '',
    parent_annual_income_inr: ''
  });

  const [documentFiles, setDocumentFiles] = useState({
    aadhar_card: null,
    pan_card: null,
    photo: null,
    other_document: null
  });
  const [res, setRes] = useState(null);
  const [error, setError] = useState('');
  const [uploadedDocuments, setUploadedDocuments] = useState([]);
  const [lastRequestId, setLastRequestId] = useState('');
  const [uploadStatus, setUploadStatus] = useState(null);
  const [courses, setCourses] = useState([]);
  const [batches, setBatches] = useState([]);
  const [autoFilledFields, setAutoFilledFields] = useState([]);
  const [addStudentsService] = useAddStudentsMutation();
  const [checkDocumentUploadService] = useLazyCheckDocumentUploadQuery();
  const [uploadApplicationFormService] = useUploadApplicationFormMutation();
  const [centerListCourses] = useLazyCenterListCoursesQuery();
  const [centerListBatches] = useLazyCenterListBatchesQuery();
  const [showFaceCapture, setShowFaceCapture] = useState(false);
  const [extractedData, setExtractedData] = useState(null);
  const [faceSessionId, setFaceSessionId] = useState(null);
  const [capturedImageUrls, setCapturedImageUrls] = useState({
    front: null,
    left: null,
    right: null
  });
  const [faceData, setFaceData] = useState({ face_embedding: '', face_session_id: '' });

  const emojiList = [
    '🩺',
    '🛠️',
    '📚',
    '🧬',
    '🔬',
    '💉',
    '💻',
    '📖',
    '🧑‍🎓',
    '👨‍⚕️',
    '👩‍⚕️',
    '👨‍🔬',
    '👨‍💻',
    '👩‍🔬'
  ];
  const [emoji, setEmoji] = useState(emojiList[0]);

  useEffect(() => {
    let index = 0;
    const interval = setInterval(() => {
      index = (index + 1) % emojiList.length;
      setEmoji(emojiList[index]);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      await fetchCourses();
      await fetchBatches();
    };
    fetchData();
  }, []);

  const fetchCourses = async () => {
    try {
      const result = await centerListCourses().unwrap();
      if (result.success && result.data && Array.isArray(result.data.courses)) {
        setCourses(result.data.courses);
      } else {
        setCourses([]);
        setRes('❌ Error loading courses. Please try again.');
      }
    } catch (error) {
      setCourses([]);
      setRes('❌ Error loading courses. Please try again.');
    }
  };

  const fetchBatches = async () => {
    try {
      const result = await centerListBatches().unwrap();
      if (result.success && result.data && Array.isArray(result.data.batches)) {
        setBatches(result.data.batches);
      } else {
        setBatches([]);
        setRes('❌ Error loading batches. Please try again.');
      }
    } catch (error) {
      setBatches([]);
      setRes('❌ Error loading batches. Please try again.');
    }
  };

  const validateField = (field, value) => {
    if (field.required && !value) return setError(`${field.placeholder} is required`), false;
    if (
      [
        'first_name',
        'last_name',
        'parent_first_name',
        'parent_last_name',
        'religion',
        'nationality'
      ].includes(field.name) &&
      value.length > 100
    )
      return setError('Maximum length is 100 characters'), false;
    if (field.type === 'email' && value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value))
      return setError('Invalid email format'), false;
    if (field.name === 'phone' && value && !/^\+91[0-9]{10}$/.test(value))
      return setError('Phone must be in +91xxxxxxxxxx format'), false;
    if (field.name === 'parent_phone' && value && !/^\+91[0-9]{10}$/.test(value))
      return setError('Parent phone must be in +91xxxxxxxxxx format'), false;
    if (['marks_10th', 'marks_12th'].includes(field.name)) {
      const num = parseFloat(value);
      if (value && (isNaN(num) || num < 0 || num > 99.99))
        return setError('Enter a valid percentage between 0 and 99.99'), false;
    }
    if (field.name === 'age' && value) {
      const num = parseInt(value);
      if (isNaN(num) || num < 1 || num > 100)
        return setError('Enter a valid age between 1 and 100'), false;
    }
    if (field.name === 'aadhar_number' && value && !/^[0-9]{4}-[0-9]{4}-[0-9]{4}$/.test(value))
      return setError('Aadhar number must be in 1234-5678-9012 format'), false;
    if (field.name === 'parent_annual_income_inr' && value) {
      const num = parseInt(value);
      if (isNaN(num) || num < 0) return setError('Enter a valid annual income'), false;
    }
    setError('');
    return true;
  };

  const handleFileChange = (documentType, file) => {
    if (!file) {
      setDocumentFiles((prev) => ({ ...prev, [documentType]: null }));
      return;
    }

    const maxSizes = {
      aadhar_card: 5 * 1024 * 1024,
      pan_card: 5 * 1024 * 1024,
      photo: 2 * 1024 * 1024,
      other_document: 10 * 1024 * 1024
    };
    const allowedTypes = {
      aadhar_card: ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'],
      pan_card: ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'],
      photo: ['image/jpeg', 'image/jpg', 'image/png'],
      other_document: ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png']
    };

    if (file.size > maxSizes[documentType]) {
      setRes(
        `❌ File too large!\n\nFile: ${file.name}\nSize: ${(file.size / (1024 * 1024)).toFixed(2)}MB\nMax allowed: ${maxSizes[documentType] / (1024 * 1024)}MB`
      );
      return;
    }

    if (!allowedTypes[documentType].includes(file.type)) {
      setRes(
        `❌ Invalid file type!\n\nFile: ${file.name}\nType: ${file.type}\nAllowed types: ${allowedTypes[documentType].join(', ')}`
      );
      return;
    }

    setDocumentFiles((prev) => ({ ...prev, [documentType]: file }));
  };

  const fileToBase64 = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result.split(',')[1]);
      reader.onerror = (error) => reject(error);
    });
  };

  const getContentType = (file) => file.type || 'application/octet-stream';

  const processDocuments = async () => {
    const documentData = {};
    const hasFiles = Object.values(documentFiles).some((file) => file !== null);

    if (!hasFiles) return documentData;

    try {
      for (const [docType, file] of Object.entries(documentFiles)) {
        if (file) {
          const base64Data = await fileToBase64(file);
          documentData[docType] = {
            data: base64Data,
            filename: file.name,
            content_type: getContentType(file)
          };
        }
      }
    } catch (error) {
      throw new Error(`Failed to process documents: ${error.message}`);
    }

    return documentData;
  };

  const handleFormExtraction = async (file) => {
    if (!file) return;

    setFormExtractLoading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);
      const result = await uploadApplicationFormService(formData).unwrap();

      if (result && (result.success || result.extracted_data || result.form_id)) {
        setExtractedData(result);
        const extracted = result.extracted_data || {};
        setStudentForm((prev) => ({
          ...prev,
          first_name: extracted.first_name || prev.first_name,
          last_name: extracted.last_name || prev.last_name,
          email: extracted.student_email || prev.email,
          phone: extracted.phone || prev.phone,
          dob: extracted.dob || prev.dob,
          age: extracted.age || prev.age,
          aadhar_number: extracted.aadhar_number || prev.aadhar_number,
          gender: extracted.gender || prev.gender,
          course: extracted.course || prev.course,
          marks_10th: extracted.marks_10th || prev.marks_10th,
          marks_12th: extracted.marks_12th || prev.marks_12th,
          nationality: extracted.nationality || prev.nationality,
          full_address: extracted.full_address || prev.full_address,
          parent_first_name: extracted.parent_first_name || prev.parent_first_name,
          parent_last_name: extracted.parent_last_name || prev.parent_last_name,
          parent_email: extracted.parent_email || prev.parent_email,
          parent_phone: extracted.parent_phone || prev.parent_phone,
          parent_relationship: extracted.parent_relationship || prev.parent_relationship,
          parent_occupation: extracted.parent_occupation || prev.parent_occupation,
          parent_annual_income_inr:
            extracted.parent_annual_income_inr || prev.parent_annual_income_inr
        }));
        setRes(
          `✅ Form data extracted successfully!\nConfidence: ${((result.confidence || 0) * 100).toFixed(1)}%\nPlease review and modify the pre-filled data if needed.`
        );
      } else {
        setRes(`❌ Error extracting form data: ${result?.message || 'Unknown error'}`);
      }
    } catch (error) {
      setRes('❌ Error extracting form data. Please try again.');
    } finally {
      setFormExtractLoading(false);
    }
  };

  const renderExtractionDetails = () => {
    if (!extractedData || !extractedData.extracted_data) return null;

    const extracted = extractedData.extracted_data || {};
    const fieldsToDisplay = [
      { key: 'first_name', label: 'First Name' },
      { key: 'last_name', label: 'Last Name' },
      { key: 'student_email', label: 'Email' },
      { key: 'phone', label: 'Phone' },
      { key: 'dob', label: 'Date of Birth' },
      { key: 'age', label: 'Age' },
      { key: 'aadhar_number', label: 'Aadhar Number' },
      { key: 'gender', label: 'Gender' },
      { key: 'course', label: 'Course' },
      { key: 'marks_10th', label: '10th Marks (%)' },
      { key: 'marks_12th', label: '12th Marks (%)' },
      { key: 'nationality', label: 'Nationality' },
      { key: 'full_address', label: 'Full Address' }
    ];

    return (
      <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
        <h4 className="text-sm font-semibold text-green-800 mb-2">📄 Extracted Student Details</h4>
        <p className="text-sm text-green-700 mb-2">
          Confidence: {(extractedData.confidence * 100).toFixed(1)}%
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
          {fieldsToDisplay.map(({ key, label }) =>
            extracted[key] ? (
              <div key={key} className="text-sm">
                <span className="font-medium text-green-800">{label}:</span> {extracted[key]}
              </div>
            ) : null
          )}
        </div>
        {extractedData.comments?.length > 0 && (
          <div className="mt-2">
            <p className="text-sm font-medium text-green-800">Comments:</p>
            <ul className="text-sm text-green-700 list-disc pl-5">
              {extractedData.comments.map((comment, idx) => (
                <li key={idx}>{comment}</li>
              ))}
            </ul>
          </div>
        )}
        <button
          type="button"
          onClick={() => setExtractedData(null)}
          className="mt-2 text-xs bg-gray-500 text-white px-2 py-1 rounded hover:bg-gray-600">
          ✕ Clear
        </button>
      </div>
    );
  };

  const handleFaceCaptureSuccess = (data) => {
    setFaceData({
      face_embedding: data.face_embedding,
      face_session_id: data.face_session_id
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const invalid = fields(courses, batches)
      .flatMap((s) => s.inputs)
      .find(
        (f) =>
          f.required &&
          !validateField(f, f.type === 'file' ? documentFiles[f.name] : studentForm[f.name])
      );
    if (invalid) return;

    setLoading(true);

    try {
      const documentData = await processDocuments();
      const selectedCourse = courses.find((course) => course.id === studentForm.course_id);
      const selectedBatch = batches.find((batch) => batch.id === studentForm.batch_id);
      const requestPayload = {
        first_name: studentForm.first_name,
        last_name: studentForm.last_name,
        student_email: studentForm.email,
        phone: studentForm.phone,
        dob: studentForm.dob,
        course: selectedCourse ? selectedCourse.course_name : '',
        course_id: studentForm.course_id,
        batch_name: selectedBatch ? selectedBatch.batch_name : '',
        batch_id: studentForm.batch_id,
        marks_10th: parseFloat(studentForm.marks_10th),
        marks_12th: parseFloat(studentForm.marks_12th),
        religion: studentForm.religion || '',
        gender: studentForm.gender,
        age: parseInt(studentForm.age, 10),
        aadhar_number: studentForm.aadhar_number || '',
        nationality: studentForm.nationality,
        full_address: studentForm.full_address,
        parent_first_name: studentForm.parent_first_name,
        parent_last_name: studentForm.parent_last_name || '',
        parent_email: studentForm.parent_email || '',
        parent_phone: studentForm.parent_phone || '',
        parent_relationship: studentForm.parent_relationship,
        parent_occupation: studentForm.parent_occupation || '',
        parent_annual_income_inr: studentForm.parent_annual_income_inr
          ? parseFloat(studentForm.parent_annual_income_inr)
          : null,
        document_data: documentData,
        face_embedding: faceData.face_embedding,
        face_session_id: faceData.face_session_id,
        temp_student_id: Date.now().toString() + Math.random().toString(36).substring(2, 11)
      };

      const result = await addStudentsService(requestPayload).unwrap();
      console.log('Submit Student Request Response:', result);
      setRes(result);
      navigate('/sasthra');
    } catch (error) {
      setRes(`❌ Error submitting request: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const checkDocumentUploadStatus = async () => {
    try {
      const result = await checkDocumentUploadService(lastRequestId).unwrap();
      setRes(result);
    } catch (error) {
      setRes(error);
      return null;
    }
  };

  const resetForm = () => {
    setStudentForm({
      first_name: '',
      last_name: '',
      email: '',
      phone: '',
      dob: '',
      course: '',
      course_id: '',
      batch_name: '',
      batch_id: '',
      marks_10th: '',
      marks_12th: '',
      religion: '',
      gender: '',
      age: '',
      aadhar_number: '',
      nationality: '',
      full_address: '',
      parent_first_name: '',
      parent_last_name: '',
      parent_email: '',
      parent_phone: '',
      parent_relationship: 'father',
      parent_occupation: '',
      parent_annual_income_inr: ''
    });
    setDocumentFiles({ aadhar_card: null, pan_card: null, photo: null, other_document: null });
    setUploadedDocuments([]);
    setExtractedData(null);
    setFaceSessionId(null);
    setCapturedImageUrls({ front: null, left: null, right: null });
    setAutoFilledFields([]);
  };

  const renderCapturedImages = () => {
    if (!faceSessionId) return null;
    return (
      <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
        <h4 className="text-sm font-semibold text-green-800 mb-2">📷 Captured Face Images</h4>
        <div className="grid grid-cols-3 gap-3">
          {capturedImageUrls.front && (
            <div className="text-center">
              <img
                src={capturedImageUrls.front}
                alt="Front Face"
                className="w-full h-24 object-cover rounded border border-green-300"
              />
              <p className="text-xs text-green-700 mt-1">Front</p>
            </div>
          )}
          {capturedImageUrls.left && (
            <div className="text-center">
              <img
                src={capturedImageUrls.left}
                alt="Left Profile"
                className="w-full h-24 object-cover rounded border border-green-300"
              />
              <p className="text-xs text-green-700 mt-1">Left</p>
            </div>
          )}
          {capturedImageUrls.right && (
            <div className="text-center">
              <img
                src={capturedImageUrls.right}
                alt="Right Profile"
                className="w-full h-24 object-cover rounded border border-green-300"
              />
              <p className="text-xs text-green-700 mt-1">Right</p>
            </div>
          )}
        </div>
        <div className="mt-2 text-sm text-green-700">
          <p>✅ Face images captured successfully</p>
          <p className="text-xs text-green-600">Session ID: {faceSessionId.substring(0, 8)}...</p>
        </div>
      </div>
    );
  };

  const handleFormChange = (fieldName, value) => {
    if (fieldName === 'dob') {
      const calculatedAge = calculateAge(value);
      setStudentForm((prev) => ({
        ...prev,
        dob: value,
        age: calculatedAge
      }));
    } else {
      setStudentForm((prev) => ({
        ...prev,
        [fieldName]: value
      }));
    }
  };

  // Function to calculate age from date of birth
  const calculateAge = (dob) => {
    if (!dob) return '';

    const birthDate = new Date(dob);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age > 0 ? age.toString() : '';
  };

  return (
    <div className="relative min-h-screen flex items-center justify-center bg-white">
      {/* Full-page creative loading overlay */}
      {(loading || formExtractLoading) && (
        <div className="fixed inset-0 z-50 flex flex-col items-center justify-center bg-white bg-opacity-40 backdrop-blur-sm animate-fade-in">
          <div className="flex flex-col items-center">
            <svg
              className="animate-spin h-20 w-20 text-yellow-400 mb-6"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24">
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
            </svg>
            <div className="w-64 h-3 bg-yellow-100 rounded-full overflow-hidden mb-4">
              <div
                className="h-full bg-yellow-400 animate-progress-bar"
                style={{ width: '80%' }}></div>
            </div>
            <h2 className="text-2xl font-bold text-yellow-600 mb-2 drop-shadow-lg">
              Processing your request...
            </h2>
            <p className="text-yellow-700 text-lg animate-pulse">
              Please wait while we securely submit your data{' '}
              <span className="inline-block animate-bounce">✨</span>
            </p>
          </div>
        </div>
      )}
      <Toastify res={res} resClear={() => setRes(null)} />
      <motion.div
        className="w-full max-w-4xl bg-white p-10 rounded-2xl shadow-2xl border-4 border-yellow-400"
        initial={{ opacity: 0, y: 40 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}>
        <motion.div
          className="relative text-center mb-10 py-6 overflow-hidden"
          initial={{ opacity: 0, y: -40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1, duration: 0.6 }}>
          {/* Main heading */}
          <div className="relative z-10">
            <motion.div
              className="inline-block mb-4"
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.3, type: 'spring' }}>
              <div className="relative inline-flex items-center justify-center">
                {/* Icon background with shine effect */}
                <div className="absolute inset-0 bg-yellow-400 rounded-full blur-md opacity-30"></div>
                <div className="relative w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center shadow-lg border-2 border-white/20">
                  <FontAwesomeIcon icon={faUserPlus} className="text-white text-2xl" />
                </div>
              </div>
            </motion.div>

            <motion.h2
              className="text-5xl font-extrabold tracking-tight mb-2"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.4 }}
              style={{
                background: 'linear-gradient(to right, #f4c430, #f59e0b)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                textShadow: '0 2px 12px rgba(244, 196, 48, 0.3)'
              }}>
              Student Registration
            </motion.h2>

            <motion.p
              className="text-lg text-black max-w-md mx-auto"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}>
              Complete the form to register a new student
            </motion.p>

            {/* Animated decorative elements */}
            <motion.div
              className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-64 h-1 bg-yellow-400 rounded-full"
              initial={{ scaleX: 0 }}
              animate={{ scaleX: 1 }}
              transition={{ delay: 0.6, duration: 0.8 }}
            />
          </div>
        </motion.div>

        <motion.div
          className="mb-8 p-6 rounded-3xl bg-gradient-to-br from-yellow-50 to-amber-50 border-2 border-yellow-300 shadow-lg relative overflow-hidden"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.6 }}>
          {/* Decorative elements */}
          <div className="absolute top-0 right-0 w-24 h-24 bg-yellow-200 rounded-bl-full opacity-20"></div>
          <div className="absolute bottom-0 left-0 w-16 h-16 bg-amber-200 rounded-tr-full opacity-20"></div>

          <div className="relative z-10">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* File Upload Card */}
              <motion.div
                className="bg-white hover:cursor-pointer p-5 rounded-xl border-2 border-dashed border-yellow-400 hover:border-yellow-500 transition-all"
                whileHover={{ y: -5 }}>
                <div className="flex flex-col items-center text-center">
                  <div className="w-16 h-16 mb-4 bg-yellow-100 rounded-2xl flex items-center justify-center text-yellow-600">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-8 w-8"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                      />
                    </svg>
                  </div>
                  <h4 className="text-lg font-semibold text-amber-800 mb-2">
                    Upload Application Form
                  </h4>
                  <p className="text-sm text-amber-600 mb-4">
                    Auto-fill details from PDF/Word/Image
                  </p>

                  <label className="relative cursor-pointer">
                    <input
                      type="file"
                      onChange={(e) => handleFormExtraction(e.target.files[0])}
                      className="hidden"
                      accept=".pdf,.docx,.doc,.jpg,.jpeg,.png"
                      disabled={loading || formExtractLoading}
                    />
                    <motion.div
                      className={`px-6 py-2 rounded-full hover:cursor-pointer font-medium ${formExtractLoading ? 'bg-[var(--color-counselor)] text-yellow-700' : 'bg-[var(--color-counselor)] text-white hover:bg-yellow-600'}`}
                      whileHover={!formExtractLoading ? { scale: 1.05 } : {}}
                      whileTap={!formExtractLoading ? { scale: 0.95 } : {}}>
                      {formExtractLoading ? (
                        <span className="flex items-center">
                          <FontAwesomeIcon icon={faSpinner} spin className="mr-2" />
                          Processing...
                        </span>
                      ) : (
                        'Choose File'
                      )}
                    </motion.div>
                  </label>
                  {formExtractLoading && (
                    <div className="mt-3 w-full bg-yellow-100 rounded-full h-2">
                      <div
                        className="bg-[var(--color-counselor)] h-2 rounded-full animate-pulse"
                        style={{ width: '70%' }}></div>
                    </div>
                  )}
                </div>
              </motion.div>

              {/* Face Capture Card */}
              <motion.div
                className="bg-white hover:cursor-pointer p-5 rounded-xl border-2 border-dashed border-amber-400 hover:border-amber-500 transition-all"
                whileHover={{ y: -5 }}>
                <div className="flex flex-col items-center text-center">
                  <div className="w-16 h-16 mb-4 bg-amber-100 rounded-2xl flex items-center justify-center text-amber-600">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-8 w-8"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                    </svg>
                  </div>
                  <h4 className="text-lg font-semibold text-amber-800 mb-2">
                    Biometric Registration
                  </h4>
                  <p className="text-sm text-amber-600 mb-4">Capture student's face images</p>

                  <motion.button
                    onClick={() => setShowFaceCapture(true)}
                    className={`w-full px-6 py-2 rounded-full hover:cursor-pointer font-medium ${faceSessionId ? 'bg-green-500 text-white hover:bg-green-600' : 'bg-[var(--color-counselor)] text-white hover:bg-amber-600'}`}
                    disabled={loading || formExtractLoading}
                    whileHover={!loading && !formExtractLoading ? { scale: 1.05 } : {}}
                    whileTap={!loading && !formExtractLoading ? { scale: 0.95 } : {}}>
                    {faceSessionId ? (
                      <span className="flex items-center justify-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 mr-2"
                          viewBox="0 0 20 20"
                          fill="currentColor">
                          <path
                            fillRule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clipRule="evenodd"
                          />
                        </svg>
                        Captured Successfully
                      </span>
                    ) : (
                      <span className="flex  hover:cursor-pointer items-center justify-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 mr-2"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor">
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
                          />
                        </svg>
                        Capture Images
                      </span>
                    )}
                  </motion.button>

                  {faceSessionId && (
                    <div className="mt-3 text-xs text-green-600 bg-green-50 px-2 py-1 rounded-full">
                      Session ID: {faceSessionId.substring(0, 8)}...
                    </div>
                  )}
                </div>
              </motion.div>
            </div>
          </div>
        </motion.div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {fields(courses, batches).map((section, index) => (
            <motion.div
              key={index}
              className="border-2 border-yellow-200 rounded-2xl p-6 bg-white shadow-lg mb-6"
              initial={{ opacity: 0, x: 40 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 + index * 0.1, duration: 0.5 }}>
              <h3 className="text-xl font-bold mb-6 flex items-center justify-center">
                <motion.div
                  className="w-10 h-10 rounded-xl bg-gradient-to-br from-yellow-300 to-yellow-500 flex items-center justify-center mr-3 shadow-lg"
                  whileHover={{
                    y: -3,
                    rotate: 5,
                    boxShadow: '0 8px 20px rgba(244, 196, 48, 0.3)'
                  }}>
                  <FontAwesomeIcon
                    icon={section.icon}
                    className="text-white text-lg drop-shadow-sm"
                  />
                </motion.div>
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-yellow-500 to-amber-600">
                  {section.section}
                </span>
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {section.inputs.map((field, idx) => (
                  <motion.div
                    key={idx}
                    className="flex flex-col relative"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 + idx * 0.03, duration: 0.4 }}>
                    {field.type === 'select' ? (
                      <motion.div
                        className="relative"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}>
                        <Select
                          name={field.name}
                          value={studentForm[field.name]}
                          onChange={(e) => {
                            if (field.name === 'course_id') {
                              const selectedCourse = courses.find(
                                (course) => course.id === e.target.value
                              );
                              setStudentForm({
                                ...studentForm,
                                course_id: e.target.value,
                                course: selectedCourse ? selectedCourse.course_name : ''
                              });
                            } else if (field.name === 'batch_id') {
                              const selectedBatch = batches.find(
                                (batch) => batch.id === e.target.value
                              );
                              setStudentForm({
                                ...studentForm,
                                batch_id: e.target.value,
                                batch_name: selectedBatch ? selectedBatch.batch_name : ''
                              });
                            } else {
                              setStudentForm({ ...studentForm, [field.name]: e.target.value });
                            }
                          }}
                          className="text-black text-lg w-full bg-white border-b border-yellow-400"
                          options={field.options}
                          placeholder={field.placeholder}
                          required={field.required}
                          leftIcon={
                            <motion.div
                              className="w-9 h-9 flex items-center justify-center rounded-lg backdrop-blur-sm bg-white/70 border border-white/20 shadow-[0_4px_12px_rgba(244,196,48,0.15)]"
                              whileHover={{
                                y: -2,
                                boxShadow: '0 6px 16px rgba(244,196,48,0.25)'
                              }}>
                              <FontAwesomeIcon
                                icon={field.leftIcon}
                                className="text-black hover:cursor-pointer text-lg drop-shadow-sm"
                              />
                            </motion.div>
                          }
                        />
                        {autoFilledFields.includes(field.name) && (
                          <motion.span
                            className="absolute top-0 right-0 bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-bl rounded-tr"
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -10 }}
                            transition={{ duration: 0.5 }}>
                            Auto-filled from Form
                          </motion.span>
                        )}
                      </motion.div>
                    ) : field.type === 'file' ? (
                      <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                        <label className="block text-sm font-medium text-black mb-2">
                          {field.placeholder}
                        </label>
                        <input
                          type="file"
                          accept={field.accept}
                          onChange={(e) => handleFileChange(field.name, e.target.files[0])}
                          className="border border-yellow-400 rounded-md px-3 py-2 w-full bg-white text-black"
                        />
                        {documentFiles[field.name] && (
                          <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded flex items-center justify-between">
                            <div className="text-sm text-yellow-700">
                              <p className="font-medium">📄 {documentFiles[field.name].name}</p>
                              <p className="text-xs">
                                Size: {(documentFiles[field.name].size / 1024).toFixed(1)} KB
                              </p>
                            </div>
                            <motion.button
                              type="button"
                              onClick={() => handleFileChange(field.name, null)}
                              className="text-red-500 hover:text-red-700 text-sm"
                              whileHover={{ scale: 1.15 }}
                              whileTap={{ scale: 0.9 }}>
                              ✕
                            </motion.button>
                          </div>
                        )}
                      </motion.div>
                    ) : (
                      <motion.div
                        className="relative"
                        whileHover={{ scale: 1.01 }}
                        whileTap={{ scale: 0.99 }}>
                        <Input
                          name={field.name}
                          type={field.type}
                          value={studentForm[field.name]}
                          onChange={(e) => handleFormChange(field.name, e.target.value)}
                          placeholder={field.placeholder}
                          required={field.required}
                          step={field.step}
                          maxLength={field.maxLength}
                          min={field.min}
                          max={field.max}
                          pattern={field.pattern}
                          readOnly={field.readOnly}
                          leftIcon={
                            <motion.div
                              className="w-9 h-9 flex items-center justify-center rounded-lg backdrop-blur-sm bg-white/70 border border-white/20 shadow-[0_4px_12px_rgba(244,196,48,0.15)]"
                              whileHover={{
                                y: -2,
                                boxShadow: '0 6px 16px rgba(244,196,48,0.25)'
                              }}>
                              <FontAwesomeIcon
                                icon={field.leftIcon}
                                className="text-black hover:cursor-pointer text-lg drop-shadow-sm"
                              />
                            </motion.div>
                          }
                          className={`block w-full hover:cursor-pointer bg-white border-0 border-b-2 border-yellow-400 focus:outline-none focus:ring-0 focus:border-yellow-500 text-black transition-all ${field.readOnly ? 'bg-gray-50 cursor-not-allowed' : ''}`}
                        />
                        {field.readOnly && studentForm[field.name] && (
                          <motion.span
                            className="absolute top-0 right-0 bg-green-100 text-green-800 text-xs px-2 py-1 rounded-bl rounded-tr"
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -10 }}
                            transition={{ duration: 0.5 }}>
                            Auto-calculated
                          </motion.span>
                        )}
                      </motion.div>
                    )}
                  </motion.div>
                ))}
              </div>
              {section.section === 'Student Info' && renderExtractionDetails()}
            </motion.div>
          ))}

          {error && <p className="text-red-600 text-sm text-center -mt-4">{error}</p>}

          {uploadStatus && (
            <motion.div
              className="mt-4 p-4 bg-yellow-50 border border-yellow-400 rounded-md"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.5 }}>
              <h4 className="text-sm font-semibold text-yellow-800 mb-2">
                📋 Last Submission Status
              </h4>
              <div className="text-sm text-yellow-700 space-y-1">
                <p>
                  <strong>Request ID:</strong> {uploadStatus.requestId}
                </p>
                <p>
                  <strong>Temp Student ID:</strong> {uploadStatus.tempStudentId}
                </p>
                <p>
                  <strong>Documents Uploaded:</strong> {uploadStatus.documentCount}
                </p>
                {uploadStatus.documents.length > 0 && (
                  <p>
                    <strong>Files:</strong> {uploadStatus.documents.join(', ')}
                  </p>
                )}
                <p>
                  <strong>Submitted:</strong>{' '}
                  {new Date(uploadStatus.submissionTime).toLocaleString()}
                </p>
                <div className="mt-2">
                  <Button
                    onClick={() => checkDocumentUploadStatus(uploadStatus.requestId)}
                    name="🔍 Check Status"
                    className="text-xs bg-yellow-600 text-white px-3 py-1 rounded hover:bg-yellow-700"
                  />
                  <Button
                    onClick={() => setUploadStatus(null)}
                    name="✕ Clear"
                    className="text-xs bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600 ml-2"
                  />
                </div>
              </div>
            </motion.div>
          )}

          <div className="flex justify-end">
            <motion.button
              type="submit"
              whileHover={{ scale: 1.05, boxShadow: '0 2px 8px #f4c430' }}
              whileTap={{ scale: 0.97 }}
              className={`px-6 py-3 rounded-full text-black font-bold transition-all duration-300 shadow-lg border-2 border-yellow-400 ${loading || formExtractLoading ? 'bg-yellow-200 cursor-not-allowed animate-pulse' : 'bg-yellow-400 hover:bg-yellow-500 hover:cursor-pointer'}`}
              disabled={loading || formExtractLoading}>
              {loading ? (
                <span className="flex items-center gap-2 animate-pulse">
                  <FontAwesomeIcon icon={faSpinner} spin className="text-yellow-400" />
                  <span className="font-semibold text-yellow-700">
                    Submitting... Please wait{' '}
                    <span className="inline-block w-4 text-yellow-500 animate-bounce">•</span>
                    <span className="inline-block w-4 text-yellow-500 animate-bounce delay-150">
                      •
                    </span>
                    <span className="inline-block w-4 text-yellow-500 animate-bounce delay-300">
                      •
                    </span>
                  </span>
                </span>
              ) : (
                <span className="flex items-center gap-2">
                  <FontAwesomeIcon icon={faUserPlus} className="mr-2" /> Submit
                </span>
              )}
            </motion.button>
          </div>

          {Object.values(documentFiles).some((file) => file !== null) && (
            <p className="text-sm text-yellow-700 text-center">
              📎 {Object.values(documentFiles).filter((file) => file !== null).length} file(s)
              selected
            </p>
          )}
        </form>
      </motion.div>

      <FaceCaptureModal
        showFaceCapture={showFaceCapture}
        setShowFaceCapture={setShowFaceCapture}
        loading={loading}
        setLoading={setLoading}
        onFaceCaptureSuccess={handleFaceCaptureSuccess}
      />

      {renderCapturedImages()}
      <div className="absolute top-4 right-5 opacity-20 text-[3rem] pointer-events-none select-none">
        <span style={{ color: '#f4c430' }}>{emoji}</span>
      </div>
      <div className="absolute top-4 left-5 opacity-10 text-[3rem] pointer-events-none select-none">
        <span style={{ color: '#f4c430' }}>🎓</span>
      </div>
    </div>
  );
};

export default AddStudents;
