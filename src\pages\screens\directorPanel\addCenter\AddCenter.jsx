'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { useDispatch } from 'react-redux';
import { setAddCenterData, useDirectorAddCenterServiceMutation } from './addCenter.Slice';
import Toastify from '../../../../components/PopUp/Toastify';
import {
  FiEye,
  FiEyeOff,
  FiUser,
  FiMail,
  FiPhone,
  FiMapPin,
  FiLock,
  FiUserPlus
} from 'react-icons/fi';

const AddCenter = () => {
  const dispatch = useDispatch();
  const [addCenterService] = useDirectorAddCenterServiceMutation();
  const [res, setRes] = useState(null);
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const [centerForm, setCenterForm] = useState({
    name: '',
    email: '',
    phone: '',
    username: '',
    address: '',
    password: ''
  });
  const [errors, setErrors] = useState({
    name: '',
    email: '',
    phone: '',
    username: '',
    address: '',
    password: ''
  });

  // Validation functions
  const validateName = (name) => {
    if (!name) return 'Center name is required';
    if (name.length < 3) return 'Center name must be at least 3 characters long';
    return '';
  };

  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) return 'Email is required';
    if (!emailRegex.test(email)) return 'Please enter a valid email address';
    return '';
  };

  const validatePhone = (phone) => {
    if (!phone) return 'Phone number is required';
    const digitsOnly = phone.replace(/\D/g, '');
    const phoneRegex = /^\+?[\d\s()-]*$/;
    if (!phoneRegex.test(phone))
      return 'Phone number can only contain digits, spaces, hyphens, parentheses, or start with +';
    if (digitsOnly.length < 10  || digitsOnly.length > 12)
      return 'Phone number must contain at least 10 digits';
    return '';
  };

  const validateUsername = (username) => {
    const usernameRegex = /^[a-zA-Z0-9_]{3,}$/;
    if (!username) return 'Username is required';
    if (!usernameRegex.test(username))
      return 'Username must be at least 3 characters long and contain only letters, numbers, or underscores';
    return '';
  };

  const validateAddress = (address) => {
    if (!address) return 'Address is required';
    if (address.length < 10) return 'Address must be at least 10 characters long';
    return '';
  };

  const validatePassword = (password) => {
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    if (!password) return 'Password is required';
    if (!passwordRegex.test(password))
      return 'Password must be at least 8 characters long, with one uppercase, one lowercase, one number, and one special character';
    return '';
  };

  // Handle input change with validation
  const handleInputChange = (field, value) => {
    setCenterForm({ ...centerForm, [field]: value });

    let error = '';
    switch (field) {
      case 'name':
        error = validateName(value);
        break;
      case 'email':
        error = validateEmail(value);
        break;
      case 'phone':
        error = validatePhone(value);
        break;
      case 'username':
        error = validateUsername(value);
        break;
      case 'address':
        error = validateAddress(value);
        break;
      case 'password':
        error = validatePassword(value);
        break;
      default:
        break;
    }
    setErrors({ ...errors, [field]: error });
  };

  // Toggle password visibility
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  // Validate all fields before submission
  const validateForm = () => {
    const newErrors = {
      name: validateName(centerForm.name),
      email: validateEmail(centerForm.email),
      phone: validatePhone(centerForm.phone),
      username: validateUsername(centerForm.username),
      address: validateAddress(centerForm.address),
      password: validatePassword(centerForm.password)
    };
    setErrors(newErrors);
    return Object.values(newErrors).every((error) => error === '');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) {
      setRes({ status: 'error', message: 'Please fix the errors in the form.' });
      return;
    }
    try {
      setLoading(true);
      const response = await addCenterService({
        name: centerForm.name,
        email: centerForm.email,
        phone: centerForm.phone,
        username: centerForm.username,
        address: centerForm.address,
        password: centerForm.password
      }).unwrap();
      dispatch(setAddCenterData(response));
      setRes({ status: 'success', message: 'Center added successfully!' });
      setCenterForm({
        name: '',
        email: '',
        phone: '',
        username: '',
        address: '',
        password: ''
      });
      setErrors({
        name: '',
        email: '',
        phone: '',
        username: '',
        address: '',
        password: ''
      });
    } catch (error) {
      setRes({
        status: 'error',
        message: error?.data?.message || 'Failed to add center. Please try again.'
      });
    } finally {
      setLoading(false);
    }
  };

  const containerVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4, ease: 'easeOut' }
    }
  };

  const inputVariants = {
    hover: {
      scale: 1.02,
      boxShadow: '0px 8px 25px rgba(0, 0, 0, 0.1)',
      transition: { duration: 0.2 }
    },
    focus: {
      scale: 1.01,
      boxShadow: '0px 0px 0px 3px rgba(var(--color-director-rgb), 0.1)',
      transition: { duration: 0.2 }
    }
  };

  const buttonVariants = {
    hover: {
      scale: 1.02,
      boxShadow: '0px 10px 30px rgba(0, 0, 0, 0.2)',
      transition: { duration: 0.2 }
    },
    tap: { scale: 0.98 }
  };

  const iconVariants = {
    hover: { rotate: 5, scale: 1.1 },
    tap: { rotate: -5, scale: 0.9 }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 p-4 md:p-8">
      <Toastify res={res} resClear={() => setRes(null)} />

      <motion.div
        className="max-w-4xl mx-auto"
        variants={containerVariants}
        initial="hidden"
        animate="visible">
        {/* Header Section */}
        <motion.div className="text-center mb-8" variants={itemVariants}>
          <motion.div
            className="inline-flex items-center justify-center w-16 h-16 rounded-full mb-4"
            style={{
              background: `linear-gradient(135deg, var(--color-director), var(--color-director))`,
              boxShadow: '0px 10px 30px rgba(0, 0, 0, 0.1)'
            }}
            whileHover={{ scale: 1.1, rotate: 5 }}
            whileTap={{ scale: 0.9 }}>
            <FiUserPlus className="text-2xl" style={{ color: 'white' }} />
          </motion.div>
          <motion.h1 className="text-4xl font-bold mb-2" style={{ color: 'var(--color-director)' }}>
            Add New Center
          </motion.h1>
          <motion.p className="text-gray-600 text-lg">
            Create a new center with all the required information
          </motion.p>
        </motion.div>

        {/* Form Card */}
        <motion.div
          className="bg-white rounded-3xl shadow-2xl overflow-hidden"
          variants={itemVariants}
          style={{
            background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
            border: '1px solid rgba(0, 0, 0, 0.05)'
          }}>
          <div className="p-8 md:p-12">
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Form Grid */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Center Name */}
                <motion.div variants={itemVariants} className="space-y-2">
                  <label className="flex items-center text-sm font-semibold text-gray-700 mb-2">
                    <FiUser className="mr-2" style={{ color: 'var(--color-director)' }} />
                    Center Name
                  </label>
                  <div className="relative">
                    <motion.input
                      type="text"
                      placeholder="Enter center name"
                      value={centerForm.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      className={`w-full border-2 ${errors.name ? 'border-red-400' : 'border-gray-200'} rounded-xl px-4 py-4 pl-12 focus:outline-none transition-all duration-300 bg-gray-50 focus:bg-white`}
                      style={{
                        focusBorderColor: 'var(--color-director)',
                        fontSize: '16px'
                      }}
                      required
                      variants={inputVariants}
                      whileHover="hover"
                      whileFocus="focus"
                    />
                    <FiUser className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  </div>
                  {errors.name && (
                    <motion.p
                      className="text-red-500 text-sm flex items-center"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}>
                      <span className="mr-1">⚠</span> {errors.name}
                    </motion.p>
                  )}
                </motion.div>

                {/* Email */}
                <motion.div variants={itemVariants} className="space-y-2">
                  <label className="flex items-center text-sm font-semibold text-gray-700 mb-2">
                    <FiMail className="mr-2" style={{ color: 'var(--color-director)' }} />
                    Email Address
                  </label>
                  <div className="relative">
                    <motion.input
                      type="email"
                      placeholder="Enter email address"
                      value={centerForm.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className={`w-full border-2 ${errors.email ? 'border-red-400' : 'border-gray-200'} rounded-xl px-4 py-4 pl-12 focus:outline-none transition-all duration-300 bg-gray-50 focus:bg-white`}
                      style={{
                        focusBorderColor: 'var(--color-director)',
                        fontSize: '16px'
                      }}
                      required
                      variants={inputVariants}
                      whileHover="hover"
                      whileFocus="focus"
                    />
                    <FiMail className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  </div>
                  {errors.email && (
                    <motion.p
                      className="text-red-500 text-sm flex items-center"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}>
                      <span className="mr-1">⚠</span> {errors.email}
                    </motion.p>
                  )}
                </motion.div>

                {/* Phone */}
                <motion.div variants={itemVariants} className="space-y-2">
                  <label className="flex items-center text-sm font-semibold text-gray-700 mb-2">
                    <FiPhone className="mr-2" style={{ color: 'var(--color-director)' }} />
                    Phone Number
                  </label>
                  <div className="relative">
                    <motion.input
                      type="tel"
                      placeholder="Enter phone number"
                      value={centerForm.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      className={`w-full border-2 ${errors.phone ? 'border-red-400' : 'border-gray-200'} rounded-xl px-4 py-4 pl-12 focus:outline-none transition-all duration-300 bg-gray-50 focus:bg-white`}
                      style={{
                        focusBorderColor: 'var(--color-director)',
                        fontSize: '16px'
                      }}
                      required
                      variants={inputVariants}
                      whileHover="hover"
                      whileFocus="focus"
                    />
                    <FiPhone className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  </div>
                  {errors.phone && (
                    <motion.p
                      className="text-red-500 text-sm flex items-center"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}>
                      <span className="mr-1">⚠</span> {errors.phone}
                    </motion.p>
                  )}
                </motion.div>

                {/* Username */}
                <motion.div variants={itemVariants} className="space-y-2">
                  <label className="flex items-center text-sm font-semibold text-gray-700 mb-2">
                    <FiUser className="mr-2" style={{ color: 'var(--color-director)' }} />
                    Username
                  </label>
                  <div className="relative">
                    <motion.input
                      type="text"
                      placeholder="Enter username"
                      value={centerForm.username}
                      onChange={(e) => handleInputChange('username', e.target.value)}
                      className={`w-full border-2 ${errors.username ? 'border-red-400' : 'border-gray-200'} rounded-xl px-4 py-4 pl-12 focus:outline-none transition-all duration-300 bg-gray-50 focus:bg-white`}
                      style={{
                        focusBorderColor: 'var(--color-director)',
                        fontSize: '16px'
                      }}
                      required
                      variants={inputVariants}
                      whileHover="hover"
                      whileFocus="focus"
                    />
                    <FiUser className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  </div>
                  {errors.username && (
                    <motion.p
                      className="text-red-500 text-sm flex items-center"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}>
                      <span className="mr-1">⚠</span> {errors.username}
                    </motion.p>
                  )}
                </motion.div>
              </div>

              {/* Address - Full Width */}
              <motion.div variants={itemVariants} className="space-y-2">
                <label className="flex items-center text-sm font-semibold text-gray-700 mb-2">
                  <FiMapPin className="mr-2" style={{ color: 'var(--color-director)' }} />
                  Address
                </label>
                <div className="relative">
                  <motion.textarea
                    placeholder="Enter complete address"
                    value={centerForm.address}
                    onChange={(e) => handleInputChange('address', e.target.value)}
                    className={`w-full border-2 ${errors.address ? 'border-red-400' : 'border-gray-200'} rounded-xl px-4 py-4 pl-12 focus:outline-none transition-all duration-300 bg-gray-50 focus:bg-white resize-none`}
                    style={{
                      focusBorderColor: 'var(--color-director)',
                      fontSize: '16px'
                    }}
                    rows="4"
                    required
                    variants={inputVariants}
                    whileHover="hover"
                    whileFocus="focus"
                  />
                  <FiMapPin className="absolute left-4 top-6 text-gray-400" />
                </div>
                {errors.address && (
                  <motion.p
                    className="text-red-500 text-sm flex items-center"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}>
                    <span className="mr-1">⚠</span> {errors.address}
                  </motion.p>
                )}
              </motion.div>

              {/* Password */}
              <motion.div variants={itemVariants} className="space-y-2">
                <label className="flex items-center text-sm font-semibold text-gray-700 mb-2">
                  <FiLock className="mr-2" style={{ color: 'var(--color-director)' }} />
                  Password
                </label>
                <div className="relative">
                  <motion.input
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Enter secure password"
                    value={centerForm.password}
                    onChange={(e) => handleInputChange('password', e.target.value)}
                    className={`w-full border-2 ${errors.password ? 'border-red-400' : 'border-gray-200'} rounded-xl px-4 py-4 pl-12 pr-12 focus:outline-none transition-all duration-300 bg-gray-50 focus:bg-white`}
                    style={{
                      focusBorderColor: 'var(--color-director)',
                      fontSize: '16px'
                    }}
                    required
                    variants={inputVariants}
                    whileHover="hover"
                    whileFocus="focus"
                    aria-describedby="password-error"
                  />
                  <FiLock className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <motion.button
                    type="button"
                    onClick={togglePasswordVisibility}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-100"
                    aria-label={showPassword ? 'Hide password' : 'Show password'}
                    variants={iconVariants}
                    whileHover="hover"
                    whileTap="tap">
                    {showPassword ? <FiEyeOff size={20} /> : <FiEye size={20} />}
                  </motion.button>
                </div>
                {errors.password && (
                  <motion.p
                    id="password-error"
                    className="text-red-500 text-sm flex items-center"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}>
                    <span className="mr-1">⚠</span> {errors.password}
                  </motion.p>
                )}
              </motion.div>

              {/* Submit Button */}
              <motion.div variants={itemVariants} className="pt-4">
                <motion.button
                  type="submit"
                  disabled={loading}
                  className="w-full  px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-3"
                  style={{
                    background: `linear-gradient(135deg, var(--color-director), var(--color-director)dd)`,
                    boxShadow: '0px 4px 15px rgba(0, 0, 0, 0.1)',
                    color: 'var(--color-director)'
                  }}
                  variants={buttonVariants}
                  whileHover="hover"
                  whileTap="tap">
                  {loading ? (
                    <>
                      <motion.div
                        className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                        animate={{ rotate: 360 }}
                        transition={{
                          duration: 1,
                          repeat: Number.POSITIVE_INFINITY,
                          ease: 'linear'
                        }}
                      />
                      <span>Adding Center...</span>
                    </>
                  ) : (
                    <>
                      <FiUserPlus size={20} />
                      <span>Add New Center</span>
                    </>
                  )}
                </motion.button>
              </motion.div>
            </form>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default AddCenter;
