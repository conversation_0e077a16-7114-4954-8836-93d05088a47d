// src/components/HomeNav.js

import { Menu, X } from 'lucide-react';
import React, { useState } from 'react';
import { Link } from 'react-router';
// Import icons from Heroicons for the hamburger menu
// import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';

const HomeNav = () => {
  // State to manage the mobile navigation's open/closed status
  const [isNavOpen, setIsNavOpen] = useState(false);

  const navData = [
    { name: 'Home', href: '/' },
    { name: 'About', href: '/about' },
    { name: 'NEET', href: '/neet' },
    { name: 'J<PERSON>', href: '/jee' },
   
    { name: 'Contact Us', href: '/contact' }
  ];

  // Function to close the mobile nav, useful when a link is clicked
  const closeMobileNav = () => {
    setIsNavOpen(false);
  };

  return (
    <header className="bg-white shadow-sm sticky top-0 z-50">
      <nav className="mx-auto flex max-w-7xl items-center justify-between p-4 lg:px-8">
        {/* Logo */}
        <Link to="/" className="text-xl font-bold text-gray-900">
          YourLogo
        </Link>

        {/* Desktop Navigation Links */}
        <ul className="hidden md:flex md:items-center md:space-x-8">
          {navData.map((item) => (
            <li key={item.name}>
              <Link
                to={item.href}
                // Conditionally apply classes for the active link
                className={({ isActive }) =>
                  `text-base font-medium transition-colors duration-300 ${
                    isActive ? 'text-blue-600' : 'text-gray-700 hover:text-blue-600'
                  }`
                }>
                {item.name}
              </Link>
            </li>
          ))}
        </ul>

        {/* Mobile Menu Button (Hamburger) */}
        <div className="md:hidden">
          <button
            onClick={() => setIsNavOpen(!isNavOpen)}
            aria-label="Toggle navigation"
            aria-expanded={isNavOpen}>
            {isNavOpen ? (
              <X className="h-7 w-7 text-gray-800" />
            ) : (
              <Menu className="h-7 w-7 text-gray-800" />
            )}
          </button>
        </div>
      </nav>

      {/* Mobile Navigation Menu (Slides from the side) */}
      <div
        className={`
          md:hidden fixed top-0 left-0 h-full w-2/3 max-w-sm bg-white shadow-xl
          transform transition-transform duration-300 ease-in-out
          ${isNavOpen ? 'translate-x-0' : '-translate-x-full'}
        `}>
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-lg font-semibold text-gray-800">Menu</h2>
          <button onClick={closeMobileNav}>
            <X className="h-7 w-7 text-gray-800" />
          </button>
        </div>
        <ul className="flex flex-col items-start space-y-4 p-4">
          {navData.map((item) => (
            <li key={item.name} className="w-full">
              <Link
                to={item.href}
                onClick={closeMobileNav} // Close menu on link click
                className={({ isActive }) =>
                  `block w-full rounded-md p-3 text-base font-medium transition-colors duration-200 ${
                    isActive ? 'bg-blue-100 text-blue-600' : 'text-gray-700 hover:bg-gray-100'
                  }`
                }>
                {item.name}
              </Link>
            </li>
          ))}
        </ul>
      </div>

      {/* Optional: Overlay for the rest of the page when mobile nav is open */}
      {isNavOpen && (
        <div
          onClick={closeMobileNav}
          className="md:hidden fixed inset-0 bg-black bg-opacity-50 z-[-1]"></div>
      )}
    </header>
  );
};

export default HomeNav;
