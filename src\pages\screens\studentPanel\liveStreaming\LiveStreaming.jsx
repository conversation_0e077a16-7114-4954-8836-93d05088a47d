import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Video, Loader2, Users, Clock, Play, Hash, ArrowLeft, MessageCircle, Send, X } from 'lucide-react';
import { Room, RoomEvent } from "livekit-client";
import {
  useLazyGetActiveStreamsForStudentQuery,
  useJoinStreamAsStudentMutation,
  useSendStudentChatMessageMutation,
  useLazyGetStudentChatHistoryQuery
} from './studentLiveStreaming.slice';

const LiveStreaming = () => {
  // API hooks
  const [getActiveStreams, { data: activeStreamsData, error: streamsError, isLoading: streamsLoading }] = useLazyGetActiveStreamsForStudentQuery();
  const [joinStream, { isLoading: isJoining }] = useJoinStreamAsStudentMutation();
  const [sendChatMessage, { isLoading: isSendingMessage }] = useSendStudentChatMessageMutation();
  const [getChatHistory, { data: chatHistoryData, isLoading: isLoadingHistory }] = useLazyGetStudentChatHistoryQuery();

  // Component state
  const [isLoading, setIsLoading] = useState(false);
  const [manualSessionId, setManualSessionId] = useState('');
  const [showManualJoin, setShowManualJoin] = useState(false);
  const [isViewingStream, setIsViewingStream] = useState(false);

  // Stream viewing states (copied from CenterTraineeLiveViewer)
  const [currentStream, setCurrentStream] = useState(null);
  const [livekitRoom, setLivekitRoom] = useState(null);
  const [livekitConnected, setLivekitConnected] = useState(false);
  const [participants, setParticipants] = useState([]);
  const [remoteVideoTracks, setRemoteVideoTracks] = useState([]);
  const [remoteAudioTracks, setRemoteAudioTracks] = useState([]);
  const [connectionStatus, setConnectionStatus] = useState("");
  const [hasScreenShare, setHasScreenShare] = useState(false);
  const [hasCameraTrack, setHasCameraTrack] = useState(false);

  // Chat states
  const [chatMessages, setChatMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [unreadMessages, setUnreadMessages] = useState(0);

  // Video refs
  const screenVideoRef = useRef(null);
  const cameraVideoRef = useRef(null);
  const cameraMainRef = useRef(null);

  // Load active streams on component mount
  useEffect(() => {
    getActiveStreams();
    // Refresh active streams every 30 seconds
    const interval = setInterval(() => {
      if (!isViewingStream) {
        getActiveStreams();
      }
    }, 30000);
    return () => clearInterval(interval);
  }, [getActiveStreams, isViewingStream]);

  // Cleanup effect for LiveKit room
  useEffect(() => {
    return () => {
      if (livekitRoom) {
        livekitRoom.disconnect();
        setLivekitRoom(null);
      }
    };
  }, [livekitRoom]);

  // HTTP-based chat system (copied from CenterTraineeLiveViewer)
  useEffect(() => {
    if (isViewingStream && currentStream) {
      console.log("💬 STUDENT: Starting HTTP-based chat for session:", currentStream.session_id);
      setChatMessages([]);
      loadChatHistory();
      const pollInterval = setInterval(() => loadChatHistory(), 2000);
      return () => clearInterval(pollInterval);
    }
  }, [isViewingStream, currentStream]);

  const handleRequest = () => {
    setIsLoading(true);
    getActiveStreams();
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  // LiveKit room connection logic (copied from CenterTraineeLiveViewer)
  const connectToLiveKitRoom = async (token, url) => {
    try {
      setConnectionStatus("Connecting...");
      const room = new Room();

      room.on(RoomEvent.Connected, () => {
        setLivekitConnected(true);
        setConnectionStatus("Connected");
      });

      room.on(RoomEvent.Disconnected, () => {
        setLivekitConnected(false);
        setConnectionStatus("Disconnected");
        setIsViewingStream(false);
      });

      room.on(RoomEvent.ParticipantConnected, (participant) => {
        setParticipants((prev) => [...prev, participant]);
      });

      room.on(RoomEvent.ParticipantDisconnected, (participant) => {
        setParticipants((prev) => prev.filter((p) => p.identity !== participant.identity));
      });

      room.on(RoomEvent.TrackSubscribed, (track, publication, participant) => {
        if (track.kind === "video") {
          setRemoteVideoTracks((prev) => {
            const newTracks = [...prev, { track, participant, publication }];
            setHasScreenShare(
              newTracks.some(
                (t) => t.publication.source === "screen_share" || t.publication.source === "screen_share_audio",
              ),
            );
            setHasCameraTrack(
              newTracks.some(
                (t) =>
                  t.publication.source === "camera" ||
                  t.publication.trackName === "teacher_camera" ||
                  !t.publication.source,
              ),
            );
            return newTracks;
          });
        } else if (track.kind === "audio") {
          setRemoteAudioTracks((prev) => {
            const audioElement = new Audio();
            track.attach(audioElement);
            audioElement.play();
            return [...prev, { track, participant, publication, audioElement }];
          });
        }
      });

      room.on(RoomEvent.TrackUnsubscribed, (track, publication, participant) => {
        if (track.kind === "video") {
          setRemoteVideoTracks((prev) => {
            const newTracks = prev.filter((t) => t.track !== track);
            setHasScreenShare(
              newTracks.some(
                (t) => t.publication.source === "screen_share" || t.publication.source === "screen_share_audio",
              ),
            );
            setHasCameraTrack(
              newTracks.some(
                (t) =>
                  t.publication.source === "camera" ||
                  t.publication.trackName === "teacher_camera" ||
                  !t.publication.source,
              ),
            );
            return newTracks;
          });
          track.detach();
        } else if (track.kind === "audio") {
          setRemoteAudioTracks((prev) => {
            const trackInfo = prev.find((t) => t.track === track);
            if (trackInfo && trackInfo.audioElement) {
              track.detach(trackInfo.audioElement);
            }
            return prev.filter((t) => t.track !== track);
          });
        }
      });

      await room.connect(url, token);
      setLivekitRoom(room);
    } catch (err) {
      setConnectionStatus("Connection failed: " + err.message);
      alert("Failed to connect to stream: " + err.message);
    }
  };

  const handleJoinStreamById = async (sessionId) => {
    try {
      const userId = sessionStorage.getItem('userId');
      if (!userId) {
        alert('Please login first to join the stream.');
        return;
      }

      if (!sessionId.trim()) {
        alert('Please enter a valid session ID.');
        return;
      }

      const sessionData = { session_id: sessionId.trim() };
      const response = await joinStream(sessionData).unwrap();

      if (response.token && response.livekit_url) {
        const correctSessionId = response.stream_info?.session_id || response.room_name || sessionId.trim();
        const streamData = { session_id: correctSessionId };
        setCurrentStream(streamData);
        setIsViewingStream(true);
        await connectToLiveKitRoom(response.token, response.livekit_url);
      } else {
        alert('Invalid stream response. Missing connection credentials.');
      }
    } catch (error) {
      alert('Failed to join stream: ' + (error.data?.message || 'Unknown error'));
    }
  };



  const handleLeaveStream = () => {
    if (livekitRoom) {
      livekitRoom.disconnect();
      setLivekitRoom(null);
    }
    setIsViewingStream(false);
    setCurrentStream(null);
    setLivekitConnected(false);
    setParticipants([]);
    setRemoteVideoTracks([]);
    setRemoteAudioTracks([]);
    setConnectionStatus("");
    setHasScreenShare(false);
    setHasCameraTrack(false);
    setChatMessages([]);
    setIsChatOpen(false);
    setUnreadMessages(0);
  };

  const formatUptime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  // Video track attachment effect (copied from CenterTraineeLiveViewer)
  useEffect(() => {
    if (remoteVideoTracks.length === 0) return;
    remoteVideoTracks.forEach(({ track, publication }) => {
      if (publication.source === "screen_share" || publication.source === "screen_share_audio") {
        if (screenVideoRef.current && !track.attachedElements.includes(screenVideoRef.current)) {
          track.attach(screenVideoRef.current);
        }
      } else if (publication.source === "camera" || publication.trackName === "teacher_camera" || !publication.source) {
        if (hasScreenShare) {
          if (cameraVideoRef.current && !track.attachedElements.includes(cameraVideoRef.current)) {
            track.attach(cameraVideoRef.current);
          }
        } else {
          if (cameraMainRef.current && !track.attachedElements.includes(cameraMainRef.current)) {
            track.attach(cameraMainRef.current);
          }
        }
      }
    });
  }, [remoteVideoTracks, hasScreenShare, hasCameraTrack]);

  // Chat functionality
  const loadChatHistory = async () => {
    if (!currentStream) return;
    try {
      const response = await getChatHistory(currentStream.session_id).unwrap();
      if (response.messages) {
        setChatMessages(response.messages);
      }
    } catch (error) {
      console.error("❌ STUDENT: Error loading chat history:", error.message);
    }
  };

  const handleSendChatMessage = async () => {
    if (!newMessage.trim() || !currentStream) return;
    const messageData = {
      session_id: currentStream.session_id,
      message: newMessage.trim(),
      sender_id: sessionStorage.getItem("userId"),
      sender_name: sessionStorage.getItem("name") || "Student",
    };
    try {
      await sendChatMessage(messageData).unwrap();
      setNewMessage("");
      setTimeout(loadChatHistory, 500);
    } catch (error) {
      console.error("❌ STUDENT: Error sending message:", error.message);
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendChatMessage();
    }
  };

  const toggleChat = () => {
    setIsChatOpen(!isChatOpen);
    if (!isChatOpen) {
      setUnreadMessages(0);
    }
  };



  const activeStreams = activeStreamsData?.active_streams || [];

  // If viewing a stream, show the stream viewer UI
  if (isViewingStream && currentStream) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="min-h-screen bg-gradient-to-br from-gray-900 via-indigo-900 to-gray-900"
      >
        {/* Header */}
        <motion.div
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="bg-gradient-to-r from-gray-800 to-indigo-900 text-white p-4 shadow-lg border-b border-gray-700"
        >
          <div className="flex items-center justify-between max-w-7xl mx-auto">
            <div className="flex items-center space-x-4">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleLeaveStream}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-semibold flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Leave Stream
              </motion.button>
              <div>
                <h1 className="text-xl font-bold">Live Stream</h1>
                <p className="text-sm text-gray-300">Session: {currentStream.session_id}</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className={`flex items-center space-x-2 px-3 py-1 rounded-full ${
                livekitConnected ? 'bg-green-600' : 'bg-red-600'
              }`}>
                <div className={`w-2 h-2 rounded-full ${
                  livekitConnected ? 'bg-green-300' : 'bg-red-300'
                }`} />
                <span className="text-sm font-medium">
                  {livekitConnected ? 'Connected' : 'Disconnected'}
                </span>
              </div>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={toggleChat}
                className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg font-semibold flex items-center gap-2 relative"
              >
                <MessageCircle className="h-4 w-4" />
                Chat
                {unreadMessages > 0 && (
                  <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {unreadMessages}
                  </span>
                )}
              </motion.button>
            </div>
          </div>
        </motion.div>

        <div className="flex h-[calc(100vh-80px)] max-w-7xl mx-auto">
          {/* Video Area */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="flex-1 relative bg-black rounded-lg overflow-hidden shadow-2xl"
          >
            <div className="w-full h-full flex items-center justify-center relative">
              <video
                ref={screenVideoRef}
                autoPlay
                playsInline
                className="w-full h-full object-contain"
              />
              {!hasScreenShare && hasCameraTrack && (
                <video
                  ref={cameraMainRef}
                  autoPlay
                  playsInline
                  className="w-full h-full object-contain absolute inset-0"
                />
              )}
              {!hasScreenShare && !hasCameraTrack && (
                <div className="text-white text-center">
                  <Video className="h-16 w-16 mx-auto mb-4 text-gray-400" />
                  <p className="text-lg">Waiting for stream...</p>
                  <p className="text-sm text-gray-400 mt-2">{connectionStatus}</p>
                </div>
              )}
            </div>
            {hasScreenShare && hasCameraTrack && (
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.4 }}
                className="absolute bottom-6 right-6 w-72 h-52 bg-gray-900 rounded-lg overflow-hidden border border-gray-600 shadow-lg"
              >
                <video ref={cameraVideoRef} autoPlay playsInline muted className="w-full h-full object-cover" />
                <div className="absolute bottom-2 left-2 bg-black/60 text-white text-xs px-2 py-1 rounded">
                  Teacher Camera
                </div>
              </motion.div>
            )}
          </motion.div>

          {/* Chat Sidebar */}
          <AnimatePresence>
            {isChatOpen && (
              <motion.div
                initial={{ x: 300, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                exit={{ x: 300, opacity: 0 }}
                transition={{ duration: 0.3 }}
                className="w-80 bg-white shadow-2xl border-l border-gray-200 flex flex-col"
              >
                <div className="bg-indigo-600 text-white p-4 flex items-center justify-between">
                  <h3 className="font-semibold">Chat</h3>
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={toggleChat}
                    className="text-white hover:text-gray-200"
                  >
                    <X className="h-5 w-5" />
                  </motion.button>
                </div>

                <div className="flex-1 overflow-y-auto p-4 space-y-3">
                  {chatMessages.length === 0 ? (
                    <div className="text-center text-gray-500 py-8">
                      <MessageCircle className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                      <p>No messages yet</p>
                      <p className="text-sm">Start the conversation!</p>
                    </div>
                  ) : (
                    chatMessages.map((message, index) => (
                      <div key={index} className="bg-gray-50 rounded-lg p-3">
                        <div className="flex items-center justify-between mb-1">
                          <span className="font-semibold text-sm text-indigo-600">
                            {message.sender_name || 'Unknown'}
                          </span>
                          <span className="text-xs text-gray-500">
                            {new Date(message.timestamp).toLocaleTimeString()}
                          </span>
                        </div>
                        <p className="text-gray-800 text-sm">{message.message}</p>
                      </div>
                    ))
                  )}
                </div>

                <div className="p-4 border-t border-gray-200">
                  <div className="flex gap-2">
                    <input
                      type="text"
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyDown={handleKeyDown}
                      placeholder="Type a message..."
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                    />
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={handleSendChatMessage}
                      disabled={isSendingMessage || !newMessage.trim()}
                      className="bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg flex items-center gap-2"
                    >
                      {isSendingMessage ? <Loader2 className="h-4 w-4 animate-spin" /> : <Send className="h-4 w-4" />}
                    </motion.button>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50 p-4 sm:p-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-8"
        >
          <div className="flex items-center justify-center gap-3 mb-4">
            <Video className="h-12 w-12 text-indigo-600 animate-pulse" />
            <h1 className="text-4xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
              Live Streaming
            </h1>
          </div>
          <p className="text-lg text-gray-600">Join active streams or enter a session ID manually</p>
        </motion.div>

        {/* Manual Join Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="bg-white/90 backdrop-blur-xl shadow-xl rounded-2xl p-6 mb-8 border border-indigo-100/50"
        >
          <div className="flex items-center gap-3 mb-4">
            <Hash className="h-6 w-6 text-indigo-600" />
            <h2 className="text-xl font-semibold text-gray-800">Join Stream Manually</h2>
          </div>
          <div className="flex gap-3">
            <input
              type="text"
              placeholder="Enter Stream Session ID"
              value={manualSessionId}
              onChange={(e) => setManualSessionId(e.target.value)}
              className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
            />
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => handleJoinStreamById(manualSessionId)}
              disabled={isJoining || !manualSessionId.trim()}
              className="bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-lg font-semibold flex items-center gap-2"
            >
              {isJoining ? <Loader2 className="h-5 w-5 animate-spin" /> : <Play className="h-5 w-5" />}
              {isJoining ? 'Joining...' : 'Join'}
            </motion.button>
          </div>
        </motion.div>

        {/* Active Streams Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="bg-white/90 backdrop-blur-xl shadow-xl rounded-2xl p-6 border border-indigo-100/50"
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <Users className="h-6 w-6 text-indigo-600" />
              <h2 className="text-xl font-semibold text-gray-800">Active Streams</h2>
            </div>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleRequest}
              disabled={isLoading}
              className="bg-indigo-500 hover:bg-indigo-600 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium flex items-center gap-2"
            >
              {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Video className="h-4 w-4" />}
              {isLoading ? 'Loading...' : 'Refresh'}
            </motion.button>
          </div>

          {streamsLoading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 text-indigo-500 animate-spin" />
              <p className="ml-3 text-lg text-gray-700">Loading active streams...</p>
            </div>
          ) : streamsError ? (
            <div className="text-center py-12">
              <p className="text-red-600 mb-4">Failed to load active streams</p>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleRequest}
                className="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-lg"
              >
                Try Again
              </motion.button>
            </div>
          ) : activeStreams.length === 0 ? (
            <div className="text-center py-12 text-gray-500">
              <Video className="h-16 w-16 mx-auto mb-4 text-gray-300" />
              <p className="text-lg">No active streams available</p>
              <p className="text-sm mt-2">Check back later or use manual join above</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {activeStreams.map((stream, index) => (
                <motion.div
                  key={stream.session_id || index}
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-lg p-4 border border-indigo-200 hover:shadow-lg transition-shadow"
                >
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-sm font-semibold text-indigo-600">
                      Live Stream #{index + 1}
                    </span>
                    <span className="bg-indigo-100 text-indigo-600 px-2 py-1 rounded text-xs">
                      {stream.quality || "Standard"}
                    </span>
                  </div>
                  <p className="text-gray-600 text-sm mb-3">Teacher: {stream.teacher_id}</p>
                  <div className="grid grid-cols-2 gap-3 mb-4">
                    <div className="text-center bg-white/60 p-2 rounded">
                      <div className="text-lg font-bold text-indigo-600">{stream.viewer_count || 0}</div>
                      <div className="text-xs text-gray-500">Viewers</div>
                    </div>
                    <div className="text-center bg-white/60 p-2 rounded">
                      <div className="text-lg font-bold text-indigo-600 flex items-center justify-center gap-1">
                        <Clock className="h-4 w-4" />
                        {formatUptime(stream.uptime || 0)}
                      </div>
                      <div className="text-xs text-gray-500">Uptime</div>
                    </div>
                  </div>
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 text-center">
                    <p className="text-sm text-yellow-700 font-medium">
                      🔒 Session ID Required
                    </p>
                    <p className="text-xs text-yellow-600 mt-1">
                      Use manual join above with session ID
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default LiveStreaming;
