import React from 'react';
import { motion } from 'framer-motion';
import {
  BookOpen,
  FileText,
  Sparkles,
  Bookmark,
  Library,
  NotebookText,
  PenTool,
  Ruler,
  Calculator
} from 'lucide-react';

function LandingLoader() {
  const orbitCount = 8;
  const particleCount = 10;
  const leftElements = 5;
  const rightElements = 5;

  return (
    <motion.div
      className="fixed inset-0 z-50 flex flex-col  items-center justify-center overflow-hidden"
      style={{ backgroundColor: 'white' }}
      initial={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 1.2 }}
      role="alert"
      aria-label="Loading Paper Based Exam">
      <style jsx>{`
        :root {
          --color-trainee: #f59e0b;
        }
      `}</style>

      {/* Left Side Decorative Elements */}
      <div className="absolute left-0 top-0 h-full w-1/4 flex flex-col items-start justify-center pl-8">
        {[...Array(leftElements)].map((_, i) => (
          <motion.div
            key={`left-${i}`}
            className="mb-6 flex items-center"
            initial={{ x: -100, opacity: 0 }}
            animate={{ x: 0, opacity: 0.6 }}
            transition={{
              delay: 0.5 + i * 0.2,
              duration: 1,
              type: 'spring',
              damping: 10
            }}>
            {i % 4 === 0 && <Bookmark className="text-[var(--color-trainee)] mr-3" size={24} />}
            {i % 4 === 1 && <Library className="text-[var(--color-trainee)] mr-3" size={24} />}
            {i % 4 === 2 && <NotebookText className="text-[var(--color-trainee)] mr-3" size={24} />}
            {i % 4 === 3 && <PenTool className="text-[var(--color-trainee)] mr-3" size={24} />}
            <div className="h-px w-16 bg-gray-300"></div>
          </motion.div>
        ))}
      </div>

      {/* Right Side Decorative Elements */}
      <div className="absolute right-0 top-0 h-full w-1/4 flex flex-col items-end justify-center pr-8">
        {[...Array(rightElements)].map((_, i) => (
          <motion.div
            key={`right-${i}`}
            className="mb-6 flex items-center"
            initial={{ x: 100, opacity: 0 }}
            animate={{ x: 0, opacity: 0.6 }}
            transition={{
              delay: 0.7 + i * 0.2,
              duration: 1,
              type: 'spring',
              damping: 10
            }}>
            <div className="h-px w-16 bg-gray-300"></div>
            {i % 4 === 0 && <Ruler className="text-[var(--color-trainee)] ml-3" size={24} />}
            {i % 4 === 1 && <Calculator className="text-[var(--color-trainee)] ml-3" size={24} />}
            {i % 4 === 2 && <FileText className="text-[var(--color-trainee)] ml-3" size={24} />}
            {i % 4 === 3 && <Sparkles className="text-[var(--color-trainee)] ml-3" size={24} />}
          </motion.div>
        ))}
      </div>

      {/* Floating Background Elements */}
      <motion.div
        className="absolute inset-0 overflow-hidden"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}>
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={`bg-${i}`}
            className="absolute text-[var(--color-trainee)]"
            style={{
              fontSize: `${Math.random() * 20 + 10}px`,
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`
            }}
            animate={{
              y: [0, (Math.random() - 0.5) * 40],
              opacity: [0.1, 0.3, 0.1]
            }}
            transition={{
              duration: Math.random() * 10 + 5,
              repeat: Infinity,
              repeatType: 'reverse',
              delay: Math.random() * 2
            }}>
            {i % 3 === 0 && '✐'}
            {i % 3 === 1 && '✎'}
            {i % 3 === 2 && '✏︎'}
          </motion.div>
        ))}
      </motion.div>

      {/* Holographic Panel with Book */}
      <motion.div
        className="relative w-[300px] h-[400px] z-10"
        initial={{ y: '100vh', opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 1.5, ease: 'easeOut' }}>
        <motion.div
          className="absolute inset-0 bg-[var(--color-trainee)] rounded-lg border border-[var(--color-trainee)]/30 backdrop-blur-sm shadow-[0_0_20px_rgba(245,158,11,0.3)]"
          animate={{
            rotateY: [0, 15, -15, 0],
            scale: [0.95, 1.05, 0.95]
          }}
          transition={{ duration: 4, repeat: Infinity, ease: 'easeInOut' }}>
          <motion.div
            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
            animate={{ scale: [1, 1.1, 1], opacity: [1, 0.9, 1] }}
            transition={{ duration: 2, repeat: Infinity }}>
            <BookOpen className="text-white" size={100} strokeWidth={2} />
          </motion.div>
        </motion.div>

        {/* Orbiting FileText Icons */}
        {[...Array(orbitCount)].map((_, i) => {
          const angle = (i * 2 * Math.PI) / orbitCount;
          return (
            <motion.div
              key={`orbit-${i}`}
              className="absolute w-12 h-12 flex items-center justify-center"
              initial={{ opacity: 0, scale: 0 }}
              animate={{
                x: 120 * Math.cos(angle),
                y: 120 * Math.sin(angle) - 50,
                opacity: [0, 1, 0],
                scale: [0.8, 1.2, 0.8]
              }}
              transition={{
                duration: 3,
                delay: i * 0.2,
                repeat: Infinity,
                ease: 'easeInOut'
              }}
              style={{ top: '50%', left: '50%' }}>
              <FileText className="text-white" size={24} strokeWidth={2} />
            </motion.div>
          );
        })}

        {/* Cyber Orbit Particles */}
        {[...Array(particleCount)].map((_, i) => (
          <motion.div
            key={`particle-${i}`}
            className="absolute w-3 h-3 rounded-full bg-white"
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{
              x: 150 * Math.cos((i * 2 * Math.PI) / particleCount + Date.now() / 2000),
              y: 150 * Math.sin((i * 2 * Math.PI) / particleCount + Date.now() / 2000) - 50,
              opacity: [0, 0.8, 0],
              scale: [0.5, 1, 0.5]
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: 'linear'
            }}
            style={{ top: '50%', left: '50%' }}>
            <Sparkles className="text-white" size={8} />
          </motion.div>
        ))}
      </motion.div>

      {/* Scanline Text Reveal for Heading */}
      <motion.div className="relative mt-12 z-20">
        {/* Animated background shape */}
        <motion.div
          className="absolute -inset-4 bg-[var(--color-trainee)]/10 rounded-xl blur-md"
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 0.3 }}
          transition={{ duration: 1, delay: 1.2 }}
        />

        {/* Decorative border elements */}
        <motion.div
          className="absolute -left-6 top-1/2 h-1 bg-[var(--color-trainee)]"
          initial={{ width: 0 }}
          animate={{ width: '40px' }}
          transition={{ duration: 0.6, delay: 1.5 }}
        />
        <motion.div
          className="absolute -right-6 top-1/2 h-1 bg-[var(--color-trainee)]"
          initial={{ width: 0 }}
          animate={{ width: '40px' }}
          transition={{ duration: 0.6, delay: 1.5 }}
        />

        {/* Main heading with creative reveal */}
        <motion.h2
          className="text-5xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-[var(--color-trainee)] to-amber-600 font-sans tracking-tight"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1, type: 'spring' }}>
          <motion.span
            className="block text-black"
            initial={{ clipPath: 'polygon(0 0, 0 0, 0 100%, 0% 100%)' }}
            animate={{ clipPath: 'polygon(0 0, 100% 0, 100% 100%, 0% 100%)' }}
            transition={{ duration: 1.2, delay: 1.2, ease: [0.19, 1, 0.22, 1] }}>
            Paper Based Exam System
          </motion.span>
          <motion.span
            className="block "
            initial={{ clipPath: 'polygon(0 0, 0 0, 0 100%, 0% 100%)' }}
            animate={{ clipPath: 'polygon(0 0, 100% 0, 100% 100%, 0% 100%)' }}
            transition={{ duration: 1.2, delay: 1.4, ease: [0.19, 1, 0.22, 1] }}>
            Exam System
          </motion.span>
        </motion.h2>

        {/* Animated underline */}
        <motion.div
          className="h-1 mt-2 bg-gradient-to-r from-[var(--color-trainee)] to-transparent"
          initial={{ width: 0 }}
          animate={{ width: '100%' }}
          transition={{ duration: 0.8, delay: 1.8 }}
        />

        {/* Floating particles */}
        {[...Array(4)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute text-[var(--color-trainee)]"
            style={{
              top: `${Math.random() * 40 + 10}%`,
              left: `${Math.random() * 80 + 10}%`,
              fontSize: `${Math.random() * 10 + 8}px`
            }}
            initial={{ opacity: 0, y: 10 }}
            animate={{
              opacity: [0, 0.8, 0],
              y: [-5, 5],
              x: [0, (Math.random() - 0.5) * 20]
            }}
            transition={{
              duration: 3,
              delay: 1.5 + i * 0.3,
              repeat: Infinity,
              repeatType: 'reverse'
            }}>
            {i % 2 === 0 ? '✎' : '✏︎'}
          </motion.div>
        ))}
      </motion.div>
    </motion.div>
  );
}

export default LandingLoader;
