"use client"

import { useState, useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"
import { motion, AnimatePresence } from "framer-motion"
import {
  useAddSubjectMutation,
  useUpdateSubjectMutation,
  useListSubjectsQuery,
  useDeleteSubjectMutation,
} from "./subjects.Slice"
import { setSubjects, setSelectedSubject } from "./subjects.Slice"
import Toastify from "../../../../components/PopUp/Toastify"
import {
  FiBook,
  FiEdit,
  FiTrash2,
  FiX,
  FiPlus,
  FiSave,
  FiSearch,
  FiFilter,
  FiRefreshCw,
  FiEye,
  FiFileText,
  FiList,
} from "react-icons/fi"

const SubjectManagement = () => {
  const dispatch = useDispatch()
  const { subjects, selectedSubject } = useSelector(
    (state) => state.subjects || { subjects: [], selectedSubject: null },
  )

  const [activeTab, setActiveTab] = useState("create")
  const [isEditing, setIsEditing] = useState(false)
  const [selectedSubjectId, setSelectedSubjectId] = useState(null)
  const [showSubjectDetails, setShowSubjectDetails] = useState(false)
  const [res, setRes] = useState(null)

  const [subjectForm, setSubjectForm] = useState({
    subject_name: "",
    description: "",
  })

  const [filters, setFilters] = useState({
    subject_name: "",
  })

  const {
    data: subjectsData,
    isLoading: isSubjectsLoading,
    error: subjectsError,
    refetch: refetchSubjects,
  } = useListSubjectsQuery(filters, { skip: activeTab !== "list" })

  const [addSubject, { isLoading: isAddingSubject }] = useAddSubjectMutation()
  const [updateSubject, { isLoading: isUpdatingSubject }] = useUpdateSubjectMutation()
  const [deleteSubject, { isLoading: isDeletingSubject }] = useDeleteSubjectMutation()

  useEffect(() => {
    if (subjectsData) {
      dispatch(setSubjects(subjectsData))
    }
    if (subjectsError) {
      setRes({
        status: subjectsError.status || 500,
        data: { message: subjectsError.data?.message || "Error fetching subjects" },
      })
    }
  }, [subjectsData, subjectsError, dispatch])

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setSubjectForm((prev) => ({ ...prev, [name]: value }))
  }

  const resetForm = () => {
    console.log("resetForm called at:", new Date().toLocaleString("en-IN", { timeZone: "Asia/Kolkata" }))
    setSubjectForm({
      subject_name: "",
      description: "",
    })
    setIsEditing(false)
    setSelectedSubjectId(null)
    dispatch(setSelectedSubject(null))
  }

  const resClear = () => {
    setRes(null)
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    try {
      if (isEditing) {
        await updateSubject({ subjectId: selectedSubjectId, body: subjectForm }).unwrap()
        setRes({ status: 200, data: { message: "Subject updated successfully!" } })
      } else {
        const response = await addSubject(subjectForm).unwrap()
        setSelectedSubjectId(response.subject_id)
        setRes({ status: 200, data: response })
      }
      resetForm()
    } catch (error) {
      setRes({
        status: error.status || 500,
        data: { message: error.data?.message || `Failed to ${isEditing ? "update" : "create"} subject` },
      })
      console.error(`${isEditing ? "Update" : "Create"} subject error:`, error)
    }
  }

  const handleDeleteSubject = async (subjectId) => {
    if (!window.confirm("Are you sure you want to delete this subject?")) {
      return
    }

    try {
      await deleteSubject(subjectId).unwrap()
      setRes({ status: 200, data: { message: "Subject deleted successfully!" } })
      refetchSubjects()
      if (selectedSubjectId === subjectId) {
        setShowSubjectDetails(false)
      }
    } catch (error) {
      setRes({
        status: error.status || 500,
        data: { message: error.data?.message || "Failed to delete subject" },
      })
      console.error("Delete subject error:", error)
    }
  }

  const handleEditSubject = (subject) => {
    console.log("Editing subject with ID:", subject.subject_id)
    setSelectedSubjectId(subject.subject_id)
    setIsEditing(true)
    setActiveTab("create")
    setSubjectForm({
      subject_name: subject.subject_name || "",
      description: subject.description || "",
    })
    dispatch(setSelectedSubject(subject))
  }

  const viewSubjectDetails = (subject) => {
    dispatch(setSelectedSubject(subject))
    setShowSubjectDetails(true)
  }

  const applyFilters = () => {
    refetchSubjects()
  }

  const clearFilters = () => {
    setFilters({ subject_name: "" })
    refetchSubjects()
  }

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4, ease: "easeOut", staggerChildren: 0.1 },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 15 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.3, ease: "easeOut" } },
  }

  const buttonVariants = {
    hover: { scale: 1.02, transition: { duration: 0.2 } },
    tap: { scale: 0.98 },
  }

  const renderCreateSubjectForm = () => (
    <motion.div
      className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden max-w-2xl mx-auto"
      variants={itemVariants}
      initial="hidden"
      animate="visible"
    >
      <div
        className="px-6 py-4 border-b"
        style={{
          backgroundColor: "var(--color-director-light)",
          borderColor: "var(--color-director-border)",
        }}
      >
        <div className="flex items-center">
          {isEditing ? (
            <FiEdit className="mr-3 text-lg" style={{ color: "var(--color-director)" }} />
          ) : (
            <FiPlus className="mr-3 text-lg" style={{ color: "var(--color-director)" }} />
          )}
          <div>
            <h2 className="text-lg font-medium text-gray-900">{isEditing ? "Edit Subject" : "Create New Subject"}</h2>
            <p className="text-gray-600 text-sm mt-1">
              {isEditing ? "Modify subject information" : "Add a new subject to your curriculum"}
            </p>
          </div>
        </div>
      </div>

      <div className="p-6">
        <form onSubmit={handleSubmit} className="space-y-5">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
            {/* Subject Name Input */}
            <motion.div className="space-y-2" variants={itemVariants}>
              <label className="flex items-center text-sm font-medium text-gray-700">
                <FiBook className="mr-2 text-sm" style={{ color: "var(--color-director)" }} />
                Subject Name
              </label>
              <motion.input
                type="text"
                name="subject_name"
                placeholder="Enter subject name"
                value={subjectForm.subject_name}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:border-[var(--color-director)] bg-white transition-all duration-200"
                required
                whileFocus={{ scale: 1.005 }}
              />
            </motion.div>

            {/* Description Input */}
            <motion.div className="space-y-2" variants={itemVariants}>
              <label className="flex items-center text-sm font-medium text-gray-700">
                <FiFileText className="mr-2 text-sm" style={{ color: "var(--color-director)" }} />
                Description
              </label>
              <motion.textarea
                name="description"
                placeholder="Enter subject description"
                value={subjectForm.description}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:border-[var(--color-director)] bg-white resize-none transition-all duration-200"
                rows="3"
                required
                whileFocus={{ scale: 1.005 }}
              />
            </motion.div>
          </div>

          {/* Action Buttons */}
          <motion.div className="pt-4 flex gap-3" variants={itemVariants}>
            <motion.button
              type="submit"
              disabled={isAddingSubject || isUpdatingSubject}
              className="flex-1 text-white px-4 py-2 rounded-md font-medium text-sm transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
              style={{
                backgroundColor:
                  isAddingSubject || isUpdatingSubject ? "var(--color-director-border)" : "var(--color-director)",
              }}
              variants={buttonVariants}
              whileHover="hover"
              whileTap="tap"
            >
              {isAddingSubject || isUpdatingSubject ? (
                <>
                  <motion.div
                    className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                    animate={{ rotate: 360 }}
                    transition={{
                      duration: 1,
                      repeat: Number.POSITIVE_INFINITY,
                      ease: "linear",
                    }}
                  />
                  <span>Processing...</span>
                </>
              ) : (
                <>
                  {isEditing ? <FiSave size={16} /> : <FiPlus size={16} />}
                  <span>{isEditing ? "Update Subject" : "Create Subject"}</span>
                </>
              )}
            </motion.button>

            {isEditing && (
              <motion.button
                type="button"
                onClick={resetForm}
                className="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md font-medium text-sm transition-all duration-200 flex items-center justify-center space-x-2"
                variants={buttonVariants}
                whileHover="hover"
                whileTap="tap"
              >
                <FiX size={16} />
                <span>Cancel Edit</span>
              </motion.button>
            )}
          </motion.div>
        </form>
      </div>
    </motion.div>
  )

  // const renderFilters = () => (
  //   // <motion.div
  //   //   className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-6"
  //   //   variants={itemVariants}
  //   //   initial="hidden"
  //   //   animate="visible"
  //   // >
  //   //   <div
  //   //     className="px-6 py-4 border-b"
  //   //     style={{
  //   //       backgroundColor: "var(--color-director-light)",
  //   //       borderColor: "var(--color-director-border)",
  //   //     }}
  //   //   >
  //   //     <div className="flex items-center">
  //   //       <FiFilter className="mr-3 text-lg" style={{ color: "var(--color-director)" }} />
  //   //       <div>
  //   //         <h3 className="text-lg font-medium text-gray-900">Filter Subjects</h3>
  //   //         <p className="text-gray-600 text-sm mt-1">Search and filter your subjects</p>
  //   //       </div>
  //   //     </div>
  //   //   </div>

  //   //   <div className="p-6">
  //   //     <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
  //   //       <div className="relative">
  //   //         <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
  //   //         <input
  //   //           type="text"
  //   //           name="subject_name"
  //   //           placeholder="Search by subject name"
  //   //           value={filters.subject_name}
  //   //           onChange={(e) => setFilters({ ...filters, subject_name: e.target.value })}
  //   //           className="w-full border border-gray-300 rounded-md pl-10 pr-3 py-2 text-sm focus:outline-none focus:border-[var(--color-director)] bg-white transition-all duration-200"
  //   //         />
  //   //       </div>
  //   //     </div>

  //   //     <div className="flex gap-3">
  //   //       <motion.button
  //   //         onClick={applyFilters}
  //   //         disabled={isSubjectsLoading}
  //   //         className="text-white px-4 py-2 rounded-md font-medium text-sm transition-all duration-200 disabled:opacity-50 flex items-center space-x-2"
  //   //         style={{ backgroundColor: "var(--color-director)" }}
  //   //         variants={buttonVariants}
  //   //         whileHover="hover"
  //   //         whileTap="tap"
  //   //       >
  //   //         <FiSearch size={16} />
  //   //         <span>Apply Filters</span>
  //   //       </motion.button>

  //   //       <motion.button
  //   //         onClick={clearFilters}
  //   //         className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md font-medium text-sm transition-all duration-200 flex items-center space-x-2"
  //   //         variants={buttonVariants}
  //   //         whileHover="hover"
  //   //         whileTap="tap"
  //   //       >
  //   //         <FiRefreshCw size={16} />
  //   //         <span>Clear Filters</span>
  //   //       </motion.button>
  //   //     </div>
  //   //   </div>
  //   // </motion.div>
  // )

  const renderSubjectsList = () => (
    <motion.div
      className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
      variants={itemVariants}
      initial="hidden"
      animate="visible"
    >
      <div
        className="px-6 py-4 border-b"
        style={{
          backgroundColor: "var(--color-director-light)",
          borderColor: "var(--color-director-border)",
        }}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <FiList className="mr-3 text-lg" style={{ color: "var(--color-director)" }} />
            <div>
              <h2 className="text-lg font-medium text-gray-900">All Subjects</h2>
              <p className="text-gray-600 text-sm mt-1">
                {subjectsData?.length || 0} subject{(subjectsData?.length || 0) !== 1 ? "s" : ""} available
              </p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-2xl font-semibold" style={{ color: "var(--color-director)" }}>
              {subjectsData?.length || 0}
            </div>
            <div className="text-xs text-gray-500">Total</div>
          </div>
        </div>
      </div>

      <div className="overflow-x-auto">
        {isSubjectsLoading ? (
          <div className="text-center py-12">
            <motion.div
              className="inline-block w-8 h-8 border-2 border-gray-300 rounded-full mb-4"
              style={{ borderTopColor: "var(--color-director)" }}
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: "linear" }}
            />
            <h3 className="text-lg font-medium text-gray-700 mb-1">Loading Subjects</h3>
            <p className="text-gray-500 text-sm">Please wait...</p>
          </div>
        ) : subjectsData?.length === 0 ? (
          <div className="text-center py-12">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3 }}
            >
              <FiBook className="mx-auto text-5xl text-gray-300 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Subjects Found</h3>
              <p className="text-gray-600 text-sm mb-6 max-w-sm mx-auto">
                Start building your curriculum by creating your first subject.
              </p>
              <motion.button
                onClick={() => setActiveTab("create")}
                className="inline-flex items-center px-4 py-2 text-white rounded-md font-medium text-sm transition-all duration-200"
                style={{ backgroundColor: "var(--color-director)" }}
                variants={buttonVariants}
                whileHover="hover"
                whileTap="tap"
              >
                <FiPlus className="mr-2" size={16} />
                Create First Subject
              </motion.button>
            </motion.div>
          </div>
        ) : (
          <div className="overflow-hidden">
            <table className="w-full">
              <thead style={{ backgroundColor: "var(--color-director-light)" }}>
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                    Subject Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                    Description
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                <AnimatePresence>
                  {subjectsData?.map((subject, index) => (
                    <motion.tr
                      key={subject.subject_id}
                      className="hover:bg-gray-50 transition-colors duration-150"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ delay: index * 0.05 }}
                    >
                      <td className="px-6 py-4 text-sm font-medium text-gray-900">{subject.subject_name}</td>
                      <td className="px-6 py-4 text-sm text-gray-600 max-w-xs truncate">{subject.description}</td>
                      <td className="px-6 py-4 text-sm space-x-3">
                        {/* <motion.button
                          onClick={() => viewSubjectDetails(subject)}
                          className="inline-flex items-center text-xs font-medium px-2 py-1 rounded-md transition-colors duration-200"
                          style={{
                            color: "var(--color-director)",
                            backgroundColor: "var(--color-director-light)",
                          }}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <FiEye className="mr-1" size={12} />
                          View
                        </motion.button> */}

                        <motion.button
                          onClick={() => handleEditSubject(subject)}
                          className="inline-flex items-center text-xs font-medium text-amber-600 hover:text-amber-800 px-2 py-1 rounded-md hover:bg-amber-50 transition-colors duration-200"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <FiEdit className="mr-1" size={12} />
                          Edit
                        </motion.button>

                        <motion.button
                          onClick={() => handleDeleteSubject(subject.subject_id)}
                          disabled={isDeletingSubject}
                          className="inline-flex items-center text-xs font-medium text-red-600 hover:text-red-800 px-2 py-1 rounded-md hover:bg-red-50 transition-colors duration-200 disabled:opacity-50"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <FiTrash2 className="mr-1" size={12} />
                          Delete
                        </motion.button>
                      </td>
                    </motion.tr>
                  ))}
                </AnimatePresence>
              </tbody>
            </table>
          </div>
        )}
      </div>
    </motion.div>
  )

  const renderSubjectDetails = () => (
    <AnimatePresence>
      {showSubjectDetails && (
        <motion.div
          className="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={() => setShowSubjectDetails(false)}
        >
          <motion.div
            className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4"
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            onClick={(e) => e.stopPropagation()}
          >
            <div
              className="px-6 py-4 border-b"
              style={{
                backgroundColor: "var(--color-director-light)",
                borderColor: "var(--color-director-border)",
              }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <FiEye className="mr-3 text-lg" style={{ color: "var(--color-director)" }} />
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">Subject Details</h3>
                    <p className="text-gray-600 text-sm mt-1">View subject information</p>
                  </div>
                </div>
                <motion.button
                  onClick={() => setShowSubjectDetails(false)}
                  className="p-2 hover:bg-gray-100 rounded-md transition-colors"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <FiX className="text-lg text-gray-500" />
                </motion.button>
              </div>
            </div>

            <div className="p-6">
              {selectedSubject && (
                <div className="space-y-4">
                  <div>
                    <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                      <FiBook className="mr-2 text-sm" style={{ color: "var(--color-director)" }} />
                      Subject Name
                    </label>
                    <p
                      className="text-sm text-gray-900 p-3 rounded-md"
                      style={{ backgroundColor: "var(--color-director-light)" }}
                    >
                      {selectedSubject.subject_name}
                    </p>
                  </div>

                  <div>
                    <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                      <FiFileText className="mr-2 text-sm" style={{ color: "var(--color-director)" }} />
                      Description
                    </label>
                    <p
                      className="text-sm text-gray-900 p-3 rounded-md"
                      style={{ backgroundColor: "var(--color-director-light)" }}
                    >
                      {selectedSubject.description}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )

  return (
    <div
      className="min-h-screen bg-gray-50 p-4 md:p-8"
      style={{
        "--color-director": "#7d1e1c",
        "--color-director-light": "rgba(125, 30, 28, 0.05)",
        "--color-director-medium": "rgba(125, 30, 28, 0.1)",
        "--color-director-dark": "rgba(125, 30, 28, 0.15)",
        "--color-director-border": "rgba(125, 30, 28, 0.2)",
        "--color-director-hover": "rgba(125, 30, 28, 0.9)",
      }}
    >
      <Toastify res={res} resClear={resClear} />

      <motion.div className="max-w-6xl mx-auto" variants={containerVariants} initial="hidden" animate="visible">
        {/* Header */}
        <motion.div className="text-center mb-8" variants={itemVariants}>
          <motion.div
            className="inline-flex items-center justify-center w-12 h-12 rounded-lg mb-4"
            style={{ backgroundColor: "var(--color-director-medium)" }}
            whileHover={{ scale: 1.05 }}
          >
            <FiBook className="text-xl" style={{ color: "var(--color-director)" }} />
          </motion.div>
          <h1 className="text-2xl font-semibold text-gray-900 mb-2">Subject Management</h1>
          <p className="text-gray-600 text-sm max-w-md mx-auto">Create and manage your academic subjects</p>
        </motion.div>

        {/* Tab Navigation */}
        <motion.div className="mb-8" variants={itemVariants}>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-1 max-w-md mx-auto">
            <nav className="flex">
              <motion.button
                onClick={() => setActiveTab("create")}
                className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-all duration-200 flex items-center justify-center space-x-2 ${
                  activeTab === "create" ? "text-white shadow-sm" : "text-gray-600 hover:text-gray-800 hover:bg-gray-50"
                }`}
                style={{
                  backgroundColor: activeTab === "create" ? "var(--color-director)" : "transparent",
                }}
                variants={buttonVariants}
                whileHover="hover"
                whileTap="tap"
              >
                <FiPlus size={16} />
                <span>Create Subject</span>
              </motion.button>

              <motion.button
                onClick={() => setActiveTab("list")}
                className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-all duration-200 flex items-center justify-center space-x-2 ${
                  activeTab === "list" ? "text-white shadow-sm" : "text-gray-600 hover:text-gray-800 hover:bg-gray-50"
                }`}
                style={{
                  backgroundColor: activeTab === "list" ? "var(--color-director)" : "transparent",
                }}
                variants={buttonVariants}
                whileHover="hover"
                whileTap="tap"
              >
                <FiList size={16} />
                <span>List Subjects</span>
              </motion.button>
            </nav>
          </div>
        </motion.div>

        {/* Content */}
        <AnimatePresence mode="wait">
          {activeTab === "create" && (
            <motion.div
              key="create"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.3 }}
            >
              {renderCreateSubjectForm()}
            </motion.div>
          )}

          {activeTab === "list" && (
            <motion.div
              key="list"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              {/* {renderFilters()} */}
              {renderSubjectsList()}
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      {renderSubjectDetails()}
    </div>
  )
}

export default SubjectManagement
