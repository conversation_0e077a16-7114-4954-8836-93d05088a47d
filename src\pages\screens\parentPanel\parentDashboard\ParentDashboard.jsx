import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  User,
  Phone,
  Mail,
  Calendar,
  Briefcase,
  Heart,
  DollarSign,
  Users,
  Activity,
  ChevronRight,
  Star,
  Shield,
  BookOpen,
  GraduationCap,
  BarChart2,
  CreditCard,
  FileText,
  PlusCircle
} from 'lucide-react';
import { useGetParentsQuery } from '../../studentPanel/studentsDashboard/students.Slice';

const ParentDashboard = () => {
  const parentId = sessionStorage.getItem('userId');
  const [activeTab, setActiveTab] = useState('overview');
  const [isLoadingData, setIsLoadingData] = useState(true);

  const { data: parentData, isLoading, isError, error } = useGetParentsQuery(parentId);

  // Simulate loading delay for better UX
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoadingData(false);
    }, 1500);
    return () => clearTimeout(timer);
  }, []);

  const parent = parentData?.parent || {
    username: sessionStorage.getItem('name') || 'Unknown',
    first_name: sessionStorage.getItem('first_name') || 'Unknown',
    last_name: sessionStorage.getItem('last_name') || 'Unknown',
    parent_email: 'Unknown',
    phone: 'Unknown',
    occupation: 'Unknown',
    relationship: 'Unknown',
    created_at: 'Unknown',
    annual_income_inr: null,
    student_id: 'Unknown',
    is_active: false
  };

  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        bounce: 0.4,
        duration: 0.8
      }
    }
  };

  const floating = {
    animate: {
      y: [0, -15, 0],
      transition: {
        duration: 4,
        repeat: Infinity,
        ease: 'easeInOut'
      }
    }
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: <BookOpen size={18} /> },
    { id: 'financial', label: 'Financial', icon: <DollarSign size={18} /> }
  ];

  // Loading skeleton component
  const LoadingSkeleton = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <motion.div
            key={i}
            className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 h-32"
            initial={{ opacity: 0 }}
            animate={{
              opacity: [0.5, 1, 0.5],
              transition: {
                duration: 1.5,
                repeat: Infinity,
                ease: 'easeInOut'
              }
            }}
          />
        ))}
      </div>
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 h-96">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="h-20 bg-gray-100 rounded-lg animate-pulse" />
          ))}
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 to-blue-50 p-4 md:p-8">
      {/* Hero Section with Parallax Effect */}
      <motion.section
        className="relative overflow-hidden rounded-3xl bg-[var(--color-parents)] p-8 text-white shadow-xl mb-8"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}>
        {/* Floating elements with different animations */}
        <motion.div
          className="absolute top-10 left-10 w-24 h-24 rounded-full bg-white/10"
          variants={floating}
          animate="animate"
        />
        <motion.div
          className="absolute bottom-20 right-20 w-16 h-16 rounded-full bg-white/10"
          variants={floating}
          animate="animate"
          style={{ animationDelay: '0.5s' }}
        />
        <motion.div
          className="absolute top-1/4 right-1/4 w-8 h-8 rounded-full bg-white/20"
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.3, 0.6, 0.3],
            transition: {
              duration: 5,
              repeat: Infinity,
              ease: 'easeInOut'
            }
          }}
        />

        <div className="relative z-10">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
            <div>
              <motion.h1
                className="text-3xl md:text-4xl font-bold mb-2"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 }}>
                Welcome back, {parent.first_name}!
              </motion.h1>
              <motion.p
                className="text-blue-100 max-w-lg"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 }}>
                Here's everything you need to know about your child's progress and account details.
              </motion.p>
            </div>

            <motion.div
              className="mt-6 md:mt-0 flex items-center gap-4 bg-white/10 backdrop-blur-sm p-4 rounded-xl border border-white/20"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.4 }}
              whileHover={{ scale: 1.05 }}>
              <motion.div
                className="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center"
                whileHover={{ rotate: 360 }}
                transition={{ duration: 1 }}>
                <User className="text-white" size={24} />
              </motion.div>
              <div>
                <p className="font-medium">{parent.username}</p>
                <p className="text-sm text-white">{parent.relationship} of Student</p>
              </div>
            </motion.div>
          </div>
        </div>
      </motion.section>

      {/* Tabs with animated underline */}
      <motion.div
        className="flex gap-2 mb-8 relative"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}>
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`relative flex items-center gap-2 px-4 py-2 rounded-full transition-all ${activeTab === tab.id ? 'text-blue-600' : 'text-gray-600 hover:bg-white/50'}`}>
            {tab.icon}
            <span>{tab.label}</span>
            {activeTab === tab.id && (
              <motion.div
                className="absolute bottom-0 left-0 right-0 h-1 bg-blue-500 rounded-full"
                layoutId="underline"
                transition={{ type: 'spring', bounce: 0.3, duration: 0.6 }}
              />
            )}
          </button>
        ))}
      </motion.div>

      {/* Loading State */}
      {isLoadingData || isLoading ? (
        <LoadingSkeleton />
      ) : (
        <AnimatePresence mode="wait">
          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <motion.div
              key="overview"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}>
              {/* Stats Grid with 3D tilt effect */}
              <motion.div
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
                variants={container}
                initial="hidden"
                animate="show">
                {/* Stat Card 1 */}
                <motion.div
                  className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 overflow-hidden relative group"
                  variants={item}
                  whileHover={{ y: -5 }}
                  whileTap={{ scale: 0.98 }}>
                  <div className="absolute top-0 right-0 w-16 h-16 bg-blue-100/30 rounded-bl-full" />
                  <div className="relative z-10 flex items-center gap-4">
                    <motion.div
                      className="p-3 rounded-lg bg-blue-100 text-blue-600"
                      whileHover={{ rotate: 15 }}>
                      <User size={20} />
                    </motion.div>
                    <div>
                      <p className="text-sm text-gray-500">Parent Name</p>
                      <p className="text-lg font-semibold">
                        {parent.first_name} {parent.last_name}
                      </p>
                    </div>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </motion.div>

                {/* Stat Card 2 */}
                <motion.div
                  className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 overflow-hidden relative group"
                  variants={item}
                  whileHover={{ y: -5 }}>
                  <div className="absolute top-0 right-0 w-16 h-16 bg-green-100/30 rounded-bl-full" />
                  <div className="relative z-10 flex items-center gap-4">
                    <motion.div
                      className="p-3 rounded-lg bg-green-100 text-green-600"
                      whileHover={{ rotate: 15 }}>
                      <Phone size={20} />
                    </motion.div>
                    <div>
                      <p className="text-sm text-gray-500">Contact Number</p>
                      <p className="text-lg font-semibold">{parent.phone || 'Not provided'}</p>
                    </div>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-br from-green-50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </motion.div>

                {/* Stat Card 3 */}
                <motion.div
                  className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 overflow-hidden relative group"
                  variants={item}
                  whileHover={{ y: -5 }}>
                  <div className="absolute top-0 right-0 w-16 h-16 bg-purple-100/30 rounded-bl-full" />
                  <div className="relative z-10 flex items-center gap-4">
                    <motion.div
                      className="p-3 rounded-lg bg-purple-100 text-purple-600"
                      whileHover={{ rotate: 15 }}>
                      <Mail size={20} />
                    </motion.div>
                    <div>
                      <p className="text-sm text-gray-500">Email Address</p>
                       <a
                          href={`mailto:${parent.parent_email}`}>
                      <p className="text-base sm:text-lg md:text-xl font-semibold">
                        {parent.parent_email}
                      </p>
                      </a>
                    </div>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </motion.div>

                {/* Stat Card 4 */}
                <motion.div
                  className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 overflow-hidden relative group"
                  variants={item}
                  whileHover={{ y: -5 }}>
                  <div className="absolute top-0 right-0 w-16 h-16 bg-amber-100/30 rounded-bl-full" />
                  <div className="relative z-10 flex items-center gap-4">
                    <motion.div
                      className="p-3 rounded-lg bg-amber-100 text-amber-600"
                      whileHover={{ rotate: 15 }}>
                      <Shield size={20} />
                    </motion.div>
                    <div>
                      <p className="text-sm text-gray-500">Account Status</p>
                      <p
                        className={`text-lg font-semibold ${parent.is_active ? 'text-green-600' : 'text-red-600'}`}>
                        {parent.is_active ? 'Active' : 'Inactive'}
                      </p>
                    </div>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-br from-amber-50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </motion.div>
              </motion.div>

              {/* Details Section with accordion effect */}
              <motion.div
                className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}>
                <div className="p-6 border-b border-gray-100">
                  <motion.h2
                    className="text-xl font-semibold flex items-center gap-2"
                    whileHover={{ x: 5 }}>
                    <motion.div animate={{ rotate: [0, 10, -10, 0] }} transition={{ duration: 1 }}>
                      <Star className="text-yellow-500" size={20} />
                    </motion.div>
                    <span>Parent Details</span>
                  </motion.h2>
                </div>

                <motion.div
                  className="grid grid-cols-1 md:grid-cols-2 gap-6 p-6"
                  variants={container}
                  initial="hidden"
                  animate="show">
                  {[
                    {
                      label: 'Username',
                      value: parent.username,
                      icon: <User className="text-amber-500" />
                    },
                    {
                      label: 'Occupation',
                      value: parent.occupation || 'Not specified',
                      icon: <Briefcase className="text-blue-500" />
                    },
                    {
                      label: 'Relationship',
                      value: parent.relationship,
                      icon: <Heart className="text-pink-500" />
                    },
                    {
                      label: 'Annual Income',
                      value: parent.annual_income_inr
                        ? `₹${parent.annual_income_inr.toLocaleString()}`
                        : 'Not specified',
                      icon: <DollarSign className="text-green-500" />
                    },
                    {
                      label: 'Member Since',
                      value:
                        parent.created_at !== 'Unknown'
                          ? new Date(parent.created_at).toLocaleDateString()
                          : 'Unknown',
                      icon: <Calendar className="text-purple-500" />
                    }
                  ].map((field, index) => (
                    <motion.div
                      key={index}
                      className="flex items-start gap-4 p-4 hover:bg-gray-50 rounded-lg transition-colors cursor-pointer relative overflow-hidden"
                      variants={item}
                      whileHover={{
                        scale: 1.02,
                        boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
                      }}
                      whileTap={{ scale: 0.98 }}>
                      <motion.div
                        className="p-2 rounded-lg bg-gray-100 text-gray-600"
                        whileHover={{ rotate: 15 }}>
                        {field.icon}
                      </motion.div>
                      <div className="flex-1">
                        <p className="text-sm text-gray-500">{field.label}</p>
                        <p className="font-medium">{field.value}</p>
                      </div>
                      <motion.div whileHover={{ x: 5 }}>
                        <ChevronRight className="text-gray-400" size={18} />
                      </motion.div>
                      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 to-transparent opacity-0 hover:opacity-100 transition-opacity" />
                    </motion.div>
                  ))}
                </motion.div>
              </motion.div>
            </motion.div>
          )}

          {/* Financial Tab */}
          {activeTab === 'financial' && (
            <motion.div
              key="financial"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="space-y-6">
              <motion.div
                className="grid grid-cols-1 md:grid-cols-2 gap-6"
                variants={container}
                initial="hidden"
                animate="show">
                {/* Financial Overview Card */}
                <motion.div
                  className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100"
                  variants={item}
                  whileHover={{ y: -5 }}>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="font-semibold text-lg flex items-center gap-2">
                      <DollarSign className="text-green-500" />
                      Financial Overview
                    </h3>
                    <div className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                      {parent.annual_income_inr ? 'Verified' : 'Not Specified'}
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm text-gray-500">Annual Income</p>
                      <p className="text-2xl font-bold">
                        {parent.annual_income_inr
                          ? `₹${parent.annual_income_inr.toLocaleString()}`
                          : 'Not specified'}
                      </p>
                    </div>
                    <div className="h-2 bg-gray-100 rounded-full overflow-hidden">
                      <motion.div
                        className="h-full bg-gradient-to-r from-green-400 to-blue-500"
                        initial={{ width: 0 }}
                        animate={{
                          width: parent.annual_income_inr
                            ? `${Math.min(100, (parent.annual_income_inr / 500000) * 100)}%`
                            : '0%'
                        }}
                        transition={{ duration: 1, delay: 0.3 }}
                      />
                    </div>
                    <p className="text-sm text-gray-500">
                      {parent.annual_income_inr
                        ? 'Based on your declared income'
                        : 'Please update your financial information'}
                    </p>
                  </div>
                </motion.div>

                {/* Payment History Card */}
                <motion.div
                  className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100"
                  variants={item}
                  whileHover={{ y: -5 }}>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="font-semibold text-lg flex items-center gap-2">
                      <CreditCard className="text-blue-500" />
                      Payment History
                    </h3>
                    <div className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                      Coming Soon
                    </div>
                  </div>
                  <div className="flex flex-col items-center justify-center h-32">
                    <FileText className="text-gray-300 mb-2" size={40} />
                    <p className="text-gray-500 text-center">
                      Payment history will be displayed here once available
                    </p>
                  </div>
                </motion.div>
              </motion.div>

              {/* Financial Tips */}
              <motion.div
                className="bg-gradient-to-r from-indigo-500 to-blue-600 rounded-2xl p-6 text-white"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.4 }}>
                <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
                  <BarChart2 className="text-yellow-300" />
                  Financial Planning Tips
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {[
                    "Consider setting up an education fund for your child's future",
                    'Explore tax benefits available for education expenses',
                    'Review school fee payment plans for better budgeting'
                  ].map((tip, i) => (
                    <motion.div
                      key={i}
                      className="bg-white/10 p-4 rounded-lg backdrop-blur-sm"
                      whileHover={{ scale: 1.02 }}>
                      <p className="text-sm">{tip}</p>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </motion.div>
          )}

          {/* Children Tab */}
          {activeTab === 'children' && (
            <motion.div
              key="children"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="space-y-6">
              <motion.div
                className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100"
                variants={item}>
                <div className="flex items-center justify-between mb-6">
                  <h3 className="font-semibold text-lg flex items-center gap-2">
                    <GraduationCap className="text-purple-500" />
                    My Children
                  </h3>
                  <button className="px-4 py-2 bg-blue-100 text-blue-600 rounded-full text-sm font-medium hover:bg-blue-200 transition-colors">
                    + Add Child
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Child Card */}
                  <motion.div
                    className="border border-gray-200 rounded-xl p-4 hover:shadow-md transition-shadow"
                    whileHover={{ y: -5 }}>
                    <div className="flex items-center gap-4 mb-3">
                      <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center">
                        <Child className="text-purple-600" />
                      </div>
                      <div>
                        <h4 className="font-semibold">Student Name</h4>
                        <p className="text-sm text-gray-500">Grade 5</p>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-500">Performance</span>
                        <span className="font-medium text-green-600">Excellent</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-500">Attendance</span>
                        <span className="font-medium">95%</span>
                      </div>
                    </div>
                  </motion.div>

                  {/* Add Child Card */}
                  <motion.div
                    className="border-2 border-dashed border-gray-300 rounded-xl p-4 flex flex-col items-center justify-center min-h-[120px] cursor-pointer hover:bg-gray-50"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}>
                    <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mb-2">
                      <PlusCircle className="text-blue-600" />
                    </div>
                    <p className="text-sm text-gray-600">Add another child</p>
                  </motion.div>
                </div>
              </motion.div>

              {/* Upcoming Events */}
              <motion.div
                className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2 }}>
                <h3 className="font-semibold text-lg mb-4 flex items-center gap-2">
                  <Calendar className="text-red-500" />
                  Upcoming School Events
                </h3>
                <div className="space-y-3">
                  {[
                    { date: 'Jun 15', event: 'Parent-Teacher Meeting', time: '10:00 AM' },
                    { date: 'Jun 20', event: 'Annual Sports Day', time: '8:00 AM' },
                    { date: 'Jun 25', event: 'Science Fair', time: '9:30 AM' }
                  ].map((item, i) => (
                    <motion.div
                      key={i}
                      className="flex items-center gap-4 p-3 hover:bg-gray-50 rounded-lg"
                      whileHover={{ x: 5 }}>
                      <div className="w-12 h-12 rounded-lg bg-red-100 flex flex-col items-center justify-center">
                        <span className="text-xs text-red-600">{item.date}</span>
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium">{item.event}</h4>
                        <p className="text-sm text-gray-500">{item.time}</p>
                      </div>
                      <ChevronRight className="text-gray-400" />
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      )}
    </div>
  );
};

export default ParentDashboard;
